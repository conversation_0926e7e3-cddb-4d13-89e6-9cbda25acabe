name: Automate Release
on:
  push:
    branches:
      - main
jobs:
  #This job will bump tag number based on the (conventional) commit message and make a release.
  update-tag:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Conventional Changelog Action
        id: changelog
        uses: TriPSs/conventional-changelog-action@v3
        with:
          github-token: ${{ secrets.github_token }}
          version-file: './version.json'
          version-path: 'version'
          tag-prefix: ''
          git-user-name: 'GitHub Action'
          git-user-email: '<EMAIL>'
          git-message: 'chore(release): {version}'

      - name: Create Release
        uses: actions/create-release@v1
        if: ${{ steps.changelog.outputs.skipped == 'false' }}
        env:
          GITHUB_TOKEN: ${{ secrets.github_token }}
        with:
          tag_name: ${{ steps.changelog.outputs.tag }}
          release_name: ${{ steps.changelog.outputs.tag }}
          body: ${{ steps.changelog.outputs.clean_changelog }}
