name: Trivy Control Plane UI Diff Scan

on:
  pull_request:
    branches: [dev]

jobs:
  trivy-on-diff:
    runs-on: [self-hosted, linux, codebuild]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install Trivy
        uses: aquasecurity/setup-trivy@ff1b8b060f23b650436d419b5e13f67f5d4c3087
        with:
          version: v0.64.1
          cache: true
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Get current date
        id: date
        run: echo "date=$(date +'%Y-%m-%d')" >> "$GITHUB_OUTPUT"
        shell: bash

      - name: Restore Trivy cache
        uses: actions/cache@v4
        with:
          path: /trivy-cache
          key: cache-trivy-${{ steps.date.outputs.date }}
          restore-keys: |
            cache-trivy-

      - name: Set GitHub Action Path
        run: echo "${{ github.action_path }}" >> "$GITHUB_PATH"
        shell: bash

      - name: Get target branch hash
        id: target-branch-hash
        run: |
          git fetch origin ${{ github.event.pull_request.base.ref }}
          echo "target_branch_hash=$(git rev-parse origin/${{ github.event.pull_request.base.ref }})" >> "$GITHUB_OUTPUT"

      - name: Make scan-changes.sh executable
        run: chmod +x ./scan-changes.sh

      - name: Run Trivy scan on diff
        run: ./scan-changes.sh ${{ steps.target-branch-hash.outputs.target_branch_hash }}
