import {create} from '@storybook/theming/create';

export default create({
  base: 'light',
  brandTitle: 'React Boilerplate',
  brandUrl: 'https://sourcefuse.github.io/arc-docs/arc-ui-docs/arc-react/',
  brandImage: 'https://github.com/sourcefuse/react-boilerplate-ts-ui/blob/main/src/Assets/ARC_logo.png?raw=true',
  brandTarget: '_self',

  colorPrimary: '#0469E3',
  colorSecondary: '#E81823',

  barTextColor: '#9E9E9E',
  barSelectedColor: '#585C6D',
  barBg: '#ffffff',
});
