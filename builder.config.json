{"devCommand": "npm run start", "serverUrl": "http://localhost:3000", "commitMode": "commits", "isLocal": true, "allowedCommands": ["npm *", "pnpm *", "yarn *", "yarn", "git diff *", "git diff", "git log *", "git log", "git status *", "git status", "git add *", "git commit *", "git pull *", "git fetch *", "git merge *", "echo *", "cd *", "rm *", "find *", "ls *", "cp *", "mv *", "curl *", "npx tsc *", "head *", "lsof *", "kill *", "ps *", "awk *", "cat *", "grep *", "sed *", "tail *", "touch *", "mkdir *", "npx *", "env", "printenv *", "sleep *", "netlify *"], "command": "npm run start"}