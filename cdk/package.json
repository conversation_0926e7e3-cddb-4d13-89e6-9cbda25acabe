{"name": "cdk", "version": "1.0.0", "main": "main.js", "types": "main.ts", "license": "MPL-2.0", "private": true, "scripts": {"get": "cdktf get", "build": "tsc", "synth": "cdktf synth", "compile": "tsc --pretty", "watch": "tsc -w", "test": "jest", "test:watch": "jest --watch", "upgrade": "npm i cdktf@latest cdktf-cli@latest", "upgrade:next": "npm i cdktf@next cdktf-cli@next"}, "engines": {"node": ">=14.0"}, "dependencies": {"@cdktf/provider-aws": "^13.0.0", "cdktf": "^0.15.5", "constructs": "^10.2.26", "dotenv": "^16.0.3", "uniqid": "^5.4.0"}, "devDependencies": {"@types/jest": "^29.5.1", "@types/mime-types": "^2.1.1", "@types/node": "^20.1.7", "@types/uniqid": "^5.3.2", "jest": "^29.5.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typescript": "^5.0.4"}}