{"name": "distek-saas-control-plane", "version": "0.0.1", "private": true, "type": "module", "scripts": {"config": "node configGenerator.js --templateFileName=config.template.json --outConfigPath=./public", "start": "npm run config && vite", "build": "tsc && vite build", "test": "vitest watch", "test:no-watch": "vitest run", "test:coverage": "vitest run --coverage", "preview": "vite preview", "lint": "eslint --ext .js,.jsx,.ts,.tsx ./src/", "format": "prettier --write \"**/*.+(js|jsx|ts|tsx|json|css|md)\"", "lint:fix": "eslint --fix  --ext .js,.jsx,.ts,.tsx ./src/", "test:all": "cross-env set CI=true && react-scripts test && npm run lint", "prepare": "husky install", "pre-commit": "lint-staged", "coverage": "cross-env CI=true npm test -- --env=jsdom --coverage", "cypress": "cypress open", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/inter": "^5.2.6", "@mui/icons-material": "^7.2.0", "@mui/lab": "^7.0.0-beta.14", "@mui/material": "^7.2.0", "@mui/types": "^7.4.4", "@mui/x-date-pickers": "^8.7.0", "@reduxjs/toolkit": "^2.8.2", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-table": "^8.21.3", "@vitest/coverage-v8": "^3.2.4", "babel": "^6.23.0", "c8": "^10.1.3", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^17.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-storybook": "^9.0.15", "formik": "^2.4.6", "install": "^0.13.0", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "notistack": "^3.0.2", "npm": "^11.4.2", "react": "19.1.0", "react-country-state-city": "^1.1.12", "react-dom": "19.1.0", "react-dropzone": "^14.3.8", "react-idle-timer": "^5.7.2", "react-redux": "^9.2.0", "react-router": "^7.6.3", "react-router-dom": "^7.6.3", "react-syntax-highlighter": "^15.6.1", "storybook": "^9.0.15", "tinycolor2": "^1.6.0", "use-react-router-breadcrumbs": "^4.0.1", "yup": "^1.6.1", "recharts": "^2.15.4"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@eslint/compat": "^1.3.1", "@eslint/eslintrc": "^3.3.1", "@eslint/js": "^9.30.1", "@testing-library/cypress": "^10.0.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/lodash": "^4.17.20", "@types/node": "^20.14.8", "@types/react": "19.1.8", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-copy-to-clipboard": "^5.0.7", "@types/react-dom": "19.1.6", "@types/react-syntax-highlighter": "^15.5.13", "@types/storybook__addon-actions": "^3.4.3", "@types/storybook__addon-links": "^3.3.5", "@types/storybook__react": "^4.0.2", "@types/tinycolor2": "^1.4.6", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-legacy": "^7.0.1", "@vitejs/plugin-react": "^4.6.0", "commitizen": "^4.3.1", "cross-env": "^7.0.3", "cz-conventional-changelog": "^3.3.0", "cz-customizable": "^7.4.0", "eslint": "^9.19.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-cypress": "^4.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-n": "^17.15.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.2.3", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-sonarjs": "^3.0.1", "eslint-plugin-testing-library": "^7.1.1", "globals": "^16.3.0", "husky": "^9.1.7", "jsdom": "^26.1.0", "lint-staged": "^16.1.2", "prettier": "^3.6.2", "prop-types": "^15.8.1", "typescript": "^5.8.3", "vite": "^7.0.1", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.4", "vitest": "^3.2.4"}, "lint-staged": {"*.+(js|jsx|ts|tsx)": "eslint --fix", "*.+(json|css|md|less|scss|html|jsx|js|ts|tsx)": "prettier --write"}, "config": {"commitizen": {"path": "node_modules/cz-customizable"}, "cz-customizable": {"config": "./.cz-config.cjs"}}}