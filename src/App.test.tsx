// src/App.test.tsx
import {render, screen, waitFor} from '@testing-library/react';
import App from './App';

vi.mock('Components/BackdropLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="backdrop-loader" />,
}));
vi.mock('Components/SessionTimeout/SessionTimeout', () => ({
  __esModule: true,
  default: (props: any) => <div data-testid="session-timeout">{JSON.stringify(props)}</div>,
}));
vi.mock('Routes/Routes', () => ({
  __esModule: true,
  default: (props: any) => <div data-testid="app-routes">{JSON.stringify(props)}</div>,
}));
vi.mock('Routes/layoutRouteConfig', () => ({
  getRouteConfig: () => ({route: 'mock'}),
}));
vi.mock('Hooks/useConfig', () => ({
  __esModule: true,
  default: () => ({
    config: {
      enableSessionTimeout: true,
      expiryTimeInMinute: 15,
      promptTimeBeforeIdleInMinute: 5,
    },
  }),
}));
vi.mock('Hooks/useAuth', () => ({
  __esModule: true,
  default: () => ({
    isLoggedIn: true,
  }),
}));
vi.mock('redux/hooks', () => ({
  useAppDispatch: () => () => ({
    unwrap: () => Promise.resolve({}),
  }),
}));
vi.mock('redux/config/configThunk', () => ({
  fetchConfigData: () => ({
    unwrap: () => Promise.resolve({}),
  }),
}));

describe('App', () => {
  it('shows loader while config is loading', async () => {
    render(<App />);
    expect(screen.getByTestId('backdrop-loader')).toBeInTheDocument();
    await waitFor(() => expect(screen.getByTestId('app-routes')).toBeInTheDocument());
  });

  it('renders routes after config loads', async () => {
    render(<App />);
    await waitFor(() => expect(screen.getByTestId('app-routes')).toBeInTheDocument());
  });

  it('renders SessionTimeout when enabled and logged in', async () => {
    render(<App />);
    await waitFor(() => expect(screen.getByTestId('session-timeout')).toBeInTheDocument());
  });

  it('does not render SessionTimeout when disabled', async () => {
    vi.doMock('Hooks/useConfig', () => ({
      __esModule: true,
      default: () => ({
        config: {
          enableSessionTimeout: false,
          expiryTimeInMinute: 15,
          promptTimeBeforeIdleInMinute: 5,
        },
      }),
    }));
    render(<App />);
    await waitFor(() => expect(screen.queryByTestId('session-timeout')).not.toBeInTheDocument());
  });

  it('does not render SessionTimeout when not logged in', async () => {
    vi.doMock('Hooks/useAuth', () => ({
      __esModule: true,
      default: () => ({
        isLoggedIn: false,
      }),
    }));
    render(<App />);
    await waitFor(() => expect(screen.queryByTestId('session-timeout')).not.toBeInTheDocument());
  });
});
