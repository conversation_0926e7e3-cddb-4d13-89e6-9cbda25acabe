import BackdropLoader from 'Components/BackdropLoader';
import SessionTimeout from 'Components/SessionTimeout/SessionTimeout';
import useAuth from 'Hooks/useAuth';
import useConfig from 'Hooks/useConfig';
import AppRoutes from 'Routes/Routes';
import {getRouteConfig} from 'Routes/layoutRouteConfig';
import {useEffect, useState} from 'react';
import {fetchConfigData} from 'redux/config/configThunk';
import {useAppDispatch} from 'redux/hooks';

const configSettlementTimeout = 500;

function App() {
  const dispatch = useAppDispatch();
  const {enableSessionTimeout, expiryTimeInMinute, promptTimeBeforeIdleInMinute} = useConfig().config;
  const [isConfigLoading, setIsConfigLoading] = useState(true);
  const {isLoggedIn} = useAuth();

  useEffect(() => {
    setIsConfigLoading(true);
    let timer: NodeJS.Timeout | undefined;
    const dispatchThunk = async () => {
      await dispatch(fetchConfigData()).unwrap();
      timer = setTimeout(() => {
        setIsConfigLoading(false);
      }, configSettlementTimeout);
    };
    dispatchThunk();
    return () => {
      if (timer) {
        clearTimeout(timer);
      }
    };
  }, [dispatch]);

  if (isConfigLoading) {
    return <BackdropLoader />;
  }

  return (
    <>
      <AppRoutes routesConfig={getRouteConfig()} />
      {enableSessionTimeout && isLoggedIn ? (
        <SessionTimeout
          expiryTimeInMinute={expiryTimeInMinute}
          promptTimeBeforeIdleInMinute={promptTimeBeforeIdleInMinute}
        />
      ) : null}
    </>
  );
}

export default App;
