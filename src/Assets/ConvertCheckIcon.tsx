import {SvgIcon, SvgIconProps} from '@mui/material';

const ConvertCheckIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 14 14">
      <path
        d="M12.6667 3L5.33333 10.3333L2 7"
        stroke={'currentColor'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </SvgIcon>
  );
};

export default ConvertCheckIcon;
