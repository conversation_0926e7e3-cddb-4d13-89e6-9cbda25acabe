import {SvgIcon, SvgIconProps} from '@mui/material';

const DisabledRemoveIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 38 38">
      <rect x="0.5" y="0.5" width="37" height="37" rx="18.5" fill="white" />
      <rect x="0.5" y="0.5" width="37" height="37" rx="18.5" stroke="#DBDBDB" />
      <path d="M14.3334 19H23.6667" stroke="#BABABA" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
    </SvgIcon>
  );
};

export default DisabledRemoveIcon;
