import {Box} from '@mui/material';
import React from 'react';
import distekLogoPng from './distek-logo-primary.png';

interface DistekLogoProps {
  sx?: object;
  alt?: string;
}

const DistekLogo: React.FC<DistekLogoProps> = ({sx = {}, alt = 'DISTEK Logo'}) => {
  return (
    <Box
      sx={{
        display: 'inline-block',
        ...sx,
      }}
    >
      <img
        src={distekLogoPng}
        alt={alt}
        style={{
          height: 'auto',
          maxWidth: '25%',
          maxHeight: '25%',
          width: 'auto',
          display: 'block',
          margin: '0 auto',
        }}
      />
    </Box>
  );
};

export default DistekLogo;
