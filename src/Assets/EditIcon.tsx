import {SvgIcon, SvgIconProps} from '@mui/material';

const EditIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 16 15">
      <path
        d="M11.9999 6.09591L9.33319 3.42924M1.6665 13.7626L3.92275 13.5119C4.19841 13.4813 4.33624 13.4659 4.46507 13.4242C4.57936 13.3872 4.68814 13.335 4.78843 13.2688C4.90147 13.1943 4.99953 13.0962 5.19565 12.9001L13.9999 4.09591C14.7362 3.35953 14.7362 2.16562 13.9999 1.42924C13.2635 0.692859 12.0696 0.692858 11.3332 1.42924L2.52899 10.2334C2.33287 10.4295 2.23481 10.5276 2.16026 10.6406C2.09413 10.7409 2.04185 10.8497 2.00485 10.964C1.96314 11.0928 1.94783 11.2307 1.9172 11.5063L1.6665 13.7626Z"
        stroke="currentColor"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </SvgIcon>
  );
};

export default EditIcon;
