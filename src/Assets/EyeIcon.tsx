import {SvgIcon, SvgIconProps} from '@mui/material';

const EyeIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 24 24">
      <path
        d="M12 5C16 5 20 8.5 21 12C20 15.5 16 19 12 19C8 19 4 15.5 3 12C4 8.5 8 5 12 5Z"
        stroke="currentColor"
        strokeWidth="2"
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle
        cx="12"
        cy="12"
        r="3"
        stroke="currentColor"
        strokeWidth="2"
        fill="none"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </SvgIcon>
  );
};

export default EyeIcon;
