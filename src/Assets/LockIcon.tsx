import {SvgIcon, SvgIconProps} from '@mui/material';

const LockIconAlt = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 14 16">
      <path
        d="M10.75 6.5V5C10.75 2.92893 9.07107 1.25 7 1.25C4.92893 1.25 3.25 2.92893 3.25 5V6.5M7 9.875V11.375M4.6 14.75H9.4C10.6601 14.75 11.2902 14.75 11.7715 14.5048C12.1948 14.289 12.539 13.9448 12.7548 13.5215C13 13.0402 13 12.4101 13 11.15V10.1C13 8.83988 13 8.20982 12.7548 7.72852C12.539 7.30516 12.1948 6.96095 11.7715 6.74524C11.2902 6.5 10.6601 6.5 9.4 6.5H4.6C3.33988 6.5 2.70982 6.5 2.22852 6.74524C1.80516 6.96095 1.46095 7.30516 1.24524 7.72852C1 8.20982 1 8.83988 1 10.1V11.15C1 12.4101 1 13.0402 1.24524 13.5215C1.46095 13.9448 1.80516 14.289 2.22852 14.5048C2.70982 14.75 3.33988 14.75 4.6 14.75Z"
        stroke="currentColor"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </SvgIcon>
  );
};

export default LockIconAlt;
