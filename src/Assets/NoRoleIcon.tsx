import {SvgIcon, SvgIconProps} from '@mui/material';
import {FC} from 'react';

/**
 * NoRoleIcon component
 * @param props - SVG icon properties
 * @returns JSX.Element
 */
const NoRoleIcon: FC<SvgIconProps> = props => {
  return (
    <SvgIcon {...props} viewBox="0 0 127 83.829">
      <path
        opacity={0.7}
        d="M63.5 83.829C98.5701 83.829 127 81.1198 127 77.7778C127 74.4358 98.5701 71.7266 63.5 71.7266C28.4299 71.7266 0 74.4358 0 77.7778C0 81.1198 28.4299 83.829 63.5 83.829Z"
        fill="#852352"
      />
      <path
        opacity={0.4}
        d="M106.804 33.4512H20.2339L21.6332 74.902C21.7089 76.6796 23.146 78.0789 24.8858 78.0789H102.114C103.892 78.0789 105.329 76.6796 105.367 74.902L106.804 33.4512Z"
        fill="#CBA0AF"
      />
      <path
        d="M106.804 31.9375L106.199 49.3348L105.367 73.4262C105.291 75.2037 103.854 76.5653 102.114 76.5653H24.8857C23.1082 76.5653 21.671 75.1659 21.6332 73.4262L20.8012 49.3348L20.196 31.9375H106.804Z"
        fill="white"
        stroke="#CBA0AF"
        strokeWidth={1.57764}
        strokeMiterlimit={10}
        strokeLinejoin="round"
      />
      <path opacity={0.5} d="M106.804 31.9375L106.199 49.3348H20.8012L20.2339 31.9375H106.804Z" fill="#CBA0AF" />
      <path
        d="M75.7536 21.6503H51.2461C47.7288 21.6503 44.8923 18.8138 44.8923 15.2965V7.42995C44.8923 3.91268 47.7288 1.07617 51.2461 1.07617H75.7536C79.2708 1.07617 82.1073 3.91268 82.1073 7.42995V15.2965C82.1073 18.8138 79.2708 21.6503 75.7536 21.6503ZM51.2461 5.61459C50.2628 5.61459 49.4307 6.44663 49.4307 7.42995V15.2965C49.4307 16.2799 50.2628 17.1119 51.2461 17.1119H75.7536C76.7369 17.1119 77.5689 16.2799 77.5689 15.2965V7.42995C77.5689 6.44663 76.7369 5.61459 75.7536 5.61459H51.2461Z"
        fill="#CBA0AF"
        stroke="#852352"
        strokeWidth={1.57764}
        strokeMiterlimit={10}
        strokeLinejoin="round"
      />
      <path
        d="M106.615 45.7813H20.385C18.0402 45.7813 16.2626 43.7391 16.5274 41.432L19.4773 16.3195C19.6664 14.731 20.9902 13.5586 22.5786 13.5586H104.384C105.972 13.5586 107.334 14.7688 107.485 16.3195L110.435 41.432C110.737 43.7391 108.96 45.7813 106.615 45.7813Z"
        fill="white"
        stroke="#CBA0AF"
        strokeWidth={1.57764}
        strokeMiterlimit={10}
        strokeLinejoin="round"
      />
      <path
        d="M43.4173 52.7017H35.0969C34.3405 52.7017 33.7354 52.0966 33.7354 51.3402V43.0197C33.7354 42.2633 34.3405 41.6582 35.0969 41.6582H43.4173C44.1737 41.6582 44.7788 42.2633 44.7788 43.0197V51.3402C44.7788 52.0966 44.1737 52.7017 43.4173 52.7017ZM36.4584 49.9408H42.0558V44.3434H36.4584V49.9408Z"
        fill="#CBA0AF"
        stroke="#852352"
        strokeWidth={1.57764}
        strokeMiterlimit={10}
        strokeLinejoin="round"
      />
      <path
        d="M91.9029 52.7017H83.5825C82.8261 52.7017 82.2209 52.0966 82.2209 51.3402V43.0197C82.2209 42.2633 82.8261 41.6582 83.5825 41.6582H91.9029C92.6593 41.6582 93.2644 42.2633 93.2644 43.0197V51.3402C93.2644 52.0966 92.6593 52.7017 91.9029 52.7017ZM84.9818 49.9408H90.5792V44.3434H84.9818V49.9408Z"
        fill="#CBA0AF"
        stroke="#852352"
        strokeWidth={1.57764}
        strokeMiterlimit={10}
        strokeLinejoin="round"
      />
    </SvgIcon>
  );
};

export default NoRoleIcon;
