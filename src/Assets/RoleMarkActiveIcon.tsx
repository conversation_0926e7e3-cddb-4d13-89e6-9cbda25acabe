import {SvgIcon, SvgIconProps} from '@mui/material';

/**
 * RoleMarkActiveIcon component
 * @param props - SVG icon properties
 * @returns SVG icon element
 */
const RoleMarkActiveIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 18 18">
      <path
        d="M14.1663 7.55715V8.17048C14.1655 9.6081 13.7 11.0069 12.8392 12.1584C11.9785 13.3098 10.7685 14.1521 9.38991 14.5597C8.0113 14.9674 6.53785 14.9184 5.18932 14.4202C3.8408 13.922 2.68944 13.0012 1.90698 11.7952C1.12452 10.5892 0.752869 9.16253 0.847459 7.72803C0.942049 6.29353 1.49781 4.92803 2.43186 3.8352C3.36591 2.74236 4.6282 1.98074 6.03047 1.66391C7.43274 1.34709 8.89985 1.49204 10.213 2.07715M14.1663 2.83333L7.49967 9.50667L5.49967 7.50667"
        stroke="#6B6B6B"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </SvgIcon>
  );
};
export default RoleMarkActiveIcon;
