import {SvgIcon, SvgIconProps} from '@mui/material';

const ValidationRightBgIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 16 16">
      <rect x="0.5" y="0.5" width="15" height="15" rx="7.5" fill="currentColor" stroke="currentColor" />
      <path
        d="M11.3337 5.5L6.75033 10.0833L4.66699 8"
        stroke="#0D653B"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </SvgIcon>
  );
};

export default ValidationRightBgIcon;
