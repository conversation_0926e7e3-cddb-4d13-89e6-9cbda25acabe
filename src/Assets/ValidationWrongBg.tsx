import {SvgIcon, SvgIconProps} from '@mui/material';

const ValidationWrongBgIcon = (props: SvgIconProps) => {
  return (
    <SvgIcon {...props} viewBox="0 0 16 16">
      <rect width="16" height="16" rx="8" fill="currentColor" />
      <path
        d="M10.0834 5.9165L5.91675 10.0832M5.91675 5.9165L10.0834 10.0832"
        stroke="#A91417"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
        fill="none"
      />
    </SvgIcon>
  );
};

export default ValidationWrongBgIcon;
