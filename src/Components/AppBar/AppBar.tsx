import MenuIcon from '@mui/icons-material/Menu';
import {Box, Toolbar} from '@mui/material';
import MuiAppBar from '@mui/material/AppBar';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import {styled} from '@mui/material/styles';
import DistekLogoPng from 'Assets/distek-logo-primary.png';
import {LogoutDialog} from 'Components/LogoutDialog/LogoutDialog';
import useAuth from 'Hooks/useAuth';
import {memo, useState} from 'react';
import BackdropLoader from '../BackdropLoader/BackdropLoader';
import {AppBarUserProfile} from './AppBarUserProfile';

const MyAppBar = styled(MuiAppBar)(({theme}) => ({
  zIndex: theme.zIndex.drawer + 1,
  boxShadow: 'none',
  backgroundColor: theme.palette.background.default,
  color: theme.palette.text.primary,
}));

interface IAppBarProps {
  open: boolean;
  isPermanent: boolean;
  toggleDrawer: () => void;
  userName?: string;
}

const MenuButton = ({open, toggleDrawer}: Partial<IAppBarProps>) => (
  <Tooltip title="Menu" sx={{mr: 1}}>
    <IconButton onClick={toggleDrawer}>
      <MenuIcon sx={{...(open && {color: 'secondary.main'})}} />
    </IconButton>
  </Tooltip>
);

const AppBar = ({open, toggleDrawer, isPermanent}: Partial<IAppBarProps>) => {
  const APP_BAR_MARGIN = 270;
  let appBarMargin = open ? APP_BAR_MARGIN : 0;
  appBarMargin = isPermanent ? appBarMargin : 0;

  const {logout, logoutLoading} = useAuth();
  const [logoutDialogOpen, setLogoutDialogOpen] = useState(false);

  return (
    <>
      {logoutLoading ? (
        <BackdropLoader />
      ) : (
        <MyAppBar
          sx={theme => ({
            width: `100%`,
            transition: theme.transitions.create('width', {
              easing: theme.transitions.easing.sharp,
              duration: theme.transitions.duration.leavingScreen,
            }),
            ...(appBarMargin && {
              transition: theme.transitions.create('width', {
                easing: theme.transitions.easing.easeOut,
                duration: theme.transitions.duration.enteringScreen,
              }),
            }),
          })}
        >
          <Toolbar
            disableGutters
            sx={{
              borderBottom: theme => `0.0625rem solid ${theme.palette.divider}`,
              display: 'flex',
              alignItems: 'center',
              justifyItems: 'center',
              px: 2,
            }}
          >
            <Box
              sx={{
                display: {
                  xs: 'flex',
                  md: 'none',
                },
              }}
            >
              <MenuButton open={open} toggleDrawer={toggleDrawer} />
            </Box>
            <Box sx={{height: 17}} component={'img'} src={DistekLogoPng} alt="Distek Logo" />

            <Box sx={{display: 'flex', alignItems: 'center', ml: 'auto'}}>
              <AppBarUserProfile
                onLogout={() => {
                  setLogoutDialogOpen(true);
                }}
              />
            </Box>
          </Toolbar>
          <LogoutDialog open={logoutDialogOpen} onClose={() => setLogoutDialogOpen(false)} onLogout={logout} />
        </MyAppBar>
      )}
    </>
  );
};

export default memo(AppBar);
