import {Box, MenuItem} from '@mui/material';
import ArrowDown from 'Assets/arrow-down.svg';
import LogoutIcon from 'Assets/LogoutIcon';
import UserProfileImage from 'Assets/user-profile-icon.svg';
import ToolTipTypography from 'Components/ToolTipTypography/ToolTipTypography';
import {getFullName} from 'Helpers/utils';
import React, {useRef, useState} from 'react';
import {useGetUserQuery} from 'redux/auth/authApiSlice';
import {PopoverWithArrow} from './PopoverWithArrow';

export const AppBarUserProfile = ({onLogout}: {onLogout: () => void}) => {
  const imageRef = useRef<HTMLImageElement>(null);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const menuOpen = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = () => {
    handleClose();
    onLogout();
  };

  const {data: userData} = useGetUserQuery();

  const userName = userData ? getFullName(userData) : 'User';
  const email = userData ? userData.email : undefined;

  const drawTextSection = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'flex-start',
          justifyContent: 'center',
          px: 0.5,
          textTransform: 'capitalize',
          ml: 0.75,
        }}
      >
        <ToolTipTypography
          message={userName ?? ''}
          disableInteraction
          sx={{
            fontSize: '0.875rem',
            fontWeight: 700,
          }}
        />
        {email && (
          <ToolTipTypography
            message={email}
            sx={{
              marginTop: -0.5,
              textTransform: 'lowercase',
              fontSize: '0.75rem',
              fontWeight: 'semi-bold',
              color: 'body.400',
            }}
          />
        )}
      </Box>
    );
  };

  const drawImageSection = () => {
    return (
      <Box
        ref={imageRef}
        sx={{
          height: 36,
          alignContent: 'center',
          justifyContent: 'center',
          display: 'flex',
          aspectRatio: 1,
          backgroundColor: '#F7ECF1',
          borderRadius: '50%',
        }}
      >
        <Box component={'img'} src={UserProfileImage} alt="User Profile" sx={{width: 16, aspectRatio: 1}} />
      </Box>
    );
  };

  return (
    <>
      <Box
        data-testid="menu-button"
        id="menu-button"
        aria-controls={menuOpen ? 'menu' : undefined}
        aria-haspopup="true"
        aria-expanded={menuOpen ? 'true' : undefined}
        onClick={handleClick}
        sx={{
          height: 40,
          minHeight: 40,
          minWidth: 40,
          display: 'flex',
          flexDirection: 'row',
          alignItems: 'center',
          justifyItems: 'center',
          cursor: 'pointer',
          gap: 0,
        }}
      >
        {drawImageSection()}
        {drawTextSection()}
        <Box component={'img'} src={ArrowDown} alt="Arrow Down" sx={{width: 16, aspectRatio: 1, ml: 1.5}} />
      </Box>
      <PopoverWithArrow
        open={menuOpen}
        onClose={handleClose}
        anchorEl={imageRef.current}
        transformHOrigin={{horizontal: 'left'}}
      >
        <MenuItem sx={{width: 215}} onClick={handleLogout}>
          <Box sx={{display: 'flex', flexDirection: 'row', alignItems: 'center'}}>
            <LogoutIcon sx={{color: 'body.500', fill: 'transparent', height: 14}} />
            Logout
          </Box>
        </MenuItem>
      </PopoverWithArrow>
    </>
  );
};
