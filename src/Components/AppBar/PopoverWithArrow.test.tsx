import {render, screen} from '@testing-library/react';
import {PopoverWithArrow} from './PopoverWithArrow';

describe('PopoverWithArrow', () => {
  const anchorEl = document.createElement('div');
  document.body.appendChild(anchorEl);

  it('renders children inside the Popover', () => {
    render(
      <PopoverWithArrow open={true} anchorEl={anchorEl} onClose={() => {}}>
        <div data-testid="popover-content">Popover Content</div>
      </PopoverWithArrow>,
    );
    expect(screen.getByTestId('popover-content')).toBeInTheDocument();
  });

  it('passes additional props to Popover', () => {
    render(
      <PopoverWithArrow open={true} anchorEl={anchorEl} onClose={() => {}} data-testid="popover">
        Test
      </PopoverWithArrow>,
    );
    expect(screen.getByTestId('popover')).toBeInTheDocument();
  });
});
