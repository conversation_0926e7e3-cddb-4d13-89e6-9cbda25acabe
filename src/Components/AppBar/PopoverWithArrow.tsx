import {Box, Popover, type PopoverProps} from '@mui/material';
import {useEffect, useRef, useState} from 'react';

const arrowWidth = 15; // Width of the arrow in pixels

type PopoverWithArrowProps = Omit<PopoverProps, 'anchorOrigin' | 'transformOrigin'> & {
  transformHOrigin?: Omit<PopoverProps['transformOrigin'], 'vertical'>;
};

/**
 * PopoverWithArrow component that displays a popover with an arrow pointing to the anchor element.
 * The arrow is positioned based on the width of the popover content and the anchor element.
 * @param popoverProps The props for the popover, including anchorEl, open, and children.
 * @returns {JSX.Element} The rendered PopoverWithArrow component.
 */
const PopoverArrayConfigurationTimeout = 200; // Timeout in milliseconds to wait before calculating the arrow position
export const PopoverWithArrow = (popoverProps: PopoverWithArrowProps) => {
  const anchorEl = popoverProps.anchorEl;
  const ref = useRef<HTMLDivElement>(null);
  const [arrowOffset, setArrowOffset] = useState(0);
  const [didSet, setDidSet] = useState(false);

  useEffect(() => {
    // Ensure that the anchor element is defined before calculating the arrow position
    const timer = setTimeout(() => {
      const anchorRect = (anchorEl as HTMLElement)?.getBoundingClientRect();
      const contentRect = ref.current?.getBoundingClientRect();
      if (!anchorRect || !contentRect) return;
      const centerOfAnchor = anchorRect.left + anchorRect.width / 2;
      const centerOfContent = contentRect.left + contentRect.width / 2;
      const offset = centerOfAnchor - centerOfContent;
      setArrowOffset(contentRect.width / 2 + offset - arrowWidth / 2); // 7.5 is half of the arrow width (15px)
      setDidSet(true);
    }, PopoverArrayConfigurationTimeout);
    return () => {
      clearTimeout(timer);
      setDidSet(false);
    };
  }, [popoverProps.open]);
  return (
    <Popover
      anchorOrigin={{horizontal: 'center', vertical: 'bottom', ...popoverProps.transformHOrigin}}
      slotProps={{
        paper: {
          sx: {
            overflow: 'visible',
            '&:before': {
              content: '""',
              display: didSet ? 'block' : 'none',
              position: 'absolute',
              transition: 'opacity 0.2s ease-in-out',
              top: 0,
              left: arrowOffset,
              width: arrowWidth,
              height: arrowWidth,
              backgroundColor: 'inherit',
              transform: 'translateY(-50%) rotate(45deg)',
              boxShadow: '-0.1875rem -0.1875rem 0.3125rem -0.125rem rgba(0,0,0,0.1)',
            },
          },
        },
      }}
      {...popoverProps}
      transformOrigin={{horizontal: 'center', vertical: -15, ...popoverProps.transformHOrigin}}
    >
      <Box ref={ref} sx={{overflow: 'hidden', p: 0.5}}>
        {popoverProps.children}
      </Box>
    </Popover>
  );
};
