// @vitest-environment jsdom
import '@testing-library/jest-dom';
import {fireEvent, screen} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import {BaseDialogLayout, BaseDialogLayoutProps} from './BaseDialogLayout';

// Mock dependencies
const mockOnClose = vi.fn();
const mockOnConfirm = vi.fn();

// Mock child components
vi.mock('./DefaultDialog/DefaultDialog', () => ({
  DefaultDialog: vi.fn().mockImplementation(({title, children, open, onClose, maxWidth}) =>
    open ? (
      <div data-testid="default-dialog" data-max-width={maxWidth}>
        <div data-testid="dialog-title">{title}</div>
        <button data-testid="dialog-close" onClick={onClose}>
          ×
        </button>
        <div data-testid="dialog-content">{children}</div>
      </div>
    ) : null,
  ),
}));

vi.mock('./BlueButton/BlueButton', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({children, onClick, isLoading, ...props}) => (
    <button data-testid="blue-button" onClick={onClick} disabled={isLoading} {...props}>
      {children}
      {isLoading && <span data-testid="loading-spinner">Loading...</span>}
    </button>
  )),
}));

vi.mock('./BorderButton/BorderButton', () => ({
  __esModule: true,
  default: vi.fn().mockImplementation(({children, onClick, ...props}) => (
    <button data-testid="border-button" onClick={onClick} {...props}>
      {children}
    </button>
  )),
}));

// Mock MUI components
vi.mock('@mui/material', async () => {
  const actual = await vi.importActual('@mui/material');
  return {
    ...actual,
    Box: ({children, sx, ...props}: any) => (
      <div data-testid="box" {...props}>
        {children}
      </div>
    ),
    Typography: ({children, sx, ...props}: any) => (
      <div data-testid="typography" {...props}>
        {children}
      </div>
    ),
    styled: (component: any) => (styles: any) => component,
  };
});

const defaultProps: BaseDialogLayoutProps = {
  open: true,
  onClose: mockOnClose,
  onConfirm: mockOnConfirm,
  title: 'Test Dialog',
  confirmLabel: 'Confirm',
};

const TestIcon = () => <div data-testid="test-icon">🔥</div>;

describe('BaseDialogLayout', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the dialog when open is true', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    expect(screen.getByTestId('default-dialog')).toBeInTheDocument();
    expect(screen.getByTestId('dialog-title')).toHaveTextContent('Test Dialog');
  });

  it('does not render when open is false', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} open={false} />);

    expect(screen.queryByTestId('default-dialog')).not.toBeInTheDocument();
  });

  it('renders with required props only', () => {
    const minimalProps = {
      open: true,
      onClose: mockOnClose,
      onConfirm: mockOnConfirm,
      title: 'Minimal Dialog',
    };

    renderWithTheme(<BaseDialogLayout {...minimalProps} />);

    expect(screen.getByTestId('dialog-title')).toHaveTextContent('Minimal Dialog');
    expect(screen.getByTestId('border-button')).toHaveTextContent('Cancel'); // default cancelLabel
    expect(screen.queryByTestId('dialog-confirm-button')).not.toBeInTheDocument(); // no confirmLabel
  });

  it('renders confirm button when confirmLabel is provided', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    expect(screen.getByTestId('dialog-confirm-button')).toBeInTheDocument();
    expect(screen.getByTestId('dialog-confirm-button')).toHaveTextContent('Confirm');
  });

  it('renders custom cancel label', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} cancelLabel="Close" />);

    expect(screen.getByTestId('border-button')).toHaveTextContent('Close');
  });

  it('renders message when provided', () => {
    const message = 'Are you sure you want to proceed?';
    renderWithTheme(<BaseDialogLayout {...defaultProps} message={message} />);

    expect(screen.getByText(message)).toBeInTheDocument();
  });

  it('renders highlight text when provided', () => {
    const highlightText = 'Important Item';
    renderWithTheme(<BaseDialogLayout {...defaultProps} highlightText={highlightText} />);

    expect(screen.getByText(highlightText)).toBeInTheDocument();
  });

  it('renders icon when provided', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} icon={<TestIcon />} />);

    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
  });

  it('renders caption when provided', () => {
    const caption = <div data-testid="custom-caption">Custom Caption</div>;
    renderWithTheme(<BaseDialogLayout {...defaultProps} caption={caption} />);

    expect(screen.getByTestId('custom-caption')).toBeInTheDocument();
  });

  it('calls onClose when cancel button is clicked', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    fireEvent.click(screen.getByTestId('border-button'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onConfirm when confirm button is clicked', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    fireEvent.click(screen.getByTestId('dialog-confirm-button'));
    expect(mockOnConfirm).toHaveBeenCalledTimes(1);
  });

  it('calls onClose when dialog close button is clicked', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    fireEvent.click(screen.getByTestId('dialog-close'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('shows loading state on confirm button', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} isLoading={true} />);

    const confirmButton = screen.getByTestId('dialog-confirm-button');
    expect(confirmButton).toBeDisabled();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });

  it('passes custom maxWidth to DefaultDialog', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} maxWidth={600} />);

    expect(screen.getByTestId('default-dialog')).toHaveAttribute('data-max-width', '600');
  });

  it('uses default maxWidth when not provided', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    expect(screen.getByTestId('default-dialog')).toHaveAttribute('data-max-width', '400');
  });

  it('renders all optional elements together', () => {
    const allPropsData = {
      ...defaultProps,
      message: 'Confirmation message',
      highlightText: 'Highlighted item',
      icon: <TestIcon />,
      caption: <div data-testid="caption">Caption text</div>,
      cancelLabel: 'No',
      confirmLabel: 'Yes',
      isLoading: false,
      maxWidth: 500,
      iconBGColor: '#00ff00',
    };

    renderWithTheme(<BaseDialogLayout {...allPropsData} />);

    expect(screen.getByText('Confirmation message')).toBeInTheDocument();
    expect(screen.getByText('Highlighted item')).toBeInTheDocument();
    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
    expect(screen.getByTestId('caption')).toBeInTheDocument();
    expect(screen.getByTestId('border-button')).toHaveTextContent('No');
    expect(screen.getByTestId('dialog-yes-button')).toHaveTextContent('Yes');
  });

  it('does not render confirm button when confirmLabel is not provided', () => {
    const propsWithoutConfirm = {
      ...defaultProps,
      confirmLabel: undefined,
    };

    renderWithTheme(<BaseDialogLayout {...propsWithoutConfirm} />);

    expect(screen.getByTestId('border-button')).toBeInTheDocument();
    expect(screen.queryByTestId('blue-button')).not.toBeInTheDocument();
  });

  it('does not render message when not provided', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    // Should not have any typography elements for message
    const typographyElements = screen.queryAllByTestId('typography');
    expect(typographyElements).toHaveLength(0);
  });

  it('does not render highlight text when not provided', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    // Should not have highlight text styling
    expect(screen.queryByText(/highlighted/i)).not.toBeInTheDocument();
  });

  it('does not render icon when not provided', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    expect(screen.queryByTestId('test-icon')).not.toBeInTheDocument();
  });

  it('does not render caption when not provided', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    expect(screen.queryByTestId('custom-caption')).not.toBeInTheDocument();
  });

  it('handles empty string confirmLabel', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} confirmLabel="" />);

    expect(screen.queryByTestId('blue-button')).not.toBeInTheDocument();
  });

  it('generates correct test id for confirm button', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} confirmLabel="Delete" />);

    const confirmButton = screen.getByTestId('dialog-delete-button');
    expect(confirmButton).toHaveAttribute('data-testid', 'dialog-delete-button');
  });

  it('handles complex confirm label for test id generation', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} confirmLabel="Save Changes" />);

    const confirmButton = screen.getByTestId('dialog-save changes-button');
    expect(confirmButton).toHaveAttribute('data-testid', 'dialog-save changes-button');
  });

  it('handles multiple rapid clicks on confirm button', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    const confirmButton = screen.getByTestId('dialog-confirm-button');
    fireEvent.click(confirmButton);
    fireEvent.click(confirmButton);
    fireEvent.click(confirmButton);

    expect(mockOnConfirm).toHaveBeenCalledTimes(3);
  });

  it('handles multiple rapid clicks on cancel button', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    const cancelButton = screen.getByTestId('border-button');
    fireEvent.click(cancelButton);
    fireEvent.click(cancelButton);

    expect(mockOnClose).toHaveBeenCalledTimes(2);
  });

  it('prevents confirm button clicks when loading', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} isLoading={true} />);

    const confirmButton = screen.getByTestId('dialog-confirm-button');
    fireEvent.click(confirmButton);

    // Should not call onConfirm when disabled due to loading
    expect(mockOnConfirm).not.toHaveBeenCalled();
  });

  it('renders with zero maxWidth', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} maxWidth={0} />);

    expect(screen.getByTestId('default-dialog')).toHaveAttribute('data-max-width', '0');
  });

  it('renders with very large maxWidth', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} maxWidth={9999} />);

    expect(screen.getByTestId('default-dialog')).toHaveAttribute('data-max-width', '9999');
  });

  it('handles null icon gracefully', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} icon={null} />);

    expect(screen.queryByTestId('test-icon')).not.toBeInTheDocument();
  });

  it('handles undefined icon gracefully', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} icon={undefined} />);

    expect(screen.queryByTestId('test-icon')).not.toBeInTheDocument();
  });

  it('renders with complex React node as caption', () => {
    const complexCaption = (
      <div data-testid="complex-caption">
        <p>This action cannot be undone.</p>
        <small>Please confirm your choice.</small>
      </div>
    );

    renderWithTheme(<BaseDialogLayout {...defaultProps} caption={complexCaption} />);

    expect(screen.getByTestId('complex-caption')).toBeInTheDocument();
    expect(screen.getByText('This action cannot be undone.')).toBeInTheDocument();
    expect(screen.getByText('Please confirm your choice.')).toBeInTheDocument();
  });

  it('maintains proper component structure with all elements', () => {
    const fullProps = {
      ...defaultProps,
      message: 'Test message',
      highlightText: 'Test highlight',
      icon: <TestIcon />,
      caption: <div data-testid="test-caption">Test caption</div>,
    };

    renderWithTheme(<BaseDialogLayout {...fullProps} />);

    // Verify all elements are present in the correct order
    expect(screen.getByTestId('default-dialog')).toBeInTheDocument();
    expect(screen.getByTestId('test-icon')).toBeInTheDocument();
    expect(screen.getByText('Test message')).toBeInTheDocument();
    expect(screen.getByTestId('test-caption')).toBeInTheDocument();
    expect(screen.getByText('Test highlight')).toBeInTheDocument();
    expect(screen.getByTestId('border-button')).toBeInTheDocument();
    expect(screen.getByTestId('dialog-confirm-button')).toBeInTheDocument();
  });

  it('handles empty string values gracefully', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} message="" highlightText="" cancelLabel="" confirmLabel="" />);

    expect(screen.getByTestId('border-button')).toHaveTextContent('');
    expect(screen.queryByTestId('blue-button')).not.toBeInTheDocument();
  });

  it('passes through additional props to buttons', () => {
    renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    const confirmButton = screen.getByTestId('dialog-confirm-button');
    const cancelButton = screen.getByTestId('border-button');

    expect(confirmButton).toHaveAttribute('data-testid', 'dialog-confirm-button');
    expect(cancelButton).toHaveAttribute('data-testid', 'border-button');
  });

  it('ensures proper cleanup on unmount', () => {
    const {unmount} = renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    expect(() => unmount()).not.toThrow();
  });

  it('handles re-renders with changing props', () => {
    const {rerender} = renderWithTheme(<BaseDialogLayout {...defaultProps} />);

    expect(screen.getByText('Test Dialog')).toBeInTheDocument();

    rerender(<BaseDialogLayout {...defaultProps} title="Updated Dialog" />);

    expect(screen.getByText('Updated Dialog')).toBeInTheDocument();
    expect(screen.queryByText('Test Dialog')).not.toBeInTheDocument();
  });

  it('maintains state consistency during prop changes', () => {
    const {rerender} = renderWithTheme(<BaseDialogLayout {...defaultProps} isLoading={false} />);

    expect(screen.getByTestId('dialog-confirm-button')).not.toBeDisabled();

    rerender(<BaseDialogLayout {...defaultProps} isLoading={true} />);

    expect(screen.getByTestId('dialog-confirm-button')).toBeDisabled();
    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
  });
});
