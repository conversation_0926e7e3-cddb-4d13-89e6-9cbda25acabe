import {Box, Typography, styled} from '@mui/material';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import {DefaultDialog} from 'Components/DefaultDialog/DefaultDialog';
import {Integers} from 'Helpers/integers';

export interface BaseDialogLayoutProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  /** Label for the confirm action button */
  confirmLabel?: string;
  /** Label for the cancel button (optional, defaults to "Cancel") */
  cancelLabel?: string;
  /** Title of the dialog */
  title: string;
  /** Optional subtitle / confirmation message */
  message?: string;
  /** Optional highlight item (e.g. plan name, user name, etc.) */
  highlightText?: string;
  /** Icon to render inside the circular container */
  icon?: React.ReactNode;
  iconBGColor?: string;
  /** Loading state for confirm button */
  isLoading?: boolean;
  /** Max width of the dialog */
  maxWidth?: number;
  caption?: React.ReactNode;
}

/* --- Styled Components --- */
export const IconWrapper = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
}));

export const KeyIconContainer = styled(Box)(({theme}) => {
  return {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '5rem',
    height: '5rem',
    borderRadius: '50%',
    border: `1px solid ${theme.palette.white[Integers.TwoHundred]}`,
    padding: '1rem',
    backgroundColor: theme.palette.alert.error.bg,
    fill: 'transparent',
    color: theme.palette.alert.success.bg,
  };
});

/**
 * Generic confirmation dialog component.
 *
 * Can be used for activate/deactivate, delete, or any other action.
 */
export const BaseDialogLayout = ({
  open,
  onClose,
  onConfirm,
  confirmLabel,
  cancelLabel = 'Cancel',
  title,
  message,
  highlightText,
  icon,
  isLoading,
  maxWidth = 400,
  iconBGColor,
  caption,
}: BaseDialogLayoutProps) => {
  return (
    <DefaultDialog title={title} maxWidth={maxWidth} open={open} onClose={onClose}>
      <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4}}>
        {icon && (
          <IconWrapper>
            <KeyIconContainer
              sx={{
                backgroundColor: iconBGColor,
              }}
            >
              {icon}
            </KeyIconContainer>
          </IconWrapper>
        )}

        {message && (
          <Typography
            sx={{
              mt: 2,
              fontSize: '1.125rem',
              fontWeight: 700,
              color: 'body.dark',
              textAlign: 'center',
            }}
          >
            {message}
          </Typography>
        )}
        {caption && <Box sx={{mt: 1}}>{caption}</Box>}

        {highlightText && (
          <Typography
            sx={{
              mt: 1,
              fontSize: '0.75rem',
              fontWeight: 700,
              backgroundColor: 'secondary.50',
              borderWidth: '0.0625rem',
              borderStyle: 'solid',
              borderColor: 'secondary.50',
              padding: '3px 10px',
              borderRadius: '4px',
              color: 'secondary.700',
              textAlign: 'center',
            }}
          >
            {highlightText}
          </Typography>
        )}

        <Box
          sx={{
            mt: 4,
            display: 'flex',
            gap: 1,
            flexDirection: 'row',
            width: '100%',
            fontSize: '1rem',
            fontWeight: 600,
            height: '3.125rem',
          }}
        >
          <BorderButton sx={{flex: 1}} onClick={onClose}>
            {cancelLabel}
          </BorderButton>
          {confirmLabel && (
            <BlueButton
              variant="contained"
              isLoading={isLoading}
              sx={{
                flex: 1,
              }}
              onClick={onConfirm}
              data-testid={`dialog-${confirmLabel.toLowerCase()}-button`}
            >
              {confirmLabel}
            </BlueButton>
          )}
        </Box>
      </Box>
    </DefaultDialog>
  );
};
