import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {renderWithTheme, useThemeColor} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import BlueButton from './BlueButton';

describe('BlueButton', () => {
  it('should render with children content', () => {
    render(<BlueButton>Test Button</BlueButton>);
    const button = screen.getByRole('button', {name: 'Test Button'});
    expect(button).toBeInTheDocument();
  });

  it('should call onClick handler when clicked', () => {
    const handleClick = vi.fn();
    render(<BlueButton onClick={handleClick}>Click Me</BlueButton>);
    const button = screen.getByRole('button', {name: 'Click Me'});

    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should apply custom sx prop', () => {
    const customSx = {margin: '16px'};
    render(<BlueButton sx={customSx}>Styled Button</BlueButton>);
    const button = screen.getByRole('button', {name: 'Styled Button'});

    expect(button).toHaveStyle({margin: '16px'});
  });

  it('should forward ref to button element', () => {
    const ref = React.createRef<HTMLButtonElement>();
    render(<BlueButton ref={ref}>Ref Button</BlueButton>);

    expect(ref.current).toBeInstanceOf(HTMLButtonElement);
    expect(ref.current?.textContent).toBe('Ref Button');
  });

  it('should have correct default styling', () => {
    renderWithTheme(<BlueButton>Styled Button</BlueButton>);
    const button = screen.getByRole('button', {name: 'Styled Button'});

    expect(button).toHaveStyle({
      borderColor: 'body.100',
      borderRadius: '0.375rem',
      color: useThemeColor('white.main'),
      backgroundColor: 'secondary.main',
    });
  });

  it('should have correct displayName', () => {
    expect(BlueButton.displayName).toBe('BlueButton');
  });
});
