import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {describe, expect, it, vi} from 'vitest';
import BorderButton from './BorderButton';

describe('BorderButton', () => {
  it('should render with children content', () => {
    render(<BorderButton>Test Button</BorderButton>);
    const button = screen.getByRole('button', {name: 'Test Button'});
    expect(button).toBeInTheDocument();
  });

  it('should call onClick handler when clicked', () => {
    const handleClick = vi.fn();
    render(<BorderButton onClick={handleClick}>Click Me</BorderButton>);
    const button = screen.getByRole('button', {name: 'Click Me'});

    fireEvent.click(button);
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should apply custom sx prop', () => {
    const customSx = {margin: '16px'};
    render(<BorderButton sx={customSx}>Styled Button</BorderButton>);
    const button = screen.getByRole('button', {name: 'Styled Button'});

    expect(button).toHaveStyle({margin: '16px'});
  });

  it('should forward ref to button element', () => {
    const ref = React.createRef<HTMLButtonElement>();
    render(<BorderButton ref={ref}>Ref Button</BorderButton>);

    expect(ref.current).toBeInstanceOf(HTMLButtonElement);
    expect(ref.current?.textContent).toBe('Ref Button');
  });
});
