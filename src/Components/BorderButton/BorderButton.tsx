import {ButtonProps} from '@mui/material';
import Button from 'Components/Button';
import {forwardRef} from 'react';

/**
 * BorderButton component that renders a button with a border style.
 * It accepts onClick handler, children, and additional styles via sx prop.
 */

const BorderButton = forwardRef<HTMLButtonElement, ButtonProps>(({onClick, children, sx, ...reset}, ref) => {
  return (
    <Button
      ref={ref}
      variant="outlined"
      sx={{
        borderColor: 'body.100',
        borderWidth: 1,
        borderRadius: '0.375rem',
        backgroundColor: 'white.main',
        fontSize: 'inherit',
        color: 'body.800',
        ...sx,
      }}
      onClick={onClick}
      {...reset}
    >
      {children}
    </Button>
  );
});
BorderButton.displayName = 'BorderButton';

export default BorderButton;
