import {ThemeProvider, createTheme} from '@mui/material/styles';
import {fireEvent, render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import React from 'react';
import {vi} from 'vitest';
import CloseButton from './CloseButton';

// Create a test theme
const testTheme = createTheme({
  palette: {
    grey: {
      300: '#d1d5db',
      400: '#9ca3af',
      50: '#f9fafb',
    },
    text: {
      primary: '#111827',
    },
  },
});

// Helper function to render component with theme
const renderWithTheme = (component: React.ReactElement) => {
  return render(<ThemeProvider theme={testTheme}>{component}</ThemeProvider>);
};

describe('CloseButton', () => {
  describe('Default behavior', () => {
    it('renders with default text "Close"', () => {
      renderWithTheme(<CloseButton />);
      expect(screen.getByRole('button', {name: 'Close'})).toBeInTheDocument();
    });

    it('renders with default variant "outlined"', () => {
      renderWithTheme(<CloseButton />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('MuiButton-outlined');
    });

    it('renders with default size "medium"', () => {
      renderWithTheme(<CloseButton />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('MuiButton-sizeMedium');
    });
  });

  describe('Custom props', () => {
    it('renders custom children text', () => {
      renderWithTheme(<CloseButton>Cancel</CloseButton>);
      expect(screen.getByRole('button', {name: 'Cancel'})).toBeInTheDocument();
    });

    it('renders with custom variant "contained"', () => {
      renderWithTheme(<CloseButton variant="contained" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('MuiButton-contained');
    });

    it('renders with custom variant "text"', () => {
      renderWithTheme(<CloseButton variant="text" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('MuiButton-text');
    });

    it('renders with custom size "small"', () => {
      renderWithTheme(<CloseButton size="small" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('MuiButton-sizeSmall');
    });

    it('renders with custom size "large"', () => {
      renderWithTheme(<CloseButton size="large" />);
      const button = screen.getByRole('button');
      expect(button).toHaveClass('MuiButton-sizeLarge');
    });

    it('renders custom children as React element', () => {
      renderWithTheme(
        <CloseButton>
          <span data-testid="custom-icon">×</span>
          {' Custom Close'}
        </CloseButton>,
      );
      expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
      expect(screen.getByText('Custom Close')).toBeInTheDocument();
    });
  });

  describe('Click handling', () => {
    it('calls onClick handler when clicked', () => {
      const mockOnClick = vi.fn();
      renderWithTheme(<CloseButton onClick={mockOnClick} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('does not throw error when clicked without onClick handler', () => {
      renderWithTheme(<CloseButton />);

      const button = screen.getByRole('button');

      expect(() => {
        fireEvent.click(button);
      }).not.toThrow();
    });

    it('calls onClick multiple times when clicked multiple times', () => {
      const mockOnClick = vi.fn();
      renderWithTheme(<CloseButton onClick={mockOnClick} />);

      const button = screen.getByRole('button');
      fireEvent.click(button);
      fireEvent.click(button);
      fireEvent.click(button);

      expect(mockOnClick).toHaveBeenCalledTimes(3);
    });
  });

  describe('Styling', () => {
    it('applies custom styles for outlined variant', () => {
      renderWithTheme(<CloseButton variant="outlined" />);
      const button = screen.getByRole('button');

      // Check for specific CSS properties applied to outlined variant
      expect(button).toHaveStyle({
        borderRadius: '0.375rem',
        fontSize: '0.875rem',
        fontWeight: '500',
        textTransform: 'none',
      });
    });

    it('has correct border and text color for outlined variant', () => {
      renderWithTheme(<CloseButton variant="outlined" />);
      const button = screen.getByRole('button');

      // The component should apply theme colors via sx prop
      expect(button).toHaveClass('MuiButton-outlined');
    });

    it('maintains minWidth auto style', () => {
      renderWithTheme(<CloseButton />);
      const button = screen.getByRole('button');

      expect(button).toHaveStyle({
        minWidth: 'auto',
      });
    });
  });

  describe('Accessibility', () => {
    it('is accessible as a button', () => {
      renderWithTheme(<CloseButton />);
      const button = screen.getByRole('button');

      expect(button).toBeInTheDocument();
      expect(button.tagName).toBe('BUTTON');
    });

    it('has correct accessible name', () => {
      renderWithTheme(<CloseButton>Save Changes</CloseButton>);

      expect(screen.getByRole('button', {name: 'Save Changes'})).toBeInTheDocument();
    });

    it('can be focused', () => {
      renderWithTheme(<CloseButton />);
      const button = screen.getByRole('button');

      button.focus();
      expect(button).toHaveFocus();
    });

    it('can be activated with Enter key', async () => {
      const user = userEvent.setup();
      const mockOnClick = vi.fn();
      renderWithTheme(<CloseButton onClick={mockOnClick} />);

      await user.tab(); // Focus the button
      await user.keyboard('{Enter}');

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('can be activated with Space key', async () => {
      const user = userEvent.setup();
      const mockOnClick = vi.fn();
      renderWithTheme(<CloseButton onClick={mockOnClick} />);

      await user.tab(); // Focus the button
      await user.keyboard(' ');

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('Edge cases', () => {
    it('handles empty children', () => {
      renderWithTheme(<CloseButton>{''}</CloseButton>);
      const button = screen.getByRole('button');

      expect(button).toBeInTheDocument();
      expect(button).toHaveTextContent('');
    });

    it('handles null children fallback to default', () => {
      renderWithTheme(<CloseButton>{null}</CloseButton>);
      const button = screen.getByRole('button');

      expect(button).toBeInTheDocument();
    });

    it('handles undefined children fallback to default', () => {
      renderWithTheme(<CloseButton>{undefined}</CloseButton>);
      const button = screen.getByRole('button');

      expect(button).toBeInTheDocument();
    });

    it('renders correctly with all props combined', () => {
      const mockOnClick = vi.fn();
      renderWithTheme(
        <CloseButton onClick={mockOnClick} variant="contained" size="large">
          Custom Button Text
        </CloseButton>,
      );

      const button = screen.getByRole('button', {name: 'Custom Button Text'});
      expect(button).toBeInTheDocument();
      expect(button).toHaveClass('MuiButton-contained');
      expect(button).toHaveClass('MuiButton-sizeLarge');

      fireEvent.click(button);
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });
  });

  describe('Theme integration', () => {
    it('uses theme colors for outlined variant', () => {
      renderWithTheme(<CloseButton variant="outlined" />);
      const button = screen.getByRole('button');

      // Verify that the component is using Material-UI theme
      expect(button).toHaveClass('MuiButton-root');
      expect(button).toHaveClass('MuiButton-outlined');
    });

    it('applies hover styles correctly', () => {
      renderWithTheme(<CloseButton variant="outlined" />);
      const button = screen.getByRole('button');

      // Hover over the button
      fireEvent.mouseEnter(button);

      // The hover styles are applied via sx prop and should be present
      expect(button).toHaveClass('MuiButton-outlined');
    });
  });
});
