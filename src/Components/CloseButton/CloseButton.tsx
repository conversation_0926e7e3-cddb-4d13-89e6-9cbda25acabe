import {Button, useTheme} from '@mui/material';
import {Integers} from 'Helpers/integers';
import React from 'react';

interface CloseButtonProps {
  onClick?: () => void;
  children?: React.ReactNode;
  variant?: 'contained' | 'outlined' | 'text';
  size?: 'small' | 'medium' | 'large';
}

/**
 * Reusable Close button component
 * Matches Figma design with proper styling
 */
const CloseButton: React.FC<CloseButtonProps> = ({
  onClick,
  children = 'Close',
  variant = 'outlined',
  size = 'medium',
}) => {
  const theme = useTheme();

  return (
    <Button
      variant={variant}
      size={size}
      onClick={onClick}
      sx={{
        borderRadius: '0.375rem',
        padding: '0.5rem 1rem',
        fontSize: '0.875rem',
        fontWeight: 500,
        textTransform: 'none',
        minWidth: 'auto',
        ...(variant === 'outlined' && {
          borderColor: theme.palette.grey[Integers.ThreeHundred],
          color: theme.palette.text.primary,
          '&:hover': {
            borderColor: theme.palette.grey[Integers.FourHundred],
            backgroundColor: theme.palette.grey[50],
          },
        }),
      }}
    >
      {children}
    </Button>
  );
};

export default CloseButton;
