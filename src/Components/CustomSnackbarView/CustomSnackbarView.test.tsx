// src/Components/CustomSnackbarView/CustomSnackbarView.test.tsx
import {fireEvent, screen} from '@testing-library/react';
import {SnackbarProvider} from 'notistack';
import {renderWithTheme} from 'TestHelper/TestHelper';
import CustomSnackbarView from './CustomSnackbarView';

describe('CustomSnackbarView', () => {
  const defaultProps = {
    id: 'snackbar-1',
    message: 'Test message',
    subMessage: 'Test sub-message',
    variant: 'success' as const, // Use string literal type for variant
    iconVariant: {},
    style: {},
    anchorOrigin: {vertical: 'bottom', horizontal: 'center'} as const,
    hideIconVariant: false,
    persist: false,
  };

  it('renders message and subMessage', () => {
    renderWithTheme(
      <SnackbarProvider>
        <CustomSnackbarView {...defaultProps} />
      </SnackbarProvider>,
    );
    expect(screen.getByText('Test message')).toBeInTheDocument();
    expect(screen.getByText('Test sub-message')).toBeInTheDocument();
  });

  it('calls closeSnackbar when close icon is clicked', () => {
    renderWithTheme(
      <SnackbarProvider>
        <CustomSnackbarView {...defaultProps} />
      </SnackbarProvider>,
    );
    const closeButton = screen.getByTestId('close-button');
    fireEvent.click(closeButton);
    // No assertion here since closeSnackbar is a hook, but click should not throw
  });
});
