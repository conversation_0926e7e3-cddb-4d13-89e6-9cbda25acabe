import {Close} from '@mui/icons-material';
import {Stack, Typography, useTheme} from '@mui/material';
import {CustomContentProps, SnackbarContent, useSnackbar, VariantType} from 'notistack';
import React from 'react';

interface MyCustomProps extends CustomContentProps {
  subMessage?: string;
}
/**
 * Custom Snackbar View Component
 * Displays a customizable Snackbar notification.
 */
const CustomSnackbarView = React.forwardRef<HTMLDivElement, MyCustomProps>(
  ({id, message, subMessage, variant, style, ...props}, ref) => {
    const theme = useTheme();

    const {closeSnackbar} = useSnackbar();
    /**
     * Background color mapping for different Snackbar variants.
     */
    const backgroundColor: Record<VariantType, string> = {
      ['success']: theme.palette.alert.success.bg,
      ['error']: theme.palette.alert.error.bg,
      ['warning']: theme.palette.alert.warning.bg,
      ['info']: theme.palette.alert.info.bg,
      default: theme.palette.alert.success.bg, // Default to success background
    };

    /**
     * Text color mapping for different Snackbar variants.
     */
    const textColor = {
      ['success']: theme.palette.alert.success.onBg,
      ['error']: theme.palette.alert.error.onBg,
      ['warning']: theme.palette.alert.warning.onBg,
      ['info']: theme.palette.alert.info.onBg,
      default: theme.palette.alert.success.onBg,
    };

    return (
      <SnackbarContent
        ref={ref} // forward the ref here
        role="alert"
        style={{
          ...style,
          backgroundColor: backgroundColor[variant],
          color: textColor[variant],
          borderRadius: '12px',
          boxShadow: `0px 4px 10px ${theme.palette.body[100]}`,
          padding: '12px 16px',
          fontWeight: 500,
          borderWidth: 0.5,
          borderStyle: 'solid',
          borderColor: textColor[variant],
        }}
        {...props}
      >
        <Stack spacing={0} flex={1}>
          <Stack direction="row" alignItems="center" justifyContent={'space-between'} spacing={1}>
            <Typography fontWeight="bold">{message}</Typography>

            <Close
              data-testid="close-button"
              sx={{p: 0.5, cursor: 'pointer', color: theme.palette.body[700]}}
              onClick={() => closeSnackbar(id)}
            />
          </Stack>
          {subMessage && <Typography variant="body2">{subMessage}</Typography>}
        </Stack>
      </SnackbarContent>
    );
  },
);

CustomSnackbarView.displayName = 'CustomSnackbar';

export default CustomSnackbarView;
