import {styled} from '@mui/material/styles';
import Switch, {SwitchProps} from '@mui/material/Switch';
import {Integers} from 'Helpers/integers';

const opacityDisabledColor = 0.12;
const opacityDisabledDarkColor = 0.2;
const borderRadius = 26;

/**
 * A styled Material-UI Switch component with custom appearance and behavior.
 *
 * @component
 * @param {SwitchProps} props - The props for the Switch component.
 * @returns {JSX.Element} A customized switch component with tailored styles for checked, disabled, and focus states.
 *
 * @remarks
 * - Uses theme and custom color variables for dynamic styling.
 * - Adjusts track and thumb appearance based on switch state.
 * - Disables ripple effect and applies custom transitions.
 */
const CustomSwitch = styled((props: SwitchProps) => (
  <Switch focusVisibleClassName=".Mui-focusVisible" disableRipple {...props} />
))(({theme}) => {
  const color = theme.palette;
  return {
    width: 42,
    height: 26,
    padding: 0,
    '& .MuiSwitch-switchBase': {
      padding: 0,
      margin: 2,
      transitionDuration: '300ms',
      '&.Mui-checked': {
        transform: 'translateX(1rem)',
        color: theme.palette.white.main,
        '& + .MuiSwitch-track': {
          backgroundColor: color.primary.main,
          opacity: 1,
          border: 0,
        },
        '&.Mui-disabled + .MuiSwitch-track': {
          opacity: 0.5,
        },
      },
      '&.Mui-focusVisible .MuiSwitch-thumb': {
        color: color.alert.success.main,
        border: '0.375rem solid' + color.white.main,
      },
      '&.Mui-disabled .MuiSwitch-thumb': {
        color: 'body.100',
      },
      '&.Mui-disabled + .MuiSwitch-track': {
        opacity: theme.palette.mode === 'light' ? opacityDisabledColor : opacityDisabledDarkColor,
      },
    },
    '& .MuiSwitch-thumb': {
      boxSizing: 'border-box',
      width: 22,
      height: 22,
    },
    '& .MuiSwitch-track': {
      borderRadius: borderRadius / 2,
      backgroundColor: color.body[Integers.OneHundred],
      opacity: 1,
      transition: theme.transitions.create(['background-color'], {
        duration: 500,
      }),
    },
  };
});

export default CustomSwitch;
