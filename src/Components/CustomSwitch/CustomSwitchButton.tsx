import {FormControlLabel} from '@mui/material';
import React from 'react';
import CustomSwitch from './CustomSwitch';

type LabelPlacementType = 'start' | 'end' | 'top' | 'bottom';

interface CustomSwitchButtonProps {
  checked: boolean;
  onChange: (event: React.ChangeEvent<HTMLInputElement>, checked: boolean) => void;
  label?: string;
  disabled?: boolean;
  labelPlacement?: LabelPlacementType;
}

/**
 * A custom switch button component that wraps a `CustomSwitch` with a label using Material-UI's `FormControlLabel`.
 *
 * @param checked - Determines whether the switch is checked.
 * @param onChange - Callback fired when the switch state changes.
 * @param label - The label to display next to the switch.
 * @param disabled - If `true`, the switch will be disabled. Defaults to `false`.
 * @param labelPlacement - The position of the label relative to the switch. Defaults to `'end'`.
 *
 * @returns A styled switch button with a label.
 */
const CustomSwitchButton: React.FC<CustomSwitchButtonProps> = ({
  checked,
  onChange,
  label,
  disabled = false,
  labelPlacement = 'end',
}) => {
  return (
    <FormControlLabel
      control={<CustomSwitch checked={checked} onChange={onChange} disabled={disabled} />}
      label={label}
      labelPlacement={labelPlacement}
      data-testid="unlimited-users-toggle"
      sx={{
        marginLeft: 0,
        marginRight: 2,
        '& .MuiFormControlLabel-label': {
          fontSize: '0.875rem',
          color: 'text.primary',
        },
      }}
    />
  );
};

export default CustomSwitchButton;
