import {LocalizationProvider} from '@mui/x-date-pickers';
import {AdapterDateFns} from '@mui/x-date-pickers/AdapterDateFns';
import {render, screen, within} from '@testing-library/react';
import {useState} from 'react';
import DatePicker from './DatePicker';

function TestComponent() {
  const [value, setValue] = useState<Date | null>(null);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DatePicker id="test" label="test" value={value} onChange={setValue} />
    </LocalizationProvider>
  );
}

describe('DatePicker', () => {
  it('should be rendered', async () => {
    render(<TestComponent />);
    const datePickerFormControl = screen.getByTestId('datePickerFormControl');
    const label = within(datePickerFormControl).getByText(/test/i);
    expect(label).toBeVisible();
    // Look for input element instead of textbox role since <PERSON><PERSON> DatePicker uses a complex input structure
    const input = within(datePickerFormControl).getByDisplayValue('');
    expect(input).toBeInTheDocument();
  });
});
