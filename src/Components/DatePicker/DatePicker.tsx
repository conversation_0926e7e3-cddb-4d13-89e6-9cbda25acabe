import {FormControl, FormHelperText, SxProps, Theme, useTheme} from '@mui/material';
import {DatePicker as MuiDatePicker, DatePickerProps as MuiDatePickerProps} from '@mui/x-date-pickers/DatePicker';
import InputLabel from 'Components/InputLabel';
import {Integers} from 'Helpers/integers';
import React, {memo, useCallback} from 'react';

export interface DatePickerProps {
  id: string;
  label?: string;
  onChange?: (val: Date | null) => void;
  disabled?: boolean;
  errorMessage?: string;
  helperText?: string;
  minDateTime?: Date;
  sx?: SxProps<Theme>;
}

interface Props extends DatePickerProps {
  value?: Date | null;
}

const DatePicker: React.FC<Props & Omit<MuiDatePickerProps, 'renderInput'>> = ({
  id,
  label,
  value,
  onChange,
  errorMessage,
  sx,
  disabled,
  helperText,
  ...rest
}) => {
  const color = useTheme().palette;
  const isError = !!errorMessage;
  const handleChange = useCallback(
    (date: Date | null) => {
      if (onChange) onChange(date);
    },
    [onChange],
  );

  return (
    <FormControl sx={{width: 1, ...sx}} data-testid="datePickerFormControl" disabled={disabled} error={isError}>
      {label && (
        <InputLabel sx={{color: isError ? 'alert.error.main' : 'body.500'}} htmlFor={id}>
          {label}
        </InputLabel>
      )}
      <MuiDatePicker
        slotProps={{
          popper: {
            sx: {
              // Change color of the year items in year view
              '& .MuiYearCalendar-root .MuiYearCalendar-button': {
                color: 'body.dark', // normal text color
              },
              '& .MuiYearCalendar-root .Mui-selected': {
                color: 'white',
                backgroundColor: 'secondary.main', // selected year
              },
              '& .MuiYearCalendar-root .MuiButtonBase-root:hover': {
                backgroundColor: 'secondary[50]', // hover color
              },
            },
          },

          textField: {
            sx: {
              marginTop: '1.25rem',

              '& .MuiPickersOutlinedInput-root': {
                fontSize: '0.875rem',
                height: '2.75rem',
                '& fieldset': {
                  borderColor: color.secondary[Integers.FiveHundred],
                },
                '&:hover fieldset': {
                  borderColor: color.secondary[Integers.FiveHundred],
                },
                '&.Mui-focused fieldset': {
                  borderColor: color.secondary[Integers.FiveHundred] + ' !important',
                },
              },
            },
            id: id,
          },
          inputAdornment: {position: 'start'},
        }}
        disabled={disabled}
        value={value}
        onChange={handleChange}
        {...rest}
      />

      {(isError || helperText) && <FormHelperText>{isError ? errorMessage : helperText}</FormHelperText>}
    </FormControl>
  );
};

export default memo(DatePicker);
