import {fireEvent, render, screen} from '@testing-library/react';
import {createRef} from 'react';
import {describe, expect, it, vi} from 'vitest';
import {IClearFilters} from '../FilterUtil/IClearFilters';
import DateRangeFilter, {DateRangeFilterSelectedDate} from './DateRangeFilter';
import {DateRangeUtil} from './DateRangeUtil';

// Constants
const TEST_DATE = new Date('2025-07-31T12:00:00Z');
const ONE_DAY_MS = 24 * 60 * 60 * 1000;

enum DateRangeOptions {
  LAST_MONTH = 'Last Month',
  LAST_WEEK = 'Last Week',
  CUSTOM = 'Custom',
}

// Mock components
vi.mock('Components/DateRangePicker', () => ({
  default: ({
    onChange,
    value,
  }: {
    onChange: (value: {from: Date; to: Date}) => void;
    value: {from: Date; to: Date} | null;
  }) => (
    <div data-testid="date-range-picker">
      <button
        onClick={() =>
          onChange({
            from: new Date(TEST_DATE.getTime() - ONE_DAY_MS),
            to: TEST_DATE,
          })
        }
      >
        Select Date Range
      </button>
      {value && (
        <div>
          <span>From: {value.from.toISOString()}</span>
          <span>To: {value.to.toISOString()}</span>
        </div>
      )}
    </div>
  ),
}));

describe('DateRangeFilter', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.setSystemTime(TEST_DATE);
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('should render all filter chips', () => {
    render(<DateRangeFilter />);

    expect(screen.getByText(DateRangeOptions.LAST_MONTH)).toBeInTheDocument();
    expect(screen.getByText(DateRangeOptions.LAST_WEEK)).toBeInTheDocument();
    expect(screen.getByText(DateRangeOptions.CUSTOM)).toBeInTheDocument();
  });

  it('should select last month range when clicked', () => {
    const onSelect = vi.fn();
    render(<DateRangeFilter onSelect={onSelect} />);

    fireEvent.click(screen.getByText(DateRangeOptions.LAST_MONTH));

    const expectedRange = DateRangeUtil.getLastMonthRange();
    expect(onSelect).toHaveBeenCalledWith({
      ...expectedRange,
      dateRangeOption: DateRangeOptions.LAST_MONTH,
    });
  });

  it('should select last week range when clicked', () => {
    const onSelect = vi.fn();
    render(<DateRangeFilter onSelect={onSelect} />);

    fireEvent.click(screen.getByText(DateRangeOptions.LAST_WEEK));

    const expectedRange = DateRangeUtil.getLastWeekRange();
    expect(onSelect).toHaveBeenCalledWith({
      ...expectedRange,
      dateRangeOption: DateRangeOptions.LAST_WEEK,
    });
  });

  it('should show date picker when custom option is selected', () => {
    render(<DateRangeFilter />);

    fireEvent.click(screen.getByText(DateRangeOptions.CUSTOM));

    expect(screen.getByTestId('date-range-picker')).toBeInTheDocument();
  });

  it('should handle custom date range selection', () => {
    const onSelect = vi.fn();
    render(<DateRangeFilter onSelect={onSelect} />);

    fireEvent.click(screen.getByText(DateRangeOptions.CUSTOM));
    fireEvent.click(screen.getByText('Select Date Range'));

    const expectedFrom = new Date(TEST_DATE.getTime() - ONE_DAY_MS);
    const expectedTo = TEST_DATE;

    expect(onSelect).toHaveBeenCalledWith({
      startDate: expectedFrom,
      endDate: expectedTo,
      dateRangeOption: DateRangeOptions.CUSTOM,
    });
  });

  it('should render with pre-selected value', () => {
    const preSelectedValue: DateRangeFilterSelectedDate = {
      startDate: new Date(TEST_DATE.getTime() - ONE_DAY_MS),
      endDate: TEST_DATE,
      dateRangeOption: DateRangeOptions.CUSTOM,
    };

    render(<DateRangeFilter value={preSelectedValue} />);

    expect(screen.getByText(`From: ${preSelectedValue.startDate.toISOString()}`)).toBeInTheDocument();
    expect(screen.getByText(`To: ${preSelectedValue.endDate.toISOString()}`)).toBeInTheDocument();
  });

  it('should clear selection using ref', () => {
    const onSelect = vi.fn();
    const ref = createRef<IClearFilters>();

    render(<DateRangeFilter ref={ref} onSelect={onSelect} />);

    fireEvent.click(screen.getByText(DateRangeOptions.LAST_MONTH));
    expect(onSelect).toHaveBeenCalledWith(expect.any(Object));

    ref.current?.clearSelection();
    expect(onSelect).toHaveBeenCalledWith(undefined);
  });

  it('should initialize custom date range when no dates are selected', () => {
    const onSelect = vi.fn();
    render(<DateRangeFilter onSelect={onSelect} />);

    fireEvent.click(screen.getByText(DateRangeOptions.CUSTOM));

    expect(onSelect).toHaveBeenCalledWith({
      startDate: DateRangeUtil.getStartOfDay(),
      endDate: DateRangeUtil.getEndOfDay(),
      dateRangeOption: DateRangeOptions.CUSTOM,
    });
  });
});
