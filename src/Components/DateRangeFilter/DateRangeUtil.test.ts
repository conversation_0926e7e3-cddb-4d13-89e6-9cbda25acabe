import dayjs from 'dayjs';
import {describe, expect, it} from 'vitest';
import {DateRangeUtil} from './DateRangeUtil';
import {SelectedDateRange} from './types';

describe('DateRangeUtil', () => {
  describe('getLastMonthRange', () => {
    it('should return the correct start and end dates for last month', () => {
      const expectedStart = dayjs().subtract(1, 'month').startOf('month').toDate();
      const expectedEnd = dayjs().subtract(1, 'month').endOf('month').toDate();
      const range: SelectedDateRange = DateRangeUtil.getLastMonthRange();
      expect(range.startDate.getTime()).toBe(expectedStart.getTime());
      expect(range.endDate.getTime()).toBe(expectedEnd.getTime());
    });
  });

  describe('getLastWeekRange', () => {
    it('should return the correct start and end dates for last week', () => {
      const expectedStart = dayjs().subtract(1, 'week').startOf('week').toDate();
      const expectedEnd = dayjs().subtract(1, 'week').endOf('week').toDate();
      const range: SelectedDateRange = DateRangeUtil.getLastWeekRange();
      expect(range.startDate.getTime()).toBe(expectedStart.getTime());
      expect(range.endDate.getTime()).toBe(expectedEnd.getTime());
    });
  });

  describe('getStartOfDay', () => {
    it('should return the start of today when no date is provided', () => {
      const expected = dayjs().startOf('day').toDate();
      const result = DateRangeUtil.getStartOfDay();
      expect(result.getTime()).toBe(expected.getTime());
    });

    it('should return the start of the provided date', () => {
      const date = new Date('2024-06-01T15:30:45.000Z');
      const expected = dayjs(date).startOf('day').toDate();
      const result = DateRangeUtil.getStartOfDay(date);
      expect(result.getTime()).toBe(expected.getTime());
    });
  });

  describe('getEndOfDay', () => {
    it('should return the end of today when no date is provided', () => {
      const expected = dayjs().endOf('day').toDate();
      const result = DateRangeUtil.getEndOfDay();
      expect(result.getTime()).toBe(expected.getTime());
    });

    it('should return the end of the provided date', () => {
      const date = new Date('2024-06-01T15:30:45.000Z');
      const expected = dayjs(date).endOf('day').toDate();
      const result = DateRangeUtil.getEndOfDay(date);
      expect(result.getTime()).toBe(expected.getTime());
    });
  });
});
