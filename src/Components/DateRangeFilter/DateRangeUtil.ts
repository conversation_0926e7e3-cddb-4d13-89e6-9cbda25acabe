import dayjs from 'dayjs';
import {SelectedDateRange} from './types';

/**
 * Utility class for handling date ranges.
 * Provides methods to get predefined date ranges like last month and last week,
 * as well as methods to get the start and end of a day.
 */
export class DateRangeUtil {
  static getLastMonthRange(): SelectedDateRange {
    const start = dayjs().subtract(1, 'month').startOf('month').toDate();
    const end = dayjs().subtract(1, 'month').endOf('month').toDate();
    return {startDate: start, endDate: end};
  }
  static getLastWeekRange(): SelectedDateRange {
    const start = dayjs().subtract(1, 'week').startOf('week').toDate();
    const end = dayjs().subtract(1, 'week').endOf('week').toDate();
    return {startDate: start, endDate: end};
  }

  static getStartOfDay(date: Date = new Date()): Date {
    return dayjs(date).startOf('day').toDate();
  }

  static getEndOfDay(date: Date = new Date()): Date {
    return dayjs(date).endOf('day').toDate();
  }
}
