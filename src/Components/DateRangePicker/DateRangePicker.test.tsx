import {LocalizationProvider} from '@mui/x-date-pickers';
import {AdapterDateFns} from '@mui/x-date-pickers/AdapterDateFns';
import {fireEvent, render, screen} from '@testing-library/react';
import {vi} from 'vitest';
import {DateRangeUtil} from '../DateRangeFilter/DateRangeUtil';
import DateRangePicker from './DateRangePicker';

// Mock DatePicker to directly call onChange
vi.mock('../DatePicker/DatePicker', () => ({
  __esModule: true,
  default: (props: any) => (
    <input
      data-testid={props.id}
      aria-label={props.label}
      value={props.value}
      onChange={e => {
        const date = new Date(e.target.value);
        props.onChange(date);
      }}
    />
  ),
}));

// Mock DateRangeUtil
vi.mock('../DateRangeFilter/DateRangeUtil', () => ({
  DateRangeUtil: {
    getStartOfDay: vi.fn(date => new Date('2023-01-01T00:00:00.000Z')),
    getEndOfDay: vi.fn(date => new Date('2023-01-31T23:59:59.999Z')),
  },
}));

describe('Custom Date Range Picker', () => {
  const mockOnChange = vi.fn();
  const defaultProps = {
    value: {from: new Date('2023-01-01'), to: new Date('2023-01-31')},
    onChange: mockOnChange,
    disabled: false,
    errorMessage: '',
    sx: {},
  };

  it('renders without crashing', () => {
    render(
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DateRangePicker {...defaultProps} />
      </LocalizationProvider>,
    );

    expect(screen.getByLabelText('From')).toBeInTheDocument();
    expect(screen.getByLabelText('To')).toBeInTheDocument();
  });

  it('shows error message when errorMessage prop is provided', () => {
    render(
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DateRangePicker {...defaultProps} errorMessage="Error" />
      </LocalizationProvider>,
    );
    expect(screen.getByTestId('date-picker-form-control')).toBeInTheDocument();
  });

  it('calls getStartOfDay and onChange when "from" date changes', () => {
    render(
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DateRangePicker {...defaultProps} />
      </LocalizationProvider>,
    );
    // Simulate changing the "from" date
    const fromDatePicker = screen.getByLabelText('From');
    fireEvent.change(fromDatePicker, {target: {value: '2023-01-02'}});
    expect(DateRangeUtil.getStartOfDay).toHaveBeenCalled();
    expect(mockOnChange).toHaveBeenCalled();
  });

  it('calls getEndOfDay and onChange when "to" date changes', () => {
    render(
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <DateRangePicker {...defaultProps} />
      </LocalizationProvider>,
    );
    // Simulate changing the "to" date
    const toDatePicker = screen.getByLabelText('To');
    fireEvent.change(toDatePicker, {target: {value: '2023-02-01'}});
    expect(DateRangeUtil.getEndOfDay).toHaveBeenCalled();
    expect(mockOnChange).toHaveBeenCalled();
  });
});
