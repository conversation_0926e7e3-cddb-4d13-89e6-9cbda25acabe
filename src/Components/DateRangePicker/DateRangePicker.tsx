import FormControl from '@mui/material/FormControl';
import {SxProps, Theme} from '@mui/material/styles';
import CalendarIcon from 'Assets/date-range-calendar-icon.svg';
import DatePicker from 'Components/DatePicker/DatePicker';
import {DateRangeUtil} from 'Components/DateRangeFilter/DateRangeUtil';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import {useCallback, useState} from 'react';
// use this type for value
export interface IDateRangePickerValue {
  from: Date;
  to: Date;
}

type DateRangeKeys = keyof IDateRangePickerValue;
interface IDateRangePickerProps {
  value?: IDateRangePickerValue | null;
  onChange?: (value: IDateRangePickerValue) => void;
  errorMessage?: string;
  sx?: SxProps<Theme>;
  disableFutureDates?: boolean;
}

const DatePickerCalendarIcon = () => <SVGImageFromPath path={CalendarIcon} sx={{width: '1rem', height: '1rem'}} />;

const DateRangePicker: React.FC<IDateRangePickerProps> = ({
  value,
  onChange,
  errorMessage,
  sx,
  disableFutureDates = true,
}) => {
  const [dateValue, setDateValue] = useState<IDateRangePickerValue>(value || {from: new Date(), to: new Date()});
  const isError = !!errorMessage;

  const handleDateChange = useCallback(
    (key: DateRangeKeys, newValue: Date | null) => {
      let newDateValue = newValue;
      if (key == 'from' && newValue) {
        // Ensure 'from' date is set to the start of the day
        newDateValue = DateRangeUtil.getStartOfDay(newValue);
      }
      if (key == 'to' && newValue) {
        // Ensure 'to' date is set to the end of the day
        newDateValue = DateRangeUtil.getEndOfDay(newValue);
      }

      setDateValue({...dateValue, [key]: newDateValue});
      onChange?.({...dateValue, [key]: newDateValue});
    },
    [onChange, dateValue],
  );

  return (
    <FormControl
      sx={{display: 'flex', flexDirection: 'row', gap: 2, m: 0, ...sx}}
      data-testid="date-picker-form-control"
      error={isError}
    >
      <DatePicker
        value={dateValue.from}
        onChange={date => handleDateChange('from', date)}
        id="from-date-picker"
        label="From"
        disableFuture={disableFutureDates}
        slots={{
          openPickerIcon: DatePickerCalendarIcon, // Or use a valid React component, e.g. openPickerIcon: MySvgIcon
        }}
      />
      <DatePicker
        value={dateValue.to}
        minDate={dateValue.from}
        onChange={date => handleDateChange('to', date)}
        id="to-date-picker"
        label="To"
        disableFuture={disableFutureDates}
        slots={{
          openPickerIcon: DatePickerCalendarIcon, // Or use a valid React component, e.g. openPickerIcon: MySvgIcon
        }}
      />
    </FormControl>
  );
};

export default DateRangePicker;
