import {FormControl, FormHelperText, SxProps, Theme} from '@mui/material';
import {
  DateTimePicker as MuiDateTimePicker,
  DateTimePickerProps as MuiDateTimePickerProps,
} from '@mui/x-date-pickers/DateTimePicker';
import InputLabel from 'Components/InputLabel';
import React, {memo, useCallback} from 'react';

export interface DateTimePickerProps {
  id: string;
  label?: string;
  onChange?: (val: Date | null) => void;
  disabled?: boolean;
  errorMessage?: string;
  helperText?: string;
  minDateTime?: Date;
  sx?: SxProps<Theme>;
}

interface Props extends DateTimePickerProps {
  value?: Date | null;
}

const DateTimePicker: React.FC<Props & Omit<MuiDateTimePickerProps, 'renderInput'>> = ({
  id,
  label,
  value,
  onChange,
  errorMessage,
  sx,
  disabled,
  helperText,
  ...rest
}) => {
  const isError = !!errorMessage;
  const handleChange = useCallback(
    (date: Date | null) => {
      if (onChange) onChange(date);
    },
    [onChange],
  );

  return (
    <FormControl sx={{width: 1, ...sx}} data-testid="dateTimePickerFormControl" disabled={disabled} error={isError}>
      {label && <InputLabel htmlFor={id}>{label}</InputLabel>}
      <MuiDateTimePicker
        slotProps={{
          textField: {
            sx: {marginTop: 2, p: 1},
            id: id,
          },
          inputAdornment: {position: 'start'},
        }}
        disabled={disabled}
        value={value}
        onChange={handleChange}
        {...rest}
      />
      {(isError || helperText) && <FormHelperText>{isError ? errorMessage : helperText}</FormHelperText>}
    </FormControl>
  );
};

export default memo(DateTimePicker);
