import {LocalizationProvider} from '@mui/x-date-pickers';
import {AdapterDateFns} from '@mui/x-date-pickers/AdapterDateFns';
import {render, screen, within} from '@testing-library/react';
import {useState} from 'react';
import DateTimePicker from './DateTimePicker';

function TestComponent() {
  const [value, setValue] = useState<Date | null>(null);

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <DateTimePicker id="test" label="test" value={value} onChange={setValue} />
    </LocalizationProvider>
  );
}

describe('DateTimePicker', () => {
  it('should be rendered', async () => {
    render(<TestComponent />);
    const datePickerFormControl = screen.getByTestId('dateTimePickerFormControl');
    const label = within(datePickerFormControl).getByText(/test/i);
    expect(label).toBeVisible();
    // Look for input element instead of textbox role since MUI DateTimePicker uses a complex input structure
    const input = within(datePickerFormControl).getByDisplayValue('');
    expect(input).toBeInTheDocument();
  });
});
