import {createTheme} from '@mui/material';
import {ThemeProvider} from '@mui/material/styles';
import {render, screen} from '@testing-library/react';
import {describe, expect, it} from 'vitest';
import DefaultCloseIcon from './DefaultCloseIcon';

describe('DefaultCloseIcon', () => {
  const theme = createTheme({
    palette: {
      body: {
        dark: '#000000',
      },
    },
  });

  const renderWithTheme = (component: React.ReactNode) =>
    render(<ThemeProvider theme={theme}>{component}</ThemeProvider>);

  it('should render the close icon', () => {
    renderWithTheme(<DefaultCloseIcon data-testid="close-icon" />);
    const icon = screen.getByTestId('close-icon');
    expect(icon).toBeInTheDocument();
  });

  it('should have correct default styling', () => {
    renderWithTheme(<DefaultCloseIcon data-testid="close-icon" />);
    const icon = screen.getByTestId('close-icon');
    expect(icon).toHaveStyle({
      color: '#000000',
      padding: '4px',
    });
  });
});
