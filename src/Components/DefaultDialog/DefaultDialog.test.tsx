import {fireEvent, screen} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import {DefaultDialog, DefaultDialogProps} from './DefaultDialog';

const testDialogTitle = 'Test Dialog';

const defaultProps: DefaultDialogProps = {
  title: testDialogTitle,
  children: <div>Dialog Content</div>,
  open: true,
};

describe('DefaultDialog', () => {
  it('renders the dialog when open is true', () => {
    renderWithTheme(<DefaultDialog {...defaultProps} />);
    expect(screen.getByText(testDialogTitle)).toBeInTheDocument();
    expect(screen.getByText('Dialog Content')).toBeInTheDocument();
  });

  it('does not render the dialog when open is false', () => {
    renderWithTheme(<DefaultDialog {...defaultProps} open={false} />);
    expect(screen.queryByText(testDialogTitle)).not.toBeInTheDocument();
    expect(screen.queryByText('Dialog Content')).not.toBeInTheDocument();
  });

  it('renders the close icon', () => {
    renderWithTheme(<DefaultDialog {...defaultProps} />);
    expect(screen.getByTestId('close-button')).toBeInTheDocument();
  });

  it('calls onClose when close icon is clicked', () => {
    const onClose = vi.fn();
    renderWithTheme(<DefaultDialog {...defaultProps} onClose={onClose} />);
    fireEvent.click(screen.getByTestId('close-button'));
    expect(onClose).toHaveBeenCalled();
  });

  it('renders custom title node', () => {
    renderWithTheme(<DefaultDialog {...defaultProps} title={<span data-testid="custom-title">Custom Title</span>} />);
    expect(screen.getByTestId('custom-title')).toBeInTheDocument();
  });

  it('renders children correctly', () => {
    renderWithTheme(
      <DefaultDialog {...defaultProps}>
        <span data-testid="custom-content">Custom Content</span>
      </DefaultDialog>,
    );
    expect(screen.getByTestId('custom-content')).toBeInTheDocument();
  });
});
