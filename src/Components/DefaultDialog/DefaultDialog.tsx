import CloseIcon from '@mui/icons-material/Close';
import {Box, Dialog, DialogContent, DialogTitle, Tooltip} from '@mui/material';

export interface DefaultDialogProps {
  title: React.ReactNode;
  children: React.ReactNode;
  maxWidth?: number;
  onClose?: () => void;
  open: boolean;
}

/**
 * A reusable dialog component with a customizable title, content, and close button.
 *
 * @param {DefaultDialogProps} props - The properties for the DefaultDialog component.
 * @param {string | React.ReactNode} props.title - The title displayed at the top of the dialog.
 * @param {React.ReactNode} props.children - The content to be rendered inside the dialog.
 * @param {number | string} [props.maxWidth] - The maximum width of the dialog. Defaults to 400 if not provided.
 * @param {boolean} props.open - Controls whether the dialog is open or closed.
 * @param {() => void} props.onClose - Callback function invoked when the dialog is requested to be closed.
 *
 * @returns {JSX.Element} The rendered dialog component.
 */
export const DefaultDialog = ({title, children, maxWidth, open, onClose}: DefaultDialogProps) => {
  const drawTopSection = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          justifyItems: 'center',
        }}
      >
        <DialogTitle
          sx={{
            fontSize: '1rem',
            fontWeight: 700,
            color: 'body.dark',
            px: 2,
            pt: 2,
            pb: 0,
          }}
        >
          {title}
        </DialogTitle>
        <Tooltip title="Close">
          <Box
            data-testid="close-button"
            onClick={onClose}
            sx={{cursor: 'pointer', px: 2, py: 0, pt: 2, fontWeight: 700, alignItems: 'center', display: 'flex'}}
          >
            <CloseIcon sx={{color: theme => theme.palette.body.dark, padding: 0.5, opacity: onClose ? 1 : 0.1}} />
          </Box>
        </Tooltip>
      </Box>
    );
  };

  return (
    <Dialog
      sx={[
        {
          '& .MuiDialog-paper': {
            width: '100%',
            maxWidth: maxWidth ?? 400,
            margin: 0,
            borderRadius: 0.5,
          },
        },
      ]}
      open={open}
      onClose={onClose}
    >
      {drawTopSection()}
      <DialogContent sx={{px: 2, py: 2}}>{children}</DialogContent>
    </Dialog>
  );
};
