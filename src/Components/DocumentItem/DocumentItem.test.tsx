import {ThemeProvider, createTheme} from '@mui/material/styles';
import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {IFile} from 'redux/app/types';
import {vi} from 'vitest';
import DocumentItem from './DocumentItem';

// Mock the SVGImageFromPath component
vi.mock('../SVGImageFromPath', () => ({
  default: function MockSVGImageFromPath({sx}: {sx?: any}) {
    return <div data-testid="pdf-icon" style={sx} />;
  },
}));

// Create a test theme
const testTheme = createTheme({
  palette: {
    grey: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
    },
    text: {
      primary: '#111827',
      secondary: '#6b7280',
    },
  },
});

// Helper function to render component with theme
const renderWithTheme = (component: React.ReactElement) => {
  return render(<ThemeProvider theme={testTheme}>{component}</ThemeProvider>);
};

// Mock file data - note: source and size should be numbers but type definition has string index signature
const mockFile: IFile = {
  id: 'file-1',
  tenantId: 'tenant-123',
  fileKey: 'documents/test-file.pdf',
  originalName: 'Test Document.pdf',
  source: 1 as any, // Type workaround for conflicting type definition
  size: 1048576 as any, // 1 MB
  url: 'https://example.com/test-file.pdf',
};

const mockSmallFile: IFile = {
  id: 'file-2',
  tenantId: 'tenant-123',
  fileKey: 'documents/small-file.pdf',
  originalName: 'Small File.pdf',
  source: 1 as any,
  size: 512 as any, // 512 bytes
  url: 'https://example.com/small-file.pdf',
};

const mockLargeFile: IFile = {
  id: 'file-3',
  tenantId: 'tenant-123',
  fileKey: 'documents/large-file.pdf',
  originalName: 'Very Long File Name That Should Be Truncated With Ellipsis.pdf',
  source: 1 as any,
  size: 10485760 as any, // 10 MB
  url: 'https://example.com/large-file.pdf',
};

describe('DocumentItem', () => {
  describe('Rendering', () => {
    it('renders file name correctly', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);
      expect(screen.getByText('Test Document.pdf')).toBeInTheDocument();
    });

    it('renders file size in KB', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);
      expect(screen.getByText('1024 KB')).toBeInTheDocument();
    });

    it('renders PDF icon', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);
      expect(screen.getByTestId('pdf-icon')).toBeInTheDocument();
    });

    it('displays correct file information', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      expect(screen.getByText('Test Document.pdf')).toBeInTheDocument();
      expect(screen.getByText('1024 KB')).toBeInTheDocument();
    });

    it('renders with proper component structure', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Check that the document item container exists by looking for its content
      expect(screen.getByText('Test Document.pdf')).toBeInTheDocument();
      expect(screen.getByText('1024 KB')).toBeInTheDocument();

      // Verify the PDF icon is present using its test id
      expect(screen.getByTestId('pdf-icon')).toBeInTheDocument();
    });
  });

  describe('File size formatting', () => {
    it('formats small file size correctly', () => {
      renderWithTheme(<DocumentItem file={mockSmallFile} />);
      expect(screen.getByText('1 KB')).toBeInTheDocument(); // 512 bytes = 0.5 KB, rounded to 1 KB
    });

    it('formats large file size correctly', () => {
      renderWithTheme(<DocumentItem file={mockLargeFile} />);
      expect(screen.getByText('10240 KB')).toBeInTheDocument(); // 10 MB = 10240 KB
    });

    it('handles zero file size', () => {
      const zeroSizeFile: IFile = {
        ...mockFile,
        size: 0 as any,
        source: 1 as any,
      };
      renderWithTheme(<DocumentItem file={zeroSizeFile} />);
      expect(screen.getByText('0 KB')).toBeInTheDocument();
    });

    it('rounds file size correctly', () => {
      const fractionalFile: IFile = {
        ...mockFile,
        size: 1536 as any, // 1.5 KB
        source: 1 as any,
      };
      renderWithTheme(<DocumentItem file={fractionalFile} />);
      expect(screen.getByText('2 KB')).toBeInTheDocument(); // Math.round(1.5) = 2
    });
  });

  describe('Click handling', () => {
    it('calls onClick handler when clicked', () => {
      const mockOnClick = vi.fn();
      renderWithTheme(<DocumentItem file={mockFile} onClick={mockOnClick} />);

      // Click on the file name (which is inside the clickable container)
      fireEvent.click(screen.getByText('Test Document.pdf'));

      expect(mockOnClick).toHaveBeenCalledTimes(1);
      expect(mockOnClick).toHaveBeenCalledWith(mockFile);
    });

    it('does not throw error when clicked without onClick handler', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      expect(() => {
        fireEvent.click(screen.getByText('Test Document.pdf'));
      }).not.toThrow();
    });

    it('calls onClick multiple times when clicked multiple times', () => {
      const mockOnClick = vi.fn();
      renderWithTheme(<DocumentItem file={mockFile} onClick={mockOnClick} />);

      const fileName = screen.getByText('Test Document.pdf');
      fireEvent.click(fileName);
      fireEvent.click(fileName);
      fireEvent.click(fileName);

      expect(mockOnClick).toHaveBeenCalledTimes(3);
    });

    it('passes correct file object to onClick handler', () => {
      const mockOnClick = vi.fn();
      renderWithTheme(<DocumentItem file={mockLargeFile} onClick={mockOnClick} />);

      fireEvent.click(screen.getByText('Very Long File Name That Should Be Truncated With Ellipsis.pdf'));

      expect(mockOnClick).toHaveBeenCalledWith(mockLargeFile);
    });
  });

  describe('Styling and appearance', () => {
    it('renders with clickable container', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Check that content is rendered and clickable
      expect(screen.getByText('Test Document.pdf')).toBeInTheDocument();
      expect(screen.getByText('1024 KB')).toBeInTheDocument();

      // Should be clickable (no error when clicked)
      expect(() => fireEvent.click(screen.getByText('Test Document.pdf'))).not.toThrow();
    });

    it('has light background styling', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);
    });

    it('has proper component structure', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Should have Material-UI Box styling
    });

    it('displays PDF icon with correct styling', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      const pdfIcon = screen.getByTestId('pdf-icon');
      expect(pdfIcon).toBeInTheDocument();
    });

    it('has responsive width behavior', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Should render without layout issues
    });
  });

  describe('Text truncation and overflow', () => {
    it('handles long file names with proper text overflow', () => {
      renderWithTheme(<DocumentItem file={mockLargeFile} />);

      const fileName = screen.getByText('Very Long File Name That Should Be Truncated With Ellipsis.pdf');
      expect(fileName).toHaveStyle({
        overflow: 'hidden',
        textOverflow: 'ellipsis',
        whiteSpace: 'nowrap',
      });
    });

    it('displays full short file names without truncation', () => {
      renderWithTheme(<DocumentItem file={mockSmallFile} />);

      const fileName = screen.getByText('Small File.pdf');
      expect(fileName).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('provides interactive element', () => {
      const mockOnClick = vi.fn();
      renderWithTheme(<DocumentItem file={mockFile} onClick={mockOnClick} />);

      // Test that the component is interactive by clicking on the text
      fireEvent.click(screen.getByText('Test Document.pdf'));

      expect(mockOnClick).toHaveBeenCalled();
    });

    it('has proper semantic structure', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Check that file name and size are separate text elements
      expect(screen.getByText('Test Document.pdf')).toBeInTheDocument();
      expect(screen.getByText('1024 KB')).toBeInTheDocument();
    });

    it('provides meaningful text content', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Verify that both filename and size are accessible to screen readers
      const fileName = screen.getByText('Test Document.pdf');
      const fileSize = screen.getByText('1024 KB');

      expect(fileName).toBeVisible();
      expect(fileSize).toBeVisible();
    });
  });

  describe('Theme integration', () => {
    it('uses Material-UI theme components', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      const fileName = screen.getByText('Test Document.pdf');
      const fileSize = screen.getByText('1024 KB');

      // These elements should use Material-UI Typography components
      expect(fileName).toHaveClass('MuiTypography-root');
      expect(fileSize).toHaveClass('MuiTypography-root');
    });

    it('applies theme-based component styling', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Check that the component renders with proper Material-UI styling
      expect(screen.getByText('Test Document.pdf')).toBeInTheDocument();
      expect(screen.getByText('1024 KB')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-icon')).toBeInTheDocument();
    });
  });

  describe('Hover interactions', () => {
    it('changes appearance on hover', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Test that hover interactions work by hovering over the text element
      const fileName = screen.getByText('Test Document.pdf');
      fireEvent.mouseEnter(fileName);

      // The component should still render properly after hover
      expect(fileName).toBeInTheDocument();
    });

    it('maintains hover state properly', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      // Test hover state management
      const fileName = screen.getByText('Test Document.pdf');
      fireEvent.mouseEnter(fileName);
      fireEvent.mouseLeave(fileName);

      // Component should remain functional after hover interactions
      expect(fileName).toBeInTheDocument();
    });
  });

  describe('Edge cases', () => {
    it('handles file with empty name', () => {
      const emptyNameFile: IFile = {
        ...mockFile,
        originalName: '',
        source: 1 as any,
        size: 1048576 as any,
      };
      renderWithTheme(<DocumentItem file={emptyNameFile} />);

      // Should still render the component
      expect(screen.getByText('1024 KB')).toBeInTheDocument();
    });

    it('handles file with special characters in name', () => {
      const specialCharFile: IFile = {
        ...mockFile,
        originalName: 'File with special chars @#$%^&*().pdf',
        source: 1 as any,
        size: 1048576 as any,
      };
      renderWithTheme(<DocumentItem file={specialCharFile} />);

      expect(screen.getByText('File with special chars @#$%^&*().pdf')).toBeInTheDocument();
    });

    it('handles extremely large file size', () => {
      const hugeFile: IFile = {...mockFile, size: 1073741824 as any, source: 1 as any}; // 1 GB
      renderWithTheme(<DocumentItem file={hugeFile} />);

      expect(screen.getByText('1048576 KB')).toBeInTheDocument(); // 1 GB = 1048576 KB
    });

    it('handles file with minimal required properties', () => {
      const minimalFile: IFile = {
        id: 'min-file',
        tenantId: 'tenant',
        fileKey: 'key',
        originalName: 'min.pdf',
        source: 1 as any,
        size: 100 as any,
        url: 'url',
      };

      renderWithTheme(<DocumentItem file={minimalFile} />);

      expect(screen.getByText('min.pdf')).toBeInTheDocument();
      expect(screen.getByText('0 KB')).toBeInTheDocument(); // 100 bytes = 0.1 KB, rounded to 0
    });
  });

  describe('Component props validation', () => {
    it('renders correctly with all required props', () => {
      renderWithTheme(<DocumentItem file={mockFile} />);

      expect(screen.getByText('Test Document.pdf')).toBeInTheDocument();
      expect(screen.getByText('1024 KB')).toBeInTheDocument();
      expect(screen.getByTestId('pdf-icon')).toBeInTheDocument();
    });

    it('renders correctly with optional onClick prop', () => {
      const mockOnClick = vi.fn();
      renderWithTheme(<DocumentItem file={mockFile} onClick={mockOnClick} />);

      expect(screen.getByText('Test Document.pdf')).toBeInTheDocument();

      // Test that onClick works by clicking on the text
      fireEvent.click(screen.getByText('Test Document.pdf'));
      expect(mockOnClick).toHaveBeenCalled();
    });
  });
});
