import {Box, Typography, useTheme} from '@mui/material';
import {Integers} from 'Helpers/integers';
import React from 'react';
import {IFile} from 'redux/app/types';
import PdfIcon from '../../Assets/pdf.svg';
import SVGImageFromPath from '../SVGImageFromPath';

export interface DocumentItemProps {
  file: IFile;
  onClick?: (file: IFile) => void;
}

/**
 * DocumentItem component for displaying uploaded PDF files
 * Matches the Figma design exactly with proper styling
 */
const DocumentItem: React.FC<DocumentItemProps> = ({file, onClick}) => {
  const theme = useTheme();

  const handleClick = () => {
    if (onClick) {
      onClick(file);
    }
  };

  const formatFileSize = (sizeInBytes: number): string => `${Math.round(sizeInBytes / 1024)} KB`;

  return (
    <Box
      onClick={handleClick}
      sx={{
        display: 'flex',
        alignItems: 'center',
        gap: '0.75rem',
        padding: '0.75rem',
        backgroundColor: '#F7ECF1',
        // backgroundColor: theme.palette.grey[50],
        borderRadius: '0.5rem',
        cursor: 'pointer',
        border: `0.0625rem solid ${theme.palette.grey[Integers.TwoHundred]}`,
        transition: 'all 0.2s ease-in-out',
        minWidth: '14rem',
        maxWidth: '16rem',
        '&:hover': {
          backgroundColor: theme.palette.grey[Integers.OneHundred],
          borderColor: theme.palette.grey[Integers.ThreeHundred],
        },
      }}
    >
      {/* PDF Icon */}
      <Box
        sx={{
          width: '2.5rem',
          height: '2.5rem',
          borderRadius: '0.375rem',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          flexShrink: 0,
        }}
      >
        <SVGImageFromPath
          path={PdfIcon}
          sx={{
            width: '1.25rem',
            height: '1.25rem',
          }}
        />
      </Box>

      {/* File Info */}
      <Box sx={{flex: 1, minWidth: 0}}>
        <Typography
          sx={{
            fontSize: '0.875rem',
            fontWeight: 500,
            color: theme.palette.text.primary,
            lineHeight: 1.25,
            marginBottom: '0.25rem',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap',
          }}
        >
          {file.originalName}
        </Typography>
        <Typography
          sx={{
            fontSize: '0.75rem',
            color: 'body.500',
            lineHeight: 1,
          }}
        >
          {formatFileSize(file.size)}
        </Typography>
      </Box>
    </Box>
  );
};

export default DocumentItem;
