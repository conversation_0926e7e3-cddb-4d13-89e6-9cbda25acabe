// @vitest-environment jsdom
import {render, screen} from '@testing-library/react';
import {describe, expect, it} from 'vitest';
import EllipsisText from './EllipsisText';

describe('EllipsisText', () => {
  it('renders provided text', () => {
    render(<EllipsisText text="Hello World" />);
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });

  it('renders N/A if text is empty', () => {
    render(<EllipsisText text="" />);
    expect(screen.getByText('N/A')).toBeInTheDocument();
  });

  it('shows tooltip when overflowed', () => {
    // Mock the offsetWidth/scrollWidth to simulate overflow
    const originalGetBoundingClientRect = HTMLElement.prototype.getBoundingClientRect;
    HTMLElement.prototype.getBoundingClientRect = function () {
      return {width: 50, height: 20, top: 0, left: 0, bottom: 0, right: 0, x: 0, y: 0, toJSON: () => {}};
    };
    Object.defineProperty(HTMLElement.prototype, 'scrollWidth', {configurable: true, get: () => 100});
    Object.defineProperty(HTMLElement.prototype, 'clientWidth', {configurable: true, get: () => 50});

    render(<EllipsisText text="Overflowed Text" />);
    expect(screen.getByText('Overflowed Text')).toBeInTheDocument();

    // Clean up mocks
    HTMLElement.prototype.getBoundingClientRect = originalGetBoundingClientRect;
  });
});
