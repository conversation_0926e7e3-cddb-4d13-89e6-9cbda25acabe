import {Box, Typography} from '@mui/material';
import {MAX_FILE_SIZE, MAX_FILES} from 'Helpers/utils';
import {enqueueSnackbar} from 'notistack';
import {useState} from 'react';
import {Accept, DropEvent, DropzoneOptions, FileError, FileRejection, useDropzone} from 'react-dropzone';
import IconsFIleDrop from '../../Assets/filedropicon.svg';
/**
 * Props for the FileDropZone component.
 *
 * @property onDrop - Callback fired when files are dropped.
 * @property accept - Optional file types that are allowed.
 * @property dropzoneProps - Optional additional dropzone configuration.
 * @property files - List of currently uploaded files.
 */
interface IFileDropZoneProps {
  onDrop: <T extends File>(acceptedFiles: T[], fileRejections: FileRejection[], event: DropEvent) => void;
  accept?: Accept;
  dropzoneProps?: DropzoneOptions;
  files: File[];
}

/**
 * Formats bytes to megabytes as a string with "MB" suffix.
 * @param bytes - File size in bytes.
 * @returns Human-readable size in megabytes.
 */
const formatFileSize = (bytes: number): string => (bytes / (1024 * 1024)).toFixed(2) + ' MB';

/**
 * Returns a user-friendly error message based on the file rejection error code.
 *
 * @param error - File error object.
 * @param maxFiles - Maximum number of allowed files.
 * @returns A string message describing the error.
 */
const getErrorMessage = (error: FileError, maxFiles: number): string => {
  if (error.code === 'file-too-large') {
    return `File is larger than ${formatFileSize(MAX_FILE_SIZE)}`;
  }
  if (error.code === 'too-many-files') {
    return `File limit exceeded only ${maxFiles} files can be uploaded at once`;
  }
  return error.message;
};

/**
 * Groups file rejection errors by type and returns an array of formatted error messages.
 *
 * @param fileRejections - Array of rejected files and their errors.
 * @param maxFiles - Maximum file limit.
 * @returns A list of formatted error strings.
 */
const processFileRejections = (fileRejections: FileRejection[], maxFiles: number): string[] => {
  const errorMap = new Map<string, string[]>();

  fileRejections.forEach(({errors, file}) => {
    errors.forEach(error => {
      const errorMessage = getErrorMessage(error, maxFiles);
      if (!errorMap.has(errorMessage)) {
        errorMap.set(errorMessage, []);
      }
      errorMap.get(errorMessage)!.push(file.name);
    });
  });

  return Array.from(errorMap.entries()).map(([errorMsg, files]) => {
    return errorMsg === `File is larger than ${formatFileSize(MAX_FILE_SIZE)}`
      ? `${errorMsg} for ${files.length} files`
      : `${errorMsg}`;
  });
};

/**
 * Renders the dropzone instructions or prompt UI.
 *
 * @param props.isDragActive - Indicates if a file is being dragged over the dropzone.
 */
const DropZoneContent: React.FC<{isDragActive: boolean}> = ({isDragActive}) => {
  if (isDragActive) {
    return (
      <Typography sx={{color: 'secondary.main', fontWeight: 700}} data-testid="drop-zone">
        Drop the files here.
      </Typography>
    );
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        fontSize: '1.5rem',
      }}
    >
      <Box
        component="img"
        src={IconsFIleDrop}
        alt="file"
        sx={{
          width: '2.5rem',
          height: '2.5rem',
          objectFit: 'contain',
        }}
      />
      <Typography sx={{color: 'body.dark', fontWeight: 500, fontSize: 16}}>
        Drag & drop or browse files from here
      </Typography>
      <Typography sx={{color: 'body.600', fontSize: 14}}>Supported format: Pdf</Typography>
    </Box>
  );
};

/**
 * Renders a list of validation error messages below the dropzone.
 *
 * @param props.messages - Array of error strings.
 */
const ErrorMessages: React.FC<{messages: string[]}> = ({messages}) => {
  if (messages.length === 0) return null;

  return (
    <Box mt={2}>
      {messages.map(error => (
        <Typography key={error} sx={{color: 'error.main', fontSize: '0.875rem'}}>
          {error}
        </Typography>
      ))}
    </Box>
  );
};

/**
 * FileDropZone component provides a drag-and-drop area to upload files.
 *
 * @component
 * @param {IFileDropZoneProps} props - Component props.
 * @returns A styled file dropzone component with error messaging.
 */
const FileDropZone: React.FC<IFileDropZoneProps> = ({onDrop, accept, dropzoneProps, files}) => {
  const [errorMessages, setErrorMessages] = useState<string[]>([]);
  const {maxSize = MAX_FILE_SIZE, maxFiles = MAX_FILES, multiple = true, ...otherDropzoneProps} = dropzoneProps ?? {};

  /**
   * Handles drop event, updates errors and invokes parent onDrop handler.
   */
  const handleDrop = (acceptedFiles: File[], fileRejections: FileRejection[], event: DropEvent) => {
    const newErrorMessages = processFileRejections(fileRejections, maxFiles);
    setErrorMessages(newErrorMessages);
    onDrop(acceptedFiles, fileRejections, event);
  };

  /**
   * Prevents opening file dialog if file limit is exceeded.
   */
  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    if (!multiple && files.length >= 1) {
      event.preventDefault();
      enqueueSnackbar(`Only ${maxFiles} file${maxFiles > 1 ? 's' : ''} can be uploaded at once.`, {variant: 'error'});
    }
  };

  const {getRootProps, getInputProps, isDragActive} = useDropzone({
    onDrop: handleDrop,
    accept,
    maxSize,
    maxFiles,
    disabled: !multiple && files.length >= 1,
    ...otherDropzoneProps,
  });

  // Override rootProps to inject custom click handler logic
  const baseRootProps = getRootProps();
  const rootProps = {
    ...baseRootProps,
    onClick: (event: React.MouseEvent<HTMLElement>) => {
      handleClick(event);
      baseRootProps.onClick?.(event);
    },
  };

  return (
    <Box
      flex={1}
      p={2}
      sx={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        borderRadius: 1,
        backgroundColor: 'white.100',
        borderColor: 'body.100',
        height: '100%',
        boxSizing: 'border-box',
        flex: 1,
      }}
      border="0.125rem dashed"
      borderColor="secondary.border"
      {...rootProps}
      data-testid="file-drop-zone"
    >
      <input {...getInputProps()} data-testid="file-drop-zone-input" />
      <DropZoneContent isDragActive={isDragActive} />
      <ErrorMessages messages={errorMessages} />
    </Box>
  );
};

export default FileDropZone;
