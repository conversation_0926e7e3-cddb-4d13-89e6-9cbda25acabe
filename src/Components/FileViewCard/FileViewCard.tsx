import CloseRoundedIcon from '@mui/icons-material/CloseRounded';
import {Box, IconButton, Typography} from '@mui/material';
import {IFile} from 'types';
import fileIcon from '../../Assets/file.svg'; // Make sure path is correct
import {getFileSize} from './utils';

interface IFileViewCardProps {
  fileDetail: File | IFile;
  handleRemoveFile?: (fileName: string) => void;
  handleDownload?: () => void;
  cancelPermissions?: string[];
}
/**
 * A card component for displaying file details such as name, size, and an icon.
 * Optionally allows file removal and download actions.
 *
 * @component
 * @param {IFileViewCardProps} props - The props for FileViewCard.
 * @param {File | IFile} props.fileDetail - The file object containing details to display.
 * @param {(fileName: string) => void} [props.handleRemoveFile] - Optional callback to remove the file.
 *
 * @returns {JSX.Element} The rendered file view card.
 */
const FileViewCard: React.FC<IFileViewCardProps> = ({fileDetail, handleRemoveFile}) => {
  const fileName = (fileDetail as File).name ?? (fileDetail as IFile).originalName;
  const onFileRemove = () => {
    handleRemoveFile?.(fileName);
  };
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        gap: 1,
        width: '100%',
        border: 1,
        backgroundColor: 'background.secondaryDark',
        borderRadius: '0.225rem',
        borderColor: 'body.100',
        padding: '0.125rem 1rem',
      }}
      data-testid="file-view-card"
    >
      <Box
        component="img"
        src={fileIcon}
        alt="file"
        sx={{
          width: '2rem',
          height: '2rem',
          objectFit: 'contain',
        }}
      />

      <Box sx={{display: 'flex', flexDirection: 'column', flexGrow: 1}}>
        <Box sx={{display: 'flex', flexDirection: 'row', justifyContent: 'space-between'}}>
          <Box sx={{alignItems: 'center'}}>
            <Typography
              sx={{
                fontSize: '0.85rem',
                fontWeight: 'bold',
                paddingTop: 1,
                paddingLeft: 0.5,
                wordBreak: 'break-word', // Wrap on word boundaries
                overflowWrap: 'anywhere', // Allow breaking anywhere if needed
              }}
            >
              {fileName}
            </Typography>

            <Typography sx={{fontSize: '0.65rem', color: 'body.500', paddingBottom: 1, paddingLeft: 0.5}}>
              {getFileSize(fileDetail.size)}
            </Typography>
          </Box>

          <Box sx={{display: 'flex', flexDirection: 'row', gap: 1, flexGrow: 1, justifyContent: 'flex-end'}}>
            {handleRemoveFile && (
              <IconButton onClick={onFileRemove} data-testid="file-remove-btn">
                <CloseRoundedIcon sx={{color: 'secondary.linkBreadcrumb'}} />
              </IconButton>
            )}
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default FileViewCard;
