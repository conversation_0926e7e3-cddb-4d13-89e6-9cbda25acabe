// @vitest-environment jsdom
import {describe, expect, it} from 'vitest';
import {getFileSize} from './utils';

describe('getFileSize', () => {
  it('returns MB for size >= 1048576', () => {
    expect(getFileSize(1048576)).toBe('1.00 MB');
    expect(getFileSize(2097152)).toBe('2.00 MB');
    expect(getFileSize(10485760)).toBe('10.00 MB');
    expect(getFileSize(1048576 + 524288)).toBe('1.50 MB');
  });

  it('returns KB for size >= 1024 and < 1048576', () => {
    expect(getFileSize(1024)).toBe('1.00 KB');
    expect(getFileSize(2048)).toBe('2.00 KB');
    expect(getFileSize(1048575)).toBe((1048575 / 1024).toFixed(2) + ' KB');
    expect(getFileSize(1536)).toBe('1.50 KB');
  });

  it('returns B for size < 1024', () => {
    expect(getFileSize(0)).toBe('0 B');
    expect(getFileSize(1)).toBe('1 B');
    expect(getFileSize(512)).toBe('512 B');
    expect(getFileSize(1023)).toBe('1023 B');
  });
});
