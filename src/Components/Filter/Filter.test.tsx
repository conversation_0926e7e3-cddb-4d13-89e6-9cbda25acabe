// @vitest-environment jsdom
import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {vi} from 'vitest';
import {DateRangeOptions} from '../DateRangeFilter/DateRangeFilter';
import Filter from './Filter';

// Mocks for child components
vi.mock('../AppBar/PopoverWithArrow', () => ({
  __esModule: true,
  PopoverWithArrow: (props: any) => <div data-testid="popover">{props.children}</div>,
}));
vi.mock('../BlueButton/BlueButton', () => ({
  __esModule: true,
  default: (props: any) => (
    <button data-testid="blue-button" onClick={props.onClick}>
      {props.children}
    </button>
  ),
}));
vi.mock('../BorderButton/BorderButton', () => ({
  __esModule: true,
  default: (props: any) => (
    <button data-testid="border-button" onClick={props.onClick}>
      {props.children}
    </button>
  ),
}));
vi.mock('../DateRangeFilter/DateRangeFilter', async importOriginal => {
  const actual = await importOriginal();
  const actualObj = actual && typeof actual === 'object' ? actual : {};
  return {
    ...actualObj,
    default: React.forwardRef((_props: any, ref: any) => {
      React.useImperativeHandle(ref, () => ({
        clearSelection: vi.fn(),
      }));
      return <div data-testid="date-range-filter" />;
    }),
  };
});
vi.mock('../DefaultDialog/DefaultCloseIcon', () => ({
  __esModule: true,
  default: () => <span data-testid="close-icon">X</span>,
}));

const MockFilterStatusChips = React.forwardRef((_props: any, ref: any) => {
  React.useImperativeHandle(ref, () => ({
    clearSelection: vi.fn(),
  }));
  return <div data-testid="status-chips" />;
});
MockFilterStatusChips.displayName = 'MockFilterStatusChips';

describe('Filter', () => {
  const onClose = vi.fn();
  const onFilterChange = vi.fn();

  const defaultProps = {
    open: true,
    onClose,
    FilterStatusChips: MockFilterStatusChips,
    value: {
      status: new Set(['active']),
      dateRange: {
        dateRangeOption: DateRangeOptions.CUSTOM,
        startDate: new Date('2023-01-01'),
        endDate: new Date('2023-01-31'),
        from: new Date('2023-01-01'),
        to: new Date('2023-01-31'),
      },
    },
    onFilterChange,
    anchorEl: document.createElement('div'),
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders filter popover and sections', () => {
    render(<Filter {...defaultProps} />);
    expect(screen.getByTestId('popover')).toBeInTheDocument();
    expect(screen.getByTestId('status-chips')).toBeInTheDocument();
    expect(screen.getByTestId('date-range-filter')).toBeInTheDocument();
    expect(screen.getByText('Filter')).toBeInTheDocument();
    expect(screen.getByText('Clear all')).toBeInTheDocument();
    expect(screen.getByTestId('close-icon')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('close-icon').parentElement!);
    expect(onClose).toHaveBeenCalled();
  });

  it('calls onFilterChange and onClose when Apply is clicked', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByTestId('blue-button'));
    expect(onFilterChange).toHaveBeenCalled();
    expect(onClose).toHaveBeenCalled();
  });

  it('calls clearSelection on children when Clear all is clicked', () => {
    render(<Filter {...defaultProps} />);
    fireEvent.click(screen.getByText('Clear all'));
    // No assertion here, just coverage for imperative handle
  });
});
