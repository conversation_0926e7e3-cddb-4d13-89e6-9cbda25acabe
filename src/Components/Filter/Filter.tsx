import {Box, Divider, IconButton, Typography} from '@mui/material';
import {useCallback, useEffect, useRef, useState} from 'react';
import {PopoverWithArrow} from '../AppBar/PopoverWithArrow';
import BlueButton from '../BlueButton/BlueButton';
import BorderButton from '../BorderButton/BorderButton';
import DateRangeFilter, {DateRangeFilterSelectedDate} from '../DateRangeFilter/DateRangeFilter';
import DefaultCloseIcon from '../DefaultDialog/DefaultCloseIcon';
import {IClearFilters} from '../FilterUtil/IClearFilters';

export interface IFilter {
  status: Set<string>;
  dateRange: DateRangeFilterSelectedDate | undefined;
}

interface FilterStatusChipsProps {
  value?: Set<string>;
  onSelect?: (selected: Set<string>) => void;
}

interface FilterProps {
  open: boolean;
  onClose: () => void;
  FilterStatusChips: React.ForwardRefExoticComponent<React.RefAttributes<IClearFilters> & FilterStatusChipsProps>;
  value?: IFilter;
  anchorEl?: HTMLElement | null;
  onFilterChange?: (filter: IFilter) => void;
}

const paddingDefault = 2; // Default padding for the popover
const buttonHeight = '2.3125rem'; // Height for the buttons
const VALID_DATE_RANGE_ERROR = 'Please select a valid date range.';

/**
 * Filter component provides a UI for filtering data based on status and date range.
 *
 * @component
 * @param {FilterProps} props - The props for the Filter component.
 * @param {boolean} props.open - Controls whether the filter popover is open.
 * @param {object} props.value - The current filter values, including status and date range.
 * @param {HTMLElement | null} props.anchorEl - The element to which the popover is anchored.
 * @param {(filter: { status: Set<string>; dateRange?: DateRangeFilterSelectedDate }) => void} [props.onFilterChange] - Callback invoked when the filter is applied.
 * @param {() => void} props.onClose - Callback invoked when the filter popover is closed.
 * @param {React.ForwardRefExoticComponent<any>} props.FilterStatusChips - Component for selecting status filters, supporting ref and onSelect.
 *
 * @remarks
 * - The component uses internal state to manage selected status and date range.
 * - Provides "Apply", "Close", and "Clear all" actions for user interaction.
 * - Uses refs to clear selections in child filter components.
 * - Renders a popover with filter controls and action buttons.
 */
const Filter: React.FC<FilterProps> = props => {
  type FilterStatusChipsRef = IClearFilters;

  const {open, value: filter, anchorEl, onFilterChange, onClose, FilterStatusChips} = props;
  const [selectedStatus, setSelectedStatus] = useState<Set<string>>(filter?.status ?? new Set());
  const [selectedRange, setSelectedRange] = useState<DateRangeFilterSelectedDate | undefined>(
    filter?.dateRange ?? undefined,
  );
  const [dateRangeError, setDateRangeError] = useState<string | undefined>();
  const filterStatusChipsRef = useRef<FilterStatusChipsRef>(null);
  const filterDateRangeRef = useRef<IClearFilters>(null);

  useEffect(() => {
    if (open) {
      setSelectedStatus(filter?.status ?? new Set());
      setSelectedRange(filter?.dateRange);
    }
  }, [open]);

  const applyHandle = () => {
    let hasError = false;
    if (selectedRange && [selectedRange?.startDate, selectedRange?.endDate].some(d => !d)) {
      setDateRangeError(VALID_DATE_RANGE_ERROR);
      hasError = true;
    }

    if (hasError) return;
    onFilterChange?.({
      status: new Set(selectedStatus),
      dateRange: selectedRange,
    });
    onClose();
  };

  const handleClearSelection = useCallback(() => {
    filterStatusChipsRef.current?.clearSelection();
    filterDateRangeRef.current?.clearSelection();
  }, []);

  const buildButtonSection = () => {
    return (
      <Box sx={{display: 'flex', justifyContent: 'flex-end', p: paddingDefault, pt: 1, gap: 1}}>
        <BorderButton sx={{height: buttonHeight}} onClick={onClose}>
          Close
        </BorderButton>
        <BlueButton onClick={applyHandle} sx={{height: buttonHeight}}>
          Apply
        </BlueButton>
      </Box>
    );
  };
  const buildHeaderSection = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: paddingDefault,
          pb: 0,
        }}
      >
        <Typography>Filter</Typography>

        {showClearButton && (
          <Box
            onClick={handleClearSelection}
            sx={{
              ml: 'auto',
              p: 0,
              backgroundColor: 'transparent',
              color: 'secondary.main',
              fontWeight: 600,
              fontSize: '0.75rem',
              cursor: 'pointer',
              textDecoration: ' underline',
            }}
          >
            Clear all
          </Box>
        )}

        <IconButton onClick={onClose} size="small" sx={{padding: 0}}>
          <DefaultCloseIcon />
        </IconButton>
      </Box>
    );
  };

  const showClearButton = selectedStatus.size > 0 || selectedRange;

  return (
    <>
      {/* Popper sits above backdrop */}
      <PopoverWithArrow
        id="filter-popper"
        disablePortal={false}
        elevation={2}
        open={open}
        anchorEl={anchorEl}
        transformHOrigin={{
          horizontal: 'center',
        }}
        onClose={onClose}
      >
        <Box sx={{display: 'flex', flexDirection: 'column', gap: 1, width: 380}}>
          {buildHeaderSection()}
          <Divider />
          <Box sx={{padding: paddingDefault, pt: 0, display: 'flex', flexDirection: 'column', gap: 1}}>
            <FilterStatusChips ref={filterStatusChipsRef} onSelect={setSelectedStatus} value={selectedStatus} />
            <DateRangeFilter
              ref={filterDateRangeRef}
              onSelect={setSelectedRange}
              value={selectedRange}
              error={dateRangeError}
            />
          </Box>
          <Divider />
          {buildButtonSection()}
        </Box>
      </PopoverWithArrow>
    </>
  );
};

export default Filter;
