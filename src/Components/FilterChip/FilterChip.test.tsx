import {fireEvent, render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import FilterChip from './FilterChip';

// Constants
enum TestValues {
  LABEL = 'Test Chip',
  FONT_SIZE = '0.75rem',
  CUSTOM_MARGIN = '8px',
  CUSTOM_PADDING = '16px',
  CUSTOM_BACKGROUND = 'purple',
}

describe('FilterChip', () => {
  it('should render with label', () => {
    render(<FilterChip label={TestValues.LABEL} />);
    expect(screen.getByText(TestValues.LABEL)).toBeInTheDocument();
  });

  it('should call onClick handler when clicked', () => {
    const handleClick = vi.fn();
    render(<FilterChip label={TestValues.LABEL} onClick={handleClick} />);

    fireEvent.click(screen.getByText(TestValues.LABEL));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  it('should call onDelete handler when delete icon is clicked', () => {
    const handleDelete = vi.fn();
    render(<FilterChip label={TestValues.LABEL} selected onDelete={handleDelete} />);

    const deleteIcon = screen.getByTestId('ClearIcon');
    fireEvent.click(deleteIcon);
    expect(handleDelete).toHaveBeenCalledTimes(1);
  });

  it('should not show delete icon when not selected even if onDelete is provided', () => {
    render(<FilterChip label={TestValues.LABEL} onDelete={vi.fn()} />);
    expect(screen.queryByTestId('ClearIcon')).not.toBeInTheDocument();
  });
});
