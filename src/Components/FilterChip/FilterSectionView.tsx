import {Box} from '@mui/material';

const FilterSectionView: React.FC<{children: React.ReactNode; title: string; sx?: React.CSSProperties}> = ({
  children,
  title,
  sx,
}) => {
  return (
    <Box
      sx={{
        color: 'body.800',
        fontSize: '0.8125rem',
        fontWeight: 700,
        display: 'flex',
        flexDirection: 'column',
        gap: 0.75,
        ...sx,
      }}
    >
      {title}
      {children}
    </Box>
  );
};

export default FilterSectionView;
