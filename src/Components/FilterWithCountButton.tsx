import {Box} from '@mui/material';
import FilterIcon from 'Assets/tenant-filter-icon.svg';
import {forwardRef} from 'react';
import BorderButton from './BorderButton/BorderButton';
import SVGImageFromPath from './SVGImageFromPath';
const buttonHeight = '2.375rem';

interface FilterWithCountButtonProps {
  filterSize: number;
  onClick: () => void;
}

const FilterWithCountButton = forwardRef<HTMLElement, FilterWithCountButtonProps>(({filterSize, onClick}, ref) => {
  const hasFilters = filterSize > 0;
  return (
    <Box ref={ref} sx={{position: 'relative', display: 'inline-block'}}>
      <BorderButton
        sx={{
          height: buttonHeight,
          minWidth: buttonHeight,
          p: 0,
          borderColor: filterSize > 0 ? 'secondary.main' : 'body.100',
        }}
        onClick={onClick}
      >
        <SVGImageFromPath path={FilterIcon} sx={{width: '0.8125rem', color: 'body.800'}} />
      </BorderButton>
      {/* Counter */}
      {hasFilters && (
        <Box
          sx={{
            position: 'absolute',
            top: '-15%',
            right: '-15%',
            backgroundColor: 'primary.main',
            color: '#fff',
            borderRadius: '50%',
            minWidth: 18,
            height: 18,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.75rem',
            fontWeight: 700,
            zIndex: 1,
            boxShadow: 1,
            px: 0.5,
          }}
        >
          {filterSize}
        </Box>
      )}
    </Box>
  );
});

FilterWithCountButton.displayName = 'FilterWithCountButton';

export default FilterWithCountButton;
