'use client';

import {Box, FormControl, FormHelperText, MenuItem, Select, type SelectProps} from '@mui/material';
import InputLabel from 'Components/InputLabel';
import type React from 'react';
import {memo, useCallback, useState} from 'react';

/**
 * Represents an object with arbitrary keys and unknown values,
 * used to type error objects.
 */
interface AnyErrorObj {
  [key: string]: unknown;
}

/**
 * Defines the structure of a single option in the select input.
 */
export interface SelectOption {
  /** The value of the option, used for form submission. */
  value: string;
  /** The label of the option, shown in the dropdown. */
  label: string;
}

/**
 * Props for the `SelectInput` component.
 */
export type SelectInputProps = Omit<SelectProps, 'onChange'> & {
  /** The ID of the select input, used for accessibility. */
  id: string;
  /** The label to display above the input. */
  label?: string;
  /** Whether to enable a copy-to-clipboard button. */
  copyEnabled?: boolean;
  /** Error message to display below the input. */
  errorMessage?: string | AnyErrorObj;
  /** React node to display at the start of the input. */
  startAdornment?: React.ReactNode;
  /** React node to display at the end of the input. */
  endAdornment?: React.ReactNode;
  /** Called when the selected value changes. */
  onChange?: (val: string) => void;
  /** Optional helper text displayed below the input. */
  helperText?: string;
  /** Array of options to render in the dropdown. */
  options: SelectOption[];
  /** Placeholder shown when no option is selected. */
  placeholder?: string;
  /** Optional styles for the label. */
  labelSx?: object;
  /** Optional styles for the input. */
  inputSx?: object;
  /** Optional custom renderer for each option. */
  renderOption?: (option: SelectOption) => React.ReactNode;
  /**
   * Optional custom renderer for the selected value.
   */
  renderValue?: (selected: string, options: SelectOption[]) => React.ReactNode;
  /**
   * Placement of the dropdown menu: 'bottom' (default) or 'top'
   */
  menuPlacement?: 'bottom' | 'top';

  placeholderSx?: React.CSSProperties;
  /**
   * to optionally render no items found message
   * @returns
   */
  noItemRenderer?: () => React.ReactNode;
};

const blackMain = 'black.main';

/**
 * A styled dropdown component based on MUI's Select,
 * supporting error states, adornments, copy functionality,
 * and custom placeholder handling.
 *
 * @param props - Props for the SelectInput component.
 * @returns The rendered SelectInput component.
 */
const SelectInput: React.FC<SelectInputProps> = ({
  id,
  value,
  label,
  helperText,
  disabled = false,
  errorMessage,
  onChange,
  options,
  placeholder,
  labelSx,
  inputSx,
  renderOption,
  renderValue: customRenderValue,
  menuPlacement = 'bottom',
  placeholderSx,
  noItemRenderer,
  ...rest
}) => {
  const isError = !!errorMessage;
  const [isOpen, setIsOpen] = useState(false);

  /**
   * Handles value change in the select and calls the `onChange` prop.
   */
  const handleChangeEvent = useCallback(
    (event: React.ChangeEvent<{value: unknown}> | (Event & {target: {value: unknown; name: string}})) => {
      if (onChange) {
        const value = (event.target as {value: unknown}).value;

        let stringValue = '';
        if (typeof value === 'string') {
          stringValue = value;
        } else if (typeof value === 'number' || typeof value === 'boolean') {
          stringValue = value.toString();
        } else {
          // DO NOTHING
        }

        onChange(stringValue);
      }
    },
    [onChange],
  );

  const handleOpen = () => setIsOpen(true);
  const handleClose = () => setIsOpen(false);

  const renderErrorOrHelper = () =>
    (isError || helperText) && <FormHelperText>{isError ? <>{errorMessage}</> : helperText}</FormHelperText>;

  return (
    <Box
      sx={{
        position: 'relative',
        transition: 'min-height 0.3s ease-in-out',
        zIndex: isOpen ? 1300 : 'auto',
      }}
    >
      <FormControl sx={{width: 1}} data-testid="selectFormControl" error={isError} disabled={disabled}>
        {label && (
          <InputLabel
            htmlFor={id}
            sx={{
              ...labelSx,
              color: blackMain,
              '&.Mui-focused': {color: blackMain},
              '&.MuiFormLabel-root': {color: blackMain},
              fontSize: 17,
            }}
          >
            {label}
          </InputLabel>
        )}
        <Select
          disabled={disabled}
          data-testid="select"
          value={value || ''}
          id={id}
          displayEmpty
          open={isOpen}
          onOpen={handleOpen}
          onClose={handleClose}
          sx={{
            '& .MuiOutlinedInput-notchedOutline': {
              borderColor: 'body.100',
            },
            '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
              borderColor: 'body.100',
            },
            ...inputSx,
          }}
          MenuProps={{
            PaperProps: {
              sx: {
                maxHeight: 280,
                width: 'auto',
                minWidth: '100%',
                maxWidth: '25rem',
                ...(menuPlacement === 'top' ? {mt: '-0.5rem'} : {}),
              },
            },
            anchorOrigin: {
              vertical: menuPlacement === 'top' ? 'top' : 'bottom',
              horizontal: 'left',
            },
            transformOrigin: {
              vertical: menuPlacement === 'top' ? 'bottom' : 'top',
              horizontal: 'left',
            },
            disablePortal: false,
          }}
          onChange={handleChangeEvent}
          renderValue={(selected: unknown): React.ReactNode => {
            if (!selected) {
              return (
                <Box component="span" sx={{color: 'body.300', ...placeholderSx}}>
                  {placeholder}
                </Box>
              );
            }
            if (customRenderValue && typeof selected === 'string') {
              return customRenderValue(selected, options);
            }
            const option = options.find(opt => opt.value === selected);
            return option ? option.label : String(selected); //NOSONAR
          }}
          {...rest}
        >
          {options.length === 0 && noItemRenderer?.()}
          {options.map(option => (
            <MenuItem
              key={option.value}
              value={option.value}
              sx={{
                height: renderOption ? '3.90rem' : '2.75rem', // 44px
                opacity: 1,
                padding: '0.875rem', // 14px
                gap: '0.5rem', // 8px
                borderBottom: '0.0625rem solid #e0e0e0', // 1px
                '&:last-child': {
                  borderBottom: 'none',
                },
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
              }}
            >
              {renderOption ? renderOption(option) : option.label}
            </MenuItem>
          ))}
        </Select>
        {renderErrorOrHelper()}
      </FormControl>
    </Box>
  );
};

export default memo(SelectInput);
