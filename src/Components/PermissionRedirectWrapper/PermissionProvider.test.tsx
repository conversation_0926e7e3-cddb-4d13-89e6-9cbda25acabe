import {screen} from '@testing-library/react';
import {useAppSelector} from 'redux/hooks';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, Mock, vi} from 'vitest';
import {PermissionProvider, usePermissions} from './PermissionProvider';

// Mock redux hooks and selectors
vi.mock('redux/hooks', () => ({
  useAppSelector: vi.fn(),
}));
vi.mock('redux/auth/authSlice', () => ({
  selectCurrentAuthState: vi.fn(),
}));

const TestComponent = () => {
  const {hasPermission, isSuperAdmin, permissions, initialized} = usePermissions();
  return (
    <div>
      <span data-testid="super-admin">{isSuperAdmin ? 'yes' : 'no'}</span>
      <span data-testid="initialized">{initialized ? 'yes' : 'no'}</span>
      <span data-testid="perm">{hasPermission('read') ? 'allowed' : 'denied'}</span>
      <span data-testid="perm-list">{Array.from(permissions).join(',')}</span>
    </div>
  );
};

describe('PermissionProvider', () => {
  it('provides permissions from token payload', () => {
    // Create a fake JWT payload with permissions
    const payload = {permissions: ['read', 'write']};
    const base64Payload = btoa(JSON.stringify(payload));
    (useAppSelector as Mock).mockReturnValue({accessToken: `header.${base64Payload}.signature`});

    renderWithTheme(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>,
    );

    expect(screen.getByTestId('perm-list').textContent).toContain('read');
    expect(screen.getByTestId('perm-list').textContent).toContain('write');
    expect(screen.getByTestId('perm').textContent).toBe('allowed');
    expect(screen.getByTestId('super-admin').textContent).toBe('no');
    expect(screen.getByTestId('initialized').textContent).toBe('yes');
  });

  it('detects super admin permission', () => {
    const payload = {permissions: ['*']};
    const base64Payload = btoa(JSON.stringify(payload));
    (useAppSelector as Mock).mockReturnValue({accessToken: `header.${base64Payload}.signature`});

    renderWithTheme(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>,
    );

    expect(screen.getByTestId('super-admin').textContent).toBe('yes');
    expect(screen.getByTestId('perm').textContent).toBe('allowed');
  });

  it('handles invalid token gracefully', () => {
    // Test with a token that has 3 components but invalid base64 payload
    (useAppSelector as Mock).mockReturnValue({accessToken: 'header.invalidbase64payload.signature'});

    renderWithTheme(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>,
    );

    // When token parsing fails, permissions should be empty set
    expect(screen.getByTestId('perm-list').textContent).toBe('');
    expect(screen.getByTestId('super-admin').textContent).toBe('no');
    expect(screen.getByTestId('initialized').textContent).toBe('yes');
    expect(screen.getByTestId('perm').textContent).toBe('denied');
  });

  it('handles token with wrong component length', () => {
    (useAppSelector as Mock).mockReturnValue({accessToken: 'justonecomponent'});
    renderWithTheme(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>,
    );
    // Permissions should be empty and isSuperAdmin should be false
    expect(screen.getByTestId('perm-list').textContent).toBe('');
    expect(screen.getByTestId('super-admin').textContent).toBe('no');
    expect(screen.getByTestId('initialized').textContent).toBe('yes');
    expect(screen.getByTestId('perm').textContent).toBe('denied');
  });

  it('handles null or undefined access token', () => {
    (useAppSelector as Mock).mockReturnValue({accessToken: null});
    renderWithTheme(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>,
    );

    expect(screen.getByTestId('perm-list').textContent).toBe('');
    expect(screen.getByTestId('super-admin').textContent).toBe('no');
    expect(screen.getByTestId('initialized').textContent).toBe('yes');
    expect(screen.getByTestId('perm').textContent).toBe('denied');
  });

  it('handles empty access token', () => {
    (useAppSelector as Mock).mockReturnValue({accessToken: ''});
    renderWithTheme(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>,
    );

    expect(screen.getByTestId('perm-list').textContent).toBe('');
    expect(screen.getByTestId('super-admin').textContent).toBe('no');
    expect(screen.getByTestId('initialized').textContent).toBe('yes');
    expect(screen.getByTestId('perm').textContent).toBe('denied');
  });

  it('handles token with valid structure but malformed JSON payload', () => {
    // Create a token with 3 components but the middle one is not valid JSON
    const invalidJsonPayload = btoa('{"permissions": [invalid json}');
    (useAppSelector as Mock).mockReturnValue({accessToken: `header.${invalidJsonPayload}.signature`});

    renderWithTheme(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>,
    );

    expect(screen.getByTestId('perm-list').textContent).toBe('');
    expect(screen.getByTestId('super-admin').textContent).toBe('no');
    expect(screen.getByTestId('initialized').textContent).toBe('yes');
    expect(screen.getByTestId('perm').textContent).toBe('denied');
  });

  it('handles token with valid JSON but missing permissions field', () => {
    const payloadWithoutPermissions = {userId: '123', name: 'test'};
    const base64Payload = btoa(JSON.stringify(payloadWithoutPermissions));
    (useAppSelector as Mock).mockReturnValue({accessToken: `header.${base64Payload}.signature`});

    renderWithTheme(
      <PermissionProvider>
        <TestComponent />
      </PermissionProvider>,
    );

    // Should default to empty permissions array when permissions field is missing
    expect(screen.getByTestId('perm-list').textContent).toBe('');
    expect(screen.getByTestId('super-admin').textContent).toBe('no');
    expect(screen.getByTestId('initialized').textContent).toBe('yes');
    expect(screen.getByTestId('perm').textContent).toBe('denied');
  });
});
