import React, {createContext, useContext, useEffect, useMemo, useState} from 'react';
import {selectCurrentAuthState} from 'redux/auth/authSlice';
import {useAppSelector} from 'redux/hooks';

// ---- Types ----
type PermissionContextType = {
  hasPermission: (perm: string) => boolean;
  isSuperAdmin: boolean;
  permissions: Set<string>;
  initialized: boolean;
};

const PermissionContext = createContext<PermissionContextType | null>(null);

// ---- Provider ----
export const PermissionProvider: React.FC<{children: React.ReactNode}> = ({children}) => {
  const authState = useAppSelector(selectCurrentAuthState);
  const [permissions, setPermissions] = useState<Set<string>>(new Set());
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    const components = (authState.accessToken ?? '').split('.');
    const ExpectedComponentLength = 3;
    if (components.length === ExpectedComponentLength) {
      try {
        const payload = JSON.parse(atob(components[1]));
        const localPermissions: string[] = payload?.permissions ?? [];
        setPermissions(new Set(localPermissions));
        setIsSuperAdmin(localPermissions.includes('*'));
      } catch {
        setPermissions(new Set());
        setIsSuperAdmin(false);
      }
    } else {
      setPermissions(new Set());
      setIsSuperAdmin(false);
    }
    setInitialized(true);
  }, [authState]);

  const hasPermission = (perm: string) => {
    if (isSuperAdmin) return true;
    return permissions.has(perm);
  };

  const values = useMemo(
    () => ({hasPermission, isSuperAdmin, permissions, initialized}),
    [hasPermission, isSuperAdmin, permissions, initialized],
  );

  return <PermissionContext.Provider value={values}>{children}</PermissionContext.Provider>;
};

// ---- Hook for consuming ----
export const usePermissions = () => {
  const ctx = useContext(PermissionContext);
  if (!ctx) {
    throw new Error('usePermissions must be used inside PermissionProvider');
  }
  return ctx;
};
