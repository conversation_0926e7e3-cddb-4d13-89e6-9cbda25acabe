import {render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router';
import {describe, expect, it, Mock, vi} from 'vitest';
import {usePermissions} from './PermissionProvider';
import {PermissionRedirectWrapper} from './PermissionRedirectWrapper';

// Mock usePermissions
vi.mock('./PermissionProvider', () => ({
  usePermissions: vi.fn(),
}));

const DummyChild = () => <div data-testid="child">Allowed Content</div>;

describe('PermissionRedirectWrapper', () => {
  it('renders children when permission is allowed', () => {
    (usePermissions as Mock).mockReturnValue({hasPermission: () => true, initialized: true});
    render(
      <MemoryRouter>
        <PermissionRedirectWrapper permission="read">
          <DummyChild />
        </PermissionRedirectWrapper>
      </MemoryRouter>,
    );
    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('redirects when permission is not allowed', () => {
    (usePermissions as Mock).mockReturnValue({hasPermission: () => false, initialized: true});
    render(
      <MemoryRouter>
        <PermissionRedirectWrapper permission="read">
          <DummyChild />
        </PermissionRedirectWrapper>
      </MemoryRouter>,
    );
    // Should not render child, but instead render Navigate (which doesn't show anything in test DOM)
    expect(screen.queryByTestId('child')).not.toBeInTheDocument();
  });

  it('renders nothing while not initialized', () => {
    (usePermissions as Mock).mockReturnValue({hasPermission: () => true, initialized: false});
    const {container} = render(
      <MemoryRouter>
        <PermissionRedirectWrapper permission="read">
          <DummyChild />
        </PermissionRedirectWrapper>
      </MemoryRouter>,
    );
    expect(container.firstChild).toBeNull();
  });
});
