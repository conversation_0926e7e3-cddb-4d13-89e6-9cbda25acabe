import {JSX} from 'react';
import {Navigate} from 'react-router';
import {RouteNames} from 'Routes/routeNames';
import {usePermissions} from './PermissionProvider';

interface PermissionRedirectWrapperProps {
  children: JSX.Element;
  permission: string;
}

/**
 * Wrapper component for redirecting authenticated users.
 * Authenticated users will be redirected to the home page, while unauthenticated users will render the provided children.
 *
 * @param children - JSX element that will be rendered if the user is unauthenticated.
 * @returns The authentication redirect component.
 */
// sonarignore:start
export const PermissionRedirectWrapper: React.FC<PermissionRedirectWrapperProps> = ({children, permission}) => {
  const {hasPermission, initialized} = usePermissions();

  if (!initialized) {
    return null; // or a loading spinner
  }

  return <>{!hasPermission(permission) ? <Navigate to={RouteNames.HOME} replace /> : children}</>;
};
