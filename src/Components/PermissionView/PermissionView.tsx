import {Box, BoxProps, Divider, Typography} from '@mui/material';
import categorizedPermissions from 'Helpers/categorizedPermissions';

/**
 * Props for the PermissionsUI component.
 */
interface PermissionsUIProps extends BoxProps {
  permissions: string[];
}

/**
 * PermissionsUI component that displays categorized permissions.
 * It shows the section name and the allowed permissions for each section.
 *
 * @param permissions - Array of permission strings.
 * @param rest - Additional props for the Box component.
 * @returns A Box containing categorized permissions.
 */
const PermissionsUI = ({permissions, ...rest}: PermissionsUIProps) => {
  const hasAll = permissions.some(p => p === '*');
  return (
    <Box {...rest}>
      <Typography sx={{fontSize: '1rem', fontWeight: 700, color: 'body.900'}} gutterBottom>
        Permissions
      </Typography>

      <Box>
        {Object.entries(categorizedPermissions).map(([section, items], index, arr) => {
          const allowed = items.filter(item => hasAll || permissions.includes(item.permission));

          if (allowed.length === 0) return null;

          return (
            <Box key={section}>
              <Typography gutterBottom sx={{fontSize: '0.75rem', color: 'body.500', fontWeight: 600}}>
                {section}
              </Typography>
              <Typography sx={{color: 'body.800', fontSize: '0.875rem', fontWeight: 600}}>
                {allowed.map(item => item.label).join(', ')}
              </Typography>
              {index < arr.length - 1 && <Divider sx={{mt: 2}} />}
            </Box>
          );
        })}
      </Box>
    </Box>
  );
};

PermissionsUI.displayName = 'PermissionsUI';

export default PermissionsUI;
