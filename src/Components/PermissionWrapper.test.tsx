import {render, screen} from '@testing-library/react';
import {describe, expect, it, Mock, vi} from 'vitest';
import {usePermissions} from './PermissionRedirectWrapper/PermissionProvider';
import PermissionWrapper from './PermissionWrapper';

// Mock usePermissions
vi.mock('./PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: vi.fn(),
}));

const DummyChild = () => <div data-testid="child">Allowed Content</div>;

describe('PermissionWrapper', () => {
  it('renders children when permission is allowed', () => {
    (usePermissions as Mock).mockReturnValue({hasPermission: () => true});
    render(
      <PermissionWrapper permission="read">
        <DummyChild />
      </PermissionWrapper>,
    );
    expect(screen.getByTestId('child')).toBeInTheDocument();
  });

  it('renders nothing when permission is not allowed', () => {
    (usePermissions as Mock).mockReturnValue({hasPermission: () => false});
    const {container} = render(
      <PermissionWrapper permission="read">
        <DummyChild />
      </PermissionWrapper>,
    );
    expect(container.firstChild).toBeNull();
  });
});
