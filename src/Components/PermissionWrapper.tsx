import React from 'react';
import {usePermissions} from './PermissionRedirectWrapper/PermissionProvider';

export interface PermissionWrapperProps {
  permission: string;
  children: React.ReactNode;
}

const PermissionWrapper: React.FC<PermissionWrapperProps> = ({permission, children}) => {
  const {hasPermission} = usePermissions();

  if (!hasPermission(permission)) {
    return null;
  }
  return <>{children}</>;
};

export default PermissionWrapper;
