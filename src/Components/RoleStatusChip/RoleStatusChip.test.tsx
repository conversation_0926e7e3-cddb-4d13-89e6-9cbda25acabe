import {render, screen, waitFor} from '@testing-library/react';
import {RoleStatus} from 'Pages/RolesResponsibilitiesPage/roles.util';
import {IRoleView} from 'redux/auth/authApiSlice';
import {describe, expect, it, vi} from 'vitest';
import RoleStatusChip from './RoleStatusChip';
import * as roleStatusUtil from './roleStatus.util';

const mockRoleActive = {
  createdOn: '',
  status: RoleStatus.ACTIVE,
} as IRoleView;
const mockRoleInActive = {
  createdOn: '',
  status: RoleStatus.INACTIVE,
} as IRoleView;

describe('RoleStatusChip', () => {
  it('renders StatusChip with correct status', () => {
    render(<RoleStatusChip role={mockRoleActive} />);
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('calls getRoleStatusFrom with role', () => {
    const getRoleStatusFromSpy = vi.spyOn(roleStatusUtil, 'getRoleStatusFrom');
    render(<RoleStatusChip role={mockRoleInActive} />);
    waitFor(() => expect(getRoleStatusFromSpy).toHaveBeenCalledWith(mockRoleInActive));
  });
});

/* Removed duplicate edge case tests below, as they are already covered above */
