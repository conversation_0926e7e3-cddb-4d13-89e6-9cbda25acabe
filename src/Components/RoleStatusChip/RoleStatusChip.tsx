import StatusChip from 'Components/StatusChip/StatusChip';
import {IRole} from 'redux/app/types';
import {IRoleView} from 'redux/auth/authApiSlice';
import getChipStatusForRole, {getRoleStatusFrom} from './roleStatus.util';

interface RoleStatusChipProps {
  role: IRole | IRoleView;
}

const RoleStatusChip = ({role}: RoleStatusChipProps) => (
  <StatusChip status={getChipStatusForRole(getRoleStatusFrom(role))} />
);

export default RoleStatusChip;
