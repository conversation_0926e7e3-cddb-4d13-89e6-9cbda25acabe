import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import {Integers} from 'Helpers/integers';
import {RoleStatus} from 'Pages/RolesResponsibilitiesPage/roles.util';
import {IRole} from 'redux/app/types';
import {IRoleView} from 'redux/auth/authApiSlice';

/**
 * Maps role status to status chip state.
 * @param status - The role status to map.
 * @returns The corresponding status chip state.
 */
export const getChipStatusForRole = (status: RoleStatus): StatusChipState => {
  const chipStatusMap: Record<RoleStatus, StatusChipState> = {
    [RoleStatus.ACTIVE]: StatusChipState.ACTIVE,
    [RoleStatus.INACTIVE]: StatusChipState.INACTIVE,
    [RoleStatus.UNKNOWN]: StatusChipState.INACTIVE,
  };
  return chipStatusMap[status] ?? StatusChipState.INACTIVE;
};

export default getChipStatusForRole;

export const getRoleStatusFrom = (role: IRole | IRoleView) => {
  if (role.status === Integers.Zero) {
    return RoleStatus.ACTIVE;
  }
  return RoleStatus.INACTIVE;
};
