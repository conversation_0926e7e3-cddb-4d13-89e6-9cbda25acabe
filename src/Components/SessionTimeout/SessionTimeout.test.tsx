// src/Components/SessionTimeout/SessionTimeout.test.tsx
import {render, screen} from '@testing-library/react';
import {act} from 'react-dom/test-utils';
import SessionTimeout from './SessionTimeout';

// Mock useAuth
import useAuth from '../../Hooks/useAuth';
vi.mock('../../Hooks/useAuth');

// Mock useIdleTimer
import {useIdleTimer} from 'react-idle-timer';
vi.mock('react-idle-timer');

// Mock WarningLogoutDialog
vi.mock('./WarningLogoutDialog', () => ({
  __esModule: true,
  default: (props: any) => (
    <div data-testid="warning-dialog" {...props}>
      WarningLogoutDialog
    </div>
  ),
}));

const mockUseIdleTimer = {
  getRemainingTime: () => 10000,
  reset: vi.fn(),
  message: vi.fn(),
  isPrompted: () => false,
};

beforeEach(() => {
  (useAuth as any).mockImplementation(() => ({
    logout: vi.fn(),
    logoutLoading: false,
  }));
  (useIdleTimer as any).mockImplementation(() => mockUseIdleTimer);
});

afterEach(() => {
  vi.clearAllMocks();
});

describe('SessionTimeout', () => {
  it('renders without crashing', () => {
    render(<SessionTimeout expiryTimeInMinute={15} promptTimeBeforeIdleInMinute={1} />);
    expect(screen.queryByTestId('warning-dialog')).not.toBeInTheDocument();
  });

  it('shows WarningLogoutDialog when showPopup is true', () => {
    let onPrompt: () => void = () => {};
    (useIdleTimer as any).mockImplementationOnce((opts: any) => {
      onPrompt = opts.onPrompt;
      return {...mockUseIdleTimer, isPrompted: () => false};
    });
    render(<SessionTimeout expiryTimeInMinute={15} promptTimeBeforeIdleInMinute={1} />);
    // Simulate idle prompt
    act(() => {
      onPrompt();
    });
    // The dialog should now be in the document
    expect(screen.getByTestId('warning-dialog')).toBeInTheDocument();
  });

  it('renders BackdropLoader when logoutLoading is true', () => {
    (useAuth as any).mockImplementationOnce(() => ({
      logout: vi.fn(),
      logoutLoading: true,
    }));
    render(<SessionTimeout expiryTimeInMinute={15} promptTimeBeforeIdleInMinute={1} />);
    expect(document.querySelector('.MuiBackdrop-root')).toBeTruthy();
  });

  it('calls logout on idle', () => {
    const logoutMock = vi.fn();
    (useAuth as any).mockImplementationOnce(() => ({
      logout: logoutMock,
      logoutLoading: false,
    }));
    let onIdle: () => void = () => {};
    (useIdleTimer as any).mockImplementationOnce((opts: any) => {
      onIdle = opts.onIdle;
      return mockUseIdleTimer;
    });
    render(<SessionTimeout expiryTimeInMinute={15} promptTimeBeforeIdleInMinute={1} />);
    act(() => {
      onIdle();
    });
    expect(logoutMock).toHaveBeenCalled();
  });

  it('handles onMessage SIGN_OUT and STAY', () => {
    const logoutMock = vi.fn();
    const resetMock = vi.fn();
    (useAuth as any).mockImplementationOnce(() => ({
      logout: logoutMock,
      logoutLoading: false,
    }));
    (useIdleTimer as any).mockImplementationOnce((opts: any) => {
      // expose onMessage for direct call
      return {...mockUseIdleTimer, reset: resetMock, onMessage: opts.onMessage};
    });
    render(<SessionTimeout expiryTimeInMinute={15} promptTimeBeforeIdleInMinute={1} />);
    // Get the latest onMessage from the hook
    const {onMessage} = (useIdleTimer as any).mock.calls[0][0];
    // SIGN_OUT
    act(() => {
      onMessage({action: 'SIGN_OUT'});
    });
    // STAY
    act(() => {
      onMessage({action: 'STAY'});
    });
    expect(logoutMock).toHaveBeenCalled();
    expect(resetMock).toHaveBeenCalled();
  });

  it('handles onAction and setShowPopup', () => {
    let onAction: () => void = () => {};
    let isPrompted = vi.fn(() => false);
    (useIdleTimer as any).mockImplementationOnce((opts: any) => {
      onAction = opts.onAction;
      return {...mockUseIdleTimer, isPrompted};
    });
    render(<SessionTimeout expiryTimeInMinute={15} promptTimeBeforeIdleInMinute={1} />);
    act(() => {
      onAction();
    });
    // No assertion needed, just coverage for setShowPopup(false)
    expect(isPrompted).toHaveBeenCalled();
  });
});
