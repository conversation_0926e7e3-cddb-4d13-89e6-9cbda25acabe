// src/Components/SessionTimeout/WarningLogoutDialog.test.tsx
import {act, fireEvent, render, screen} from '@testing-library/react';
import WarningLogoutDialog from './WarningLogoutDialog';

describe('WarningLogoutDialog', () => {
  const getRemainingTime = vi.fn(() => 5000);
  const broadcastMessage = vi.fn();

  beforeEach(() => {
    vi.useFakeTimers();
    getRemainingTime.mockClear();
    broadcastMessage.mockClear();
  });

  afterEach(() => {
    vi.useRealTimers();
  });

  it('renders dialog with countdown', () => {
    render(
      <WarningLogoutDialog openDialog={true} getRemainingTime={getRemainingTime} broadcastMessage={broadcastMessage} />,
    );
    expect(screen.getByText(/Are you still there/i)).toBeInTheDocument();
    expect(screen.getByText(/If not, we'll close this session in/)).toBeInTheDocument();
  });

  it('updates countdown every half second', () => {
    render(
      <WarningLogoutDialog openDialog={true} getRemainingTime={getRemainingTime} broadcastMessage={broadcastMessage} />,
    );
    act(() => {
      vi.advanceTimersByTime(500);
    });
    expect(getRemainingTime).toHaveBeenCalled();
  });

  it('calls broadcastMessage with SIGN_OUT when Sign out is clicked', () => {
    render(
      <WarningLogoutDialog openDialog={true} getRemainingTime={getRemainingTime} broadcastMessage={broadcastMessage} />,
    );
    fireEvent.click(screen.getByText('Sign out'));
    expect(broadcastMessage).toHaveBeenCalledWith({action: 'SIGN_OUT'}, true);
  });

  it("calls broadcastMessage with STAY when I'm here is clicked", () => {
    render(
      <WarningLogoutDialog openDialog={true} getRemainingTime={getRemainingTime} broadcastMessage={broadcastMessage} />,
    );
    fireEvent.click(screen.getByText("I'm here"));
    expect(broadcastMessage).toHaveBeenCalledWith({action: 'STAY'}, true);
  });
});
