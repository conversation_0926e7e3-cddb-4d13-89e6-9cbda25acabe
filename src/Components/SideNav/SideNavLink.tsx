import {ChevronRight} from '@mui/icons-material';
import {Collapse, List} from '@mui/material';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemIcon from '@mui/material/ListItemIcon';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import {DefaultToolTip} from 'Components/ToolTipTypography/ToolTipTypography';
import {useEffect, useState} from 'react';
import {Link, Location} from 'react-router-dom';
import {SideNavConfig} from './sideNavConfig';
import styles from './styles';

const isChildOf = (child: string, parent: string) => child.startsWith(parent);

const CollapsibleRightIcon = ({isOpen}: {isOpen: boolean}) => {
  return (
    <ChevronRight
      sx={{
        transform: isOpen ? 'rotate(90deg)' : 'rotate(0deg)',
        transition: 'transform 0.2s ease',
        width: 22,
        color: isOpen ? styles.linkItemIconActive : 'body.300', // Use the same styles as linkTextActive for consistency
      }}
    />
  );
};

const NestedNavLink = ({
  link,
  children,
  // isLinkActive,
  icon,
  label,
  location,
}: SideNavConfig & {location: Location}) => {
  const [isOpen, setIsOpen] = useState(false);
  const toggleCollapse = () => setIsOpen(!isOpen);

  useEffect(() => {
    if (link && children?.length && isChildOf(location.pathname, link)) {
      setIsOpen(true);
    }
  }, [children?.length, link, location.pathname]);

  return (
    <>
      <ListItem
        component={link ? Link : 'li'}
        to={link}
        onClick={toggleCollapse}
        sx={{
          ...styles.link,
        }}
      >
        {icon && (
          <ListItemIcon sx={{...styles.listItemIcon, ...(isOpen && styles.linkItemIconActive)}}>{icon}</ListItemIcon>
        )}
        <ListItemText
          sx={{
            ...(isOpen && styles.linkTextActive),
            fontWeight: isOpen ? '700' : 'unset',
          }}
          primary={label}
          disableTypography
        />
        <CollapsibleRightIcon isOpen={isOpen} />
      </ListItem>

      {children && (
        <Collapse component="li" in={isOpen} timeout="auto" unmountOnExit sx={{paddingLeft: 2}}>
          <List component="ul" disablePadding>
            {children.map(childrenLink =>
              !childrenLink.visible ? null : (
                <SideNavLink
                  key={`${childrenLink.label}-${childrenLink.type}-${childrenLink.visible}`}
                  location={location}
                  {...childrenLink}
                />
              ),
            )}
          </List>
        </Collapse>
      )}
    </>
  );
};

const SideNavLink = (props: SideNavConfig & {location: Location}) => {
  if (!props?.visible) return null;

  if ('type' in props && 'label' in props && props.type === 'title') {
    return (
      <Typography component="li" sx={styles.title}>
        {props.label}
      </Typography>
    );
  }

  if ('type' in props && props.type === 'divider') return <Divider component="li" sx={styles.divider} />;

  const buildContent = (isLinkActive: boolean) => {
    return (
      <ListItemButton
        component={props.link ? Link : 'li'}
        to={props.link}
        sx={{
          ...styles.link,
          ...(isLinkActive && styles.linkListActive),
        }}
        disableRipple
      >
        {props.icon && (
          <ListItemIcon sx={{...styles.listItemIcon, ...(isLinkActive && styles.linkItemIconActive)}}>
            {props.icon}
          </ListItemIcon>
        )}
        <DefaultToolTip title={props.label} placement="auto" arrow>
          <ListItemText
            sx={{
              ...(isLinkActive && styles.linkTextActive),
            }}
            disableTypography
            primary={props.label}
          />
        </DefaultToolTip>
      </ListItemButton>
    );
  };

  if (!props?.children) {
    const isLinkActive =
      !!props.link && (props.location.pathname === props.link || isChildOf(props.location.pathname, props.link));
    return (
      <Grid component="li" sx={{paddingX: 1}}>
        {buildContent(isLinkActive)}
      </Grid>
    );
  }

  return (
    <Grid component="li" sx={{paddingX: 1}}>
      <NestedNavLink {...props} />
    </Grid>
  );
};

export default SideNavLink;
