import BillingInvoiceIcon from 'Assets/side-nav/side-nav-billing-invoice.svg';
import HomeOutlinedIcon from 'Assets/side-nav/side-nav-home.svg';
import ObservabilityIcon from 'Assets/side-nav/side-nav-observability.svg';
import PendingTenantIcon from 'Assets/side-nav/side-nav-pending-tenants.svg';
import PlansIcon from 'Assets/side-nav/side-nav-plans.svg';
import TenantIcon from 'Assets/side-nav/side-nav-teams.svg';
import UsermanagementIcon from 'Assets/side-nav/side-nav-user-mgmt.svg';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import PermissionKey from 'Constants/enums/permissions';
import {ReactNode} from 'react';
import {RouteNames} from 'Routes/routeNames';

export interface SideNavDividerType {
  type: 'divider';
  visible: boolean;
}

export interface SideNavTitleType extends Omit<SideNavDividerType, 'type'> {
  label: string;
  type: 'title';
}

export interface SideNavConfig extends Omit<SideNavDividerType, 'type'>, Omit<SideNavTitleType, 'type'> {
  type?: 'title' | 'divider';
  link?: string;
  icon?: ReactNode;
  children?: (SideNavConfig | SideNavTitleType)[];
}

const useSideNavConfig = () => {
  const {hasPermission} = usePermissions();

  const showUserAndRoles = [hasPermission(PermissionKey.ViewTenantUser), hasPermission(PermissionKey.ViewRoles)].some(
    Boolean,
  );
  const sideNavConfig: SideNavConfig[] = [
    {
      label: 'Dashboard',
      link: RouteNames.HOME,
      icon: <SVGImageFromPath path={HomeOutlinedIcon} />,
      visible: true,
    },
    {
      label: 'Pending Tenants',
      link: RouteNames.PENDING_TENANTS,
      icon: <SVGImageFromPath path={PendingTenantIcon} />,
      visible: hasPermission(PermissionKey.ViewLead),
    },
    {
      label: 'Tenants',
      link: RouteNames.TENANTS,
      icon: <SVGImageFromPath path={TenantIcon} />,
      visible: hasPermission(PermissionKey.ViewTenant),
    },
    {
      label: 'Billing & Invoices',
      link: RouteNames.BILLING_INVOICES,
      icon: <SVGImageFromPath path={BillingInvoiceIcon} />,
      visible: hasPermission(PermissionKey.ViewInvoice),
    },

    {
      label: 'Plans',
      link: RouteNames.PLANS,
      icon: <SVGImageFromPath path={PlansIcon} />,
      visible: hasPermission(PermissionKey.ViewPlan),
    },
    {
      label: 'Observability',
      link: RouteNames.OBSERVABILITY,
      icon: <SVGImageFromPath path={ObservabilityIcon} />,
      visible: hasPermission(PermissionKey.ViewObservability),
    },
    {
      label: 'Users & Roles',
      icon: <SVGImageFromPath path={UsermanagementIcon} />,
      visible: showUserAndRoles,
      children: [
        {
          label: 'User Management',
          link: RouteNames.USER_MANAGEMENT,
          visible: hasPermission(PermissionKey.ViewTenantUser),
        },
        {
          label: 'Roles & Permissions',
          link: RouteNames.ROLES_RESPONSIBILITIES,
          visible: hasPermission(PermissionKey.ViewRoles),
        },
      ],
    },
  ];
  return {
    sideNavConfig,
  };
};

export default useSideNavConfig;
