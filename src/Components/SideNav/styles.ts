const styles = {
  link: {
    height: '2.8125rem',
    paddingY: 0.5,
    paddingX: 2,
    borderRadius: 0.75,
    textTransform: 'capitalize',
    fontSize: 14,
    '&:hover': {
      backgroundColor: 'sideNav.hover',
    },
    marginBottom: 0.3,
    color: 'sideNav.active',
    fontWeight: '500',
  },
  linkListActive: {backgroundColor: '#F0F0FB'},
  linkTextActive: {
    fontWeight: '700',
    color: 'sideNav.linkTextActive',
    fontSize: 14,
  },
  listItemIcon: {minWidth: 30},
  linkItemIconActive: {
    color: 'sideNav.active',
  },
  divider: {marginTop: 1, marginBottom: 1, height: 2},
  title: {paddingX: 1, textTransform: 'uppercase', fontWeight: 'bold'},
};

export default styles;
