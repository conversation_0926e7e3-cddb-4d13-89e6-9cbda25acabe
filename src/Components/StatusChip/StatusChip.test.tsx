// @vitest-environment jsdom
import {render, screen} from '@testing-library/react';
import StatusChip from './StatusChip';
import * as util from './statusChip.util';
import {StatusChipState} from './statusChip.util';

describe('StatusChip', () => {
  beforeEach(() => {
    vi.restoreAllMocks();
  });

  it('renders with explicit props', () => {
    render(<StatusChip label="Custom" backgroundColor="#123" color="#456" indicatorColor="#789" />);
    const chip = screen.getByTestId('status-chip');
    expect(chip).toHaveTextContent('Custom');
  });

  Object.entries(StatusChipState)
    .filter(([k, v]) => typeof v === 'number')
    .forEach(([key, value]) => {
      it(`renders with status prop: ${key}`, () => {
        const getStatusLabel = vi.spyOn(util, 'getStatusLabel').mockReturnValue(`Label-${key}`);
        const getStatusColor = vi.spyOn(util, 'getStatusColor').mockReturnValue(`bg-${key}`);
        const getFontColor = vi.spyOn(util, 'getFontColor').mockReturnValue(`font-${key}`);
        const getIndicatorColor = vi.spyOn(util, 'getIndicatorColor').mockReturnValue(`ind-${key}`);

        render(<StatusChip status={value as StatusChipState} />);
        const chip = screen.getByTestId('status-chip');
        expect(chip).toHaveTextContent(`Label-${key}`);
        expect(getStatusLabel).toHaveBeenCalledWith(value);
        expect(getStatusColor).toHaveBeenCalledWith(value);
        expect(getFontColor).toHaveBeenCalledWith(value);
        expect(getIndicatorColor).toHaveBeenCalledWith(value);
      });
    });

  it('falls back to empty label for unknown status', () => {
    const getStatusLabel = vi.spyOn(util, 'getStatusLabel').mockReturnValue('');
    const getStatusColor = vi.spyOn(util, 'getStatusColor').mockReturnValue('fallback-bg');
    const getFontColor = vi.spyOn(util, 'getFontColor').mockReturnValue('fallback-font');
    const getIndicatorColor = vi.spyOn(util, 'getIndicatorColor').mockReturnValue('fallback-ind');

    render(<StatusChip status={999 as StatusChipState} />);
    const chip = screen.getByTestId('status-chip');
    expect(chip).toHaveTextContent('');
    expect(getStatusLabel).toHaveBeenCalledWith(999);
    expect(getStatusColor).toHaveBeenCalledWith(999);
    expect(getFontColor).toHaveBeenCalledWith(999);
    expect(getIndicatorColor).toHaveBeenCalledWith(999);
  });

  it('renders with minimal explicit props', () => {
    render(<StatusChip label="" backgroundColor="" color="" indicatorColor="" />);
    const chip = screen.getByTestId('status-chip');
    expect(chip).toHaveTextContent('');
  });
});
