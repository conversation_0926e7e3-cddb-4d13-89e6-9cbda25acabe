import {Box} from '@mui/material';
import Chip from '@mui/material/Chip';
import React from 'react';
import {getFontColor, getIndicatorColor, getStatusColor, getStatusLabel, StatusChipState} from './statusChip.util';

/**
 * Props for the first version of the StatusChip component.
 */
interface IStatusChipProps {
  label: string;
  backgroundColor: string;
  color: string;
  indicatorColor: string;
}

/**
 * Props for the second version of the StatusChip component.
 */
interface IStatusChipPropsV2 {
  status: StatusChipState;
  label?: string;
}

/**
 * A React functional component that displays a status chip with a colored dot indicator.
 *
 * @param {IStatusChipProps} props - The props for the StatusChip component.
 * @param {string} props.label - The text label to display inside the chip.
 * @param {string} props.backgroundColor - The background color of the chip.
 * @param {string} props.color - The color used for the dot indicator and the chip text.
 *
 * @returns {JSX.Element} The rendered StatusChip component.
 *
 * @example
 * <StatusChip label="Active" backgroundColor="#E0F7FA" color="#00796B" />
 */
const StatusChip: React.FC<IStatusChipProps | IStatusChipPropsV2> = props => {
  const paddingX = 2;
  const dotSize = 0.5;

  const getColors = (): IStatusChipProps => {
    if ('status' in props) {
      return {
        label: props.label ?? getStatusLabel(props.status),
        backgroundColor: getStatusColor(props.status),
        color: getFontColor(props.status),
        indicatorColor: getIndicatorColor(props.status),
      };
    } else {
      return {
        backgroundColor: props.backgroundColor,
        color: props.color,
        indicatorColor: props.indicatorColor,
        label: props.label,
      };
    }
  };

  const {label, backgroundColor, color, indicatorColor} = getColors();

  const drawDot = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'absolute',
          left: theme => theme.spacing(paddingX / 2),
        }}
      >
        <Box
          sx={{
            backgroundColor: indicatorColor,
            display: 'inline-block',
            width: theme => theme.spacing(dotSize),
            height: theme => theme.spacing(dotSize),
            borderRadius: '50%',
          }}
        />
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyItems: 'center',
        gap: 0,
        position: 'relative',
      }}
    >
      {drawDot()}
      <Chip
        data-testid="status-chip"
        label={label}
        sx={{
          backgroundColor: `${backgroundColor}`,
          color: color,
          fontSize: '0.75rem',
          height: '1.5rem',
          borderRadius: '0.4rem',
          fontWeight: 700,
        }}
        slotProps={{
          label: {
            sx: {
              paddingLeft: paddingX,
              paddingRight: paddingX,
            },
          },
        }}
      />
    </Box>
  );
};

export default StatusChip;
