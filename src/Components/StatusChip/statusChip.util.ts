const whiteMain = 'white.main';

/**
 * Enumeration for the different states of the status chip.
 */
export enum StatusChipState {
  ACTIVE, // Tenant is active and fully functional
  PENDINGPROVISION, // Tenant is awaiting provisioning
  PROVISIONING, // Tenant is currently being provisioned
  PROVISIONFAILED, // Provisioning process failed
  INACTIVE, // Tenant is inactive
  PENDINGONBOARDING, // Tenant is active but not onboarded due to missing information
}

/**
 * Get the background color for a given status.
 * @param status - The status of the chip.
 * @returns The background color path as a string.
 */
export const getStatusColor = (status: StatusChipState): string => {
  const statusColorMap: Record<StatusChipState, string> = {
    [StatusChipState.ACTIVE]: `alert.success.bg`,
    [StatusChipState.PENDINGPROVISION]: `alert.warning.bg`,
    [StatusChipState.INACTIVE]: `alert.error.bg`,
    [StatusChipState.PROVISIONFAILED]: `tenantStatus.failed.bg`,
    [StatusChipState.PROVISIONING]: `tenantStatus.provisioning.bg`,
    [StatusChipState.PENDINGONBOARDING]: `alert.info.bg`,
  };
  return statusColorMap[status] ?? whiteMain;
};

/**
 * Get the font color for a given status.
 * @param status - The status of the chip.
 * @returns The font color as a string.
 */
export const getFontColor = (status: StatusChipState): string => {
  const statusColorMap: Record<StatusChipState, string> = {
    [StatusChipState.ACTIVE]: `alert.success.onBg`,
    [StatusChipState.PENDINGPROVISION]: `alert.warning.onBg`,
    [StatusChipState.INACTIVE]: `alert.error.onBg`,
    [StatusChipState.PROVISIONFAILED]: `tenantStatus.failed.onBg`,
    [StatusChipState.PROVISIONING]: `tenantStatus.provisioning.onBg`,
    [StatusChipState.PENDINGONBOARDING]: `alert.info.onBg`,
  };
  return statusColorMap[status] ?? whiteMain;
};

/**
 * Get the indicator color for a given status.
 * @param status - The status of the chip.
 * @returns The indicator color as a string.
 */
export const getIndicatorColor = (status: StatusChipState): string => {
  const statusColorMap: Record<StatusChipState, string> = {
    [StatusChipState.ACTIVE]: `alert.success.main`,
    [StatusChipState.PENDINGPROVISION]: `alert.warning.onBg`,
    [StatusChipState.INACTIVE]: `alert.error.main`,
    [StatusChipState.PROVISIONFAILED]: `tenantStatus.failed.indicator`,
    [StatusChipState.PROVISIONING]: `tenantStatus.provisioning.indicator`,
    [StatusChipState.PENDINGONBOARDING]: `alert.warning.main`,
  };
  return statusColorMap[status] ?? whiteMain;
};

/**
 * Get the status label for a given status.
 * @param status - The status of the chip.
 * @returns The status label as a string.
 */
export const getStatusLabel = (status: StatusChipState | number): string => {
  const statusLabelMap: Record<StatusChipState | number, string> = {
    [StatusChipState.ACTIVE]: 'Active',
    [StatusChipState.PENDINGPROVISION]: 'Pending Provision',
    [StatusChipState.INACTIVE]: 'Inactive',
    [StatusChipState.PROVISIONFAILED]: 'Failed Provision',
    [StatusChipState.PROVISIONING]: 'Provisioning',
    [StatusChipState.PENDINGONBOARDING]: 'Pending Onboarding',
  };
  return statusLabelMap[status] || '';
};
