import {ThemeProvider, createTheme} from '@mui/material/styles';
import {render, screen} from '@testing-library/react';
import {describe, expect, it} from 'vitest';
import StepperTab, {StepperTabProps} from './StepperTab';

// create a custom theme with "body" palette so styles work in tests
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
      50: '#e3f2fd',
      100: '#bbdefb',
      150: '#90caf9',
      200: '#64b5f6',
    },
    body: {
      100: '#e0e0e0',
      500: '#6b6b6b',
    },
    white: {
      main: '#ffffff',
    },
  } as any, // allow custom keys
});

const defaultSteps = ['Step 1', 'Step 2', 'Step 3'];

// utility to wrap in ThemeProvider
const renderWithTheme = (props?: Partial<StepperTabProps>) =>
  render(
    <ThemeProvider theme={theme}>
      <StepperTab steps={defaultSteps} activeStep={1} {...props} />
    </ThemeProvider>,
  );

describe('StepperTab', () => {
  it('renders all step labels correctly', () => {
    renderWithTheme();

    defaultSteps.forEach(step => {
      expect(screen.getByText(step)).toBeInTheDocument();
    });
  });

  it('renders no steps if steps array is empty', () => {
    render(
      <ThemeProvider theme={theme}>
        <StepperTab steps={[]} activeStep={0} />
      </ThemeProvider>,
    );
    expect(screen.queryAllByRole('listitem')).toHaveLength(0);
  });

  it('renders stepper with memoization (no crash)', () => {
    const {container, rerender} = render(
      <ThemeProvider theme={theme}>
        <StepperTab steps={['Step A', 'Step B']} activeStep={0} />
      </ThemeProvider>,
    );
    expect(container).toBeInTheDocument();

    // re-render with same props to ensure memoization doesn't break
    rerender(
      <ThemeProvider theme={theme}>
        <StepperTab steps={['Step A', 'Step B']} activeStep={0} />
      </ThemeProvider>,
    );
    expect(screen.getByText('Step A')).toBeInTheDocument();
  });

  it('applies active step correctly', () => {
    renderWithTheme({activeStep: 1});
    const activeLabel = screen.getByText('Step 2');
    expect(activeLabel).toBeInTheDocument();
    expect(activeLabel).toHaveClass('Mui-active');
  });

  it('applies completed step correctly', () => {
    renderWithTheme({activeStep: 2});
    const completedLabel = screen.getByText('Step 1');
    expect(completedLabel).toBeInTheDocument();
    expect(completedLabel).toHaveClass('Mui-completed');
  });
});
