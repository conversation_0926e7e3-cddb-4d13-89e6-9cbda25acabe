import {fireEvent, render, screen} from '@testing-library/react';
import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest';
import {DebouncedInput} from './DebounceInput';

describe('DebouncedInput', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });

  afterEach(() => {
    vi.clearAllTimers();
    vi.restoreAllMocks();
  });

  it('renders with initial value', () => {
    render(<DebouncedInput value="test" onChange={vi.fn()} />);
    expect(screen.getByDisplayValue('test')).toBeInTheDocument();
  });

  it('calls onChange after debounce time', () => {
    const handleChange = vi.fn();
    render(<DebouncedInput value="" onChange={handleChange} debounceTime={500} />);
    const input = screen.getByPlaceholderText('Search Across Table');
    fireEvent.change(input, {target: {value: 'abc'}});

    // Fast-forward time by 499ms, should not call onChange yet
    const debounceTime = 500;
    vi.advanceTimersByTime(debounceTime - 1);
    expect(handleChange).not.toHaveBeenCalled();

    vi.advanceTimersByTime(1);
    expect(handleChange).toHaveBeenCalledWith('abc');
  });

  it('updates value when prop changes', () => {
    const {rerender} = render(<DebouncedInput value="foo" onChange={vi.fn()} />);
    expect(screen.getByDisplayValue('foo')).toBeInTheDocument();
    rerender(<DebouncedInput value="bar" onChange={vi.fn()} />);
    expect(screen.getByDisplayValue('bar')).toBeInTheDocument();
  });

  it('clears input when clear button is clicked', () => {
    const handleChange = vi.fn();
    render(<DebouncedInput value="test" onChange={handleChange} />);

    const clearButton = screen.getByLabelText('clear');
    fireEvent.click(clearButton);

    expect(screen.getByDisplayValue('')).toBeInTheDocument();
    expect(handleChange).toHaveBeenCalledWith('');
  });

  it('shows search icon by default', () => {
    render(<DebouncedInput value="" onChange={vi.fn()} />);
    expect(screen.getByLabelText('search')).toBeInTheDocument();
  });

  it('shows custom left adornment when provided', () => {
    const customAdornment = <div data-testid="custom-adornment">Custom</div>;
    render(<DebouncedInput value="" onChange={vi.fn()} leftAdornment={customAdornment} />);

    expect(screen.queryByLabelText('search')).not.toBeInTheDocument();
    expect(screen.getByTestId('custom-adornment')).toBeInTheDocument();
  });

  it('shows clear button only when there is a value', () => {
    const {rerender} = render(<DebouncedInput value="" onChange={vi.fn()} />);
    expect(screen.queryByLabelText('clear')).not.toBeInTheDocument();

    rerender(<DebouncedInput value="test" onChange={vi.fn()} />);
    expect(screen.getByLabelText('clear')).toBeInTheDocument();
  });

  it('uses default debounce time when not provided', () => {
    const handleChange = vi.fn();
    render(<DebouncedInput value="" onChange={handleChange} />);
    const input = screen.getByPlaceholderText('Search Across Table');
    fireEvent.change(input, {target: {value: 'test'}});

    const defaultDebounceTime = 299; // Assuming default debounce time is 300ms
    vi.advanceTimersByTime(defaultDebounceTime);
    expect(handleChange).not.toHaveBeenCalled();

    vi.advanceTimersByTime(1);
    expect(handleChange).toHaveBeenCalledWith('test');
  });
});
