import {IconButton, InputAdornment, Stack, SxProps, TextField, TextFieldProps} from '@mui/material';
import {ClearIcon} from '@mui/x-date-pickers';
import SearchIcon from 'Assets/SearchIcon';
import {useEffect, useState} from 'react';

interface Props extends Omit<TextFieldProps, 'onChange'> {
  value: string | number;
  onChange: (val: string | number) => void;
  debounceTime?: number;
  inputSx?: SxProps;
  leftAdornment?: React.ReactNode;
}
const DEBOUNCE_TIME = 300;
export const DebouncedInput = ({
  value: initialValue,
  onChange,
  debounceTime = DEBOUNCE_TIME,
  inputSx,
  sx,
  leftAdornment,
  ...props
}: Props) => {
  const [value, setValue] = useState(initialValue);

  // setValue if any initialValue changes
  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  // debounce onChange — triggered on every keypress
  useEffect(() => {
    const timeout = setTimeout(() => {
      onChange(value);
    }, debounceTime);

    return () => {
      clearTimeout(timeout);
    };
  }, [value, onChange, debounceTime]);

  const handleClearClick = () => {
    setValue('');
    onChange('');
  };

  return (
    <Stack direction="row">
      <TextField
        size="small"
        placeholder="Search Across Table"
        value={value}
        onChange={e => setValue(e.target.value)}
        slotProps={{
          input: {
            sx: {
              ...inputSx,
            },
            startAdornment: (
              <InputAdornment position="start" sx={{m: 0}}>
                {leftAdornment ?? <SearchIcon aria-label="search" />}
              </InputAdornment>
            ),
            endAdornment: value ? (
              <InputAdornment position="end" sx={{m: 0}}>
                <IconButton aria-label="clear" onClick={handleClearClick} sx={{p: 0}}>
                  <ClearIcon sx={{width: '1rem', height: '1rem'}} />
                </IconButton>
              </InputAdornment>
            ) : null,
          },
        }}
        sx={{
          '& .MuiOutlinedInput-root': {
            '& fieldset': {
              borderColor: 'body.100',
            },
            '&:hover fieldset': {
              borderColor: 'body.100',
            },
            '&.Mui-focused fieldset': {
              borderColor: 'body.100',
              borderWidth: 1,
            },
          },
          ...sx,
        }}
        {...props}
      />
    </Stack>
  );
};
