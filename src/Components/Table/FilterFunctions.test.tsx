// @vitest-environment jsdom
import {beforeEach, describe, expect, it, vi} from 'vitest';
import {filterFns} from './FilterFunctions';

// Use a closure variable for rankItem mock
let rankItemMock: any;

vi.mock('@tanstack/match-sorter-utils', () => ({
  rankItem: (...args: any[]) => rankItemMock(...args),
  rankings: {MATCHES: 1},
}));

const mockAddMeta = vi.fn();

const mockRow = (value: any) => ({
  getValue: vi.fn().mockReturnValue(value),
});

describe('filterFns', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('fuzzy', () => {
    const mockPassed = {passed: true};
    const mockFailed = {passed: false};

    it('returns true when rankItem.passed is true', () => {
      rankItemMock = vi.fn().mockReturnValueOnce(mockPassed);
      const row = mockRow('foo');
      const result = filterFns.fuzzy(row as any, 'col', 'foo', mockAddMeta);
      expect(result).toBe(true);
      expect(mockAddMeta).toHaveBeenCalledWith(mockPassed);
      expect(rankItemMock).toHaveBeenCalled();
    });

    it('returns false when rankItem.passed is false', () => {
      rankItemMock = vi.fn().mockReturnValueOnce(mockFailed);
      const row = mockRow('foo');
      const result = filterFns.fuzzy(row as any, 'col', 'bar', mockAddMeta);
      expect(result).toBe(false);
      expect(mockAddMeta).toHaveBeenCalledWith(mockFailed);
      expect(rankItemMock).toHaveBeenCalled();
    });

    it('autoRemove returns true for falsy values', () => {
      expect(filterFns.fuzzy.autoRemove(undefined)).toBe(true);
      expect(filterFns.fuzzy.autoRemove(null)).toBe(true);
      expect(filterFns.fuzzy.autoRemove('')).toBe(true);
      expect(filterFns.fuzzy.autoRemove(0)).toBe(true);
    });

    it('autoRemove returns false for truthy values', () => {
      expect(filterFns.fuzzy.autoRemove('abc')).toBe(false);
      expect(filterFns.fuzzy.autoRemove(1)).toBe(false);
    });
  });

  describe('contains', () => {
    it('returns true if value contains filterValue (case-insensitive, trimmed)', () => {
      const row = mockRow('  Hello World  ');
      expect(filterFns.contains(row as any, 'col', 'hello')).toBe(true);
      expect(filterFns.contains(row as any, 'col', 'WORLD')).toBe(true);
      expect(filterFns.contains(row as any, 'col', 'lo wo')).toBe(true);
    });

    it('returns false if value does not contain filterValue', () => {
      const row = mockRow('abc');
      expect(filterFns.contains(row as any, 'col', 'xyz')).toBe(false);
    });

    it('handles numeric values', () => {
      const row = mockRow(12345);
      expect(filterFns.contains(row as any, 'col', 234)).toBe(true);
      expect(filterFns.contains(row as any, 'col', 999)).toBe(false);
    });

    it('autoRemove returns true for falsy values', () => {
      expect(filterFns.contains.autoRemove(undefined)).toBe(true);
      expect(filterFns.contains.autoRemove(null)).toBe(true);
      expect(filterFns.contains.autoRemove('')).toBe(true);
      expect(filterFns.contains.autoRemove(0)).toBe(true);
    });

    it('autoRemove returns false for truthy values', () => {
      expect(filterFns.contains.autoRemove('abc')).toBe(false);
      expect(filterFns.contains.autoRemove(1)).toBe(false);
    });
  });

  describe('startsWith', () => {
    it('returns true if value starts with filterValue (case-insensitive, trimmed)', () => {
      const row = mockRow('  Hello World  ');
      expect(filterFns.startsWith(row as any, 'col', 'hello')).toBe(true);
      expect(filterFns.startsWith(row as any, 'col', '  hello')).toBe(true);
    });

    it('returns false if value does not start with filterValue', () => {
      const row = mockRow('abc');
      expect(filterFns.startsWith(row as any, 'col', 'b')).toBe(false);
    });

    it('handles numeric values', () => {
      const row = mockRow(12345);
      expect(filterFns.startsWith(row as any, 'col', 12)).toBe(true);
      expect(filterFns.startsWith(row as any, 'col', 99)).toBe(false);
    });

    it('autoRemove returns true for falsy values', () => {
      expect(filterFns.startsWith.autoRemove(undefined)).toBe(true);
      expect(filterFns.startsWith.autoRemove(null)).toBe(true);
      expect(filterFns.startsWith.autoRemove('')).toBe(true);
      expect(filterFns.startsWith.autoRemove(0)).toBe(true);
    });

    it('autoRemove returns false for truthy values', () => {
      expect(filterFns.startsWith.autoRemove('abc')).toBe(false);
      expect(filterFns.startsWith.autoRemove(1)).toBe(false);
    });
  });
});
