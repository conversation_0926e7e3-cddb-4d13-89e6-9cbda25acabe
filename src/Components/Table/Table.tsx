import {
  Table as MuiTable,
  TableProps as MuiTableProps,
  Paper,
  TableBody,
  TableBodyProps,
  TableCell,
  TableCellProps,
  TableContainer,
  TableContainerProps,
  TableFooter,
  TableFooterProps,
  TableHead,
  TableHeadProps,
  TablePaginationProps,
  TableRow,
  TableRowProps,
} from '@mui/material';
import {
  ColumnDef,
  ColumnFiltersState,
  FilterFn,
  SortingState,
  Updater,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  useReactTable,
} from '@tanstack/react-table';
import {DEFAULT_LIMIT} from 'Pages/Tenants/tenants.utils';
import {JSX, memo, useMemo, useState} from 'react';
import {filterFns} from './FilterFunctions';
import {DefaultColumn, DefaultRow, DefaultTablePagination, GlobalFilter} from './helper';
import TableCustomPagination from './TableCustomPagination';

export interface AnyObject {
  [key: string]: any; // NOSONAR
}

export type MUITablePropsObject = {
  onSortChange?: (columnId: string, isDesc: boolean) => void;
  tableContainerProps?: TableContainerProps;
  tableProps?: MuiTableProps;
  tableHeadProps?: TableHeadProps;
  headerRowProps?: TableRowProps;
  tableBodyProps?: TableBodyProps;
  tableFooterProps?: TableFooterProps;
  tablePaginationProps?: TablePaginationProps;
  bodyRowProps?: TableRowProps & {
    onClickRow?: (rowIndex: number) => void;
  };
  bodyCellProps?: TableCellProps;
  columnCellProps?: TableCellProps;
};

export interface TableProps<T extends AnyObject> {
  onSortChange?: (columnId: string, isDesc: boolean) => void;
  data: T[];
  tableName?: string;
  initialSortingState?: SortingState;
  limit?: number;
  setLimit?: (newLimit: number) => void;
  offset?: number;
  setOffset?: (newOffset: number) => void;
  count?: number;
  pageSize?: number;
  pageIndex?: number;
  columns: ColumnDef<T>[];
  enableSorting?: boolean;
  handleSortColumnChange?: (columnId: string, desc: boolean) => void;
  enableGlobalFiltering?: boolean;
  globalFilterFn?: FilterFn<T>;
  manualPagination?: boolean;
  enableColumnFiltering?: boolean;
  enablePagination?: boolean;
  rowsPerPageOptions?: Array<number | {label: string; value: number}>;
  tablePropsObject?: MUITablePropsObject;
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  noRecordsMessage?: string;
  excludeSortColumns?: string[];
}
const DEFAULT_ROWS_PER_PAGE = 5;
const ROWS_PER_PAGE_OPTION_1 = 10;
const ROWS_PER_PAGE_OPTION_2 = 25;
const ROWS_PER_PAGE_OPTION_3 = 50;

/**
 * ARCTable is a generic, highly customizable table component built with TanStack Table and Material-UI.
 * It supports features such as sorting, global and column filtering, manual and automatic pagination,
 * and custom rendering for table rows and columns.
 *
 * @template T - The type of data objects in the table.
 *
 * @param {TableProps<T>} props - The properties for configuring the table.
 * @param {T[]} props.data - The array of data objects to display in the table.
 * @param {number} [props.count] - The total number of rows (used for manual pagination).
 * @param {ColumnDef<T, any>[]} props.columns - The column definitions for the table.
 * @param {boolean} [props.enableSorting] - Whether to enable sorting on columns.
 * @param {boolean} [props.enableGlobalFiltering] - Whether to enable global filtering.
 * @param {Function} [props.globalFilterFn=filterFns.fuzzy] - The function to use for global filtering.
 * @param {boolean} [props.enableColumnFiltering] - Whether to enable column-level filtering.
 * @param {boolean} [props.enablePagination] - Whether to enable built-in pagination.
 * @param {Array<number | { label: string; value: number }>} [props.rowsPerPageOptions] - Options for rows per page selection.
 * @param {boolean} [props.manualPagination=false] - If true, enables manual pagination (for server-side data).
 * @param {(columnId: string, desc: boolean) => void} [props.onSortChange] - Callback when sorting changes.
 * @param {(columnId: string, desc: boolean) => void} [props.handleSortColumnChange] - Callback for column sort changes.
 * @param {object} [props.tablePropsObject] - Object containing additional props for table subcomponents.
 * @param {number} [props.limit] - The current limit (rows per page) for manual pagination.
 * @param {(limit: number) => void} [props.setLimit] - Setter for the limit value.
 * @param {number} [props.offset] - The current offset (start row) for manual pagination.
 * @param {number} [props.pageSize=10] - The current page size for built-in pagination.
 * @param {number} [props.pageIndex=0] - The current page index for built-in pagination.
 * @param {(offset: number) => void} [props.setOffset] - Setter for the offset value.
 * @param {(event: React.ChangeEvent<unknown>, page: number) => void} [props.onPageChange] - Callback for page changes (manual pagination).
 * @param {(event: React.ChangeEvent<HTMLInputElement>) => void} [props.onRowsPerPageChange] - Callback for rows per page changes (manual pagination).
 *
 * @returns {JSX.Element} The rendered table component with optional filtering, sorting, and pagination controls.
 */
const ARCTable = <T extends AnyObject>({
  data,
  count,
  columns,
  enableSorting,
  enableGlobalFiltering,
  globalFilterFn = filterFns.fuzzy,
  enableColumnFiltering,
  enablePagination,
  initialSortingState = [],
  rowsPerPageOptions = [
    DEFAULT_ROWS_PER_PAGE,
    ROWS_PER_PAGE_OPTION_1,
    ROWS_PER_PAGE_OPTION_2,
    ROWS_PER_PAGE_OPTION_3,
    {label: 'All', value: data.length},
  ],
  manualPagination = false as boolean, // Ensure this property is defined in the TableProps interface
  onSortChange,
  handleSortColumnChange,
  tablePropsObject,
  limit,
  setLimit,
  offset,
  pageSize = 10,
  pageIndex = 0,
  setOffset,
  onPageChange,
  onRowsPerPageChange,
  noRecordsMessage = 'No records found',
  excludeSortColumns,
}: TableProps<T>) => {
  const [sorting, setSorting] = useState<SortingState>(initialSortingState || []);

  // Handle sorting changes from TanStack table
  /**
   * Handles changes to the table's sorting state.
   *
   * This function updates the sorting state based on the provided updater or value.
   * If the new sorting state is empty but there was a previous sort, it toggles the first sort column back to ascending
   * to prevent disabling sorting entirely. After updating the state, it notifies all relevant sort change handlers
   * with the current sort column and direction.
   *
   * @param updaterOrValue - Either a new `SortingState` array or an updater function that receives the current sorting state and returns a new one.
   */
  const handleSortingChange = (updaterOrValue: Updater<SortingState>) => {
    let newSorting: SortingState;

    if (typeof updaterOrValue === 'function') {
      newSorting = updaterOrValue(sorting);
    } else {
      newSorting = updaterOrValue;
    }

    // Ensure we always have a sort state to prevent disabling
    if (newSorting.length === 0 && sorting.length > 0) {
      // Toggle back to ascending instead of removing sort
      newSorting = [{...sorting[0], desc: false}];
    }

    setSorting(newSorting);

    // Always call sort handlers with current state
    const sortItem = newSorting[0] || {id: '', desc: false};

    if (handleSortColumnChange) {
      handleSortColumnChange(sortItem.id, sortItem.desc);
    }
    if (tablePropsObject?.onSortChange) {
      tablePropsObject?.onSortChange?.(sortItem.id, sortItem.desc);
    }
    if (onSortChange) {
      onSortChange(sortItem.id, sortItem.desc);
    }
  };
  // Calculate current page from offset and limit
  const currentPage = offset && limit ? Math.floor(offset / limit) + 1 : 1;
  const currentRowsPerPage = limit ?? DEFAULT_LIMIT;
  const tableData = useMemo(() => data, [data]);
  const [globalFilter, setGlobalFilter] = useState('');
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);

  const table = useReactTable({
    data: tableData,
    columns,
    state: {
      sorting,
      globalFilter,
      columnFilters,
    },
    globalFilterFn,
    manualPagination,
    manualSorting: true, // Enable manual sorting for API calls
    onSortingChange: handleSortingChange,
    onGlobalFilterChange: setGlobalFilter,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
  });

  const {getHeaderGroups, getRowModel} = table;

  return (
    <>
      <TableContainer component={Paper} elevation={0} {...tablePropsObject?.tableContainerProps}>
        {enableGlobalFiltering && <GlobalFilter globalFilter={globalFilter} setGlobalFilter={setGlobalFilter} />}
        <MuiTable {...tablePropsObject?.tableProps}>
          <TableHead {...tablePropsObject?.tableHeadProps}>
            {getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} {...tablePropsObject?.headerRowProps}>
                {headerGroup.headers.map((header, index) => (
                  <DefaultColumn
                    header={header}
                    index={index}
                    enableColumnFiltering={enableColumnFiltering ?? false}
                    enableSorting={enableSorting ?? false}
                    columnCellProps={{
                      ...tablePropsObject?.columnCellProps,
                    }}
                    key={header.id}
                    hasActiveSorting={table.getState().sorting.length > 0}
                    excludeSortColumns={excludeSortColumns}
                  />
                ))}
              </TableRow>
            ))}
          </TableHead>
          <TableBody {...tablePropsObject?.tableBodyProps}>
            {getRowModel().rows.length > 0 ? (
              getRowModel().rows.map((row, index) => (
                <DefaultRow
                  key={row.id}
                  row={row}
                  index={index}
                  bodyRowProps={tablePropsObject?.bodyRowProps}
                  bodyCellProps={tablePropsObject?.bodyCellProps}
                />
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  align="center"
                  sx={{
                    py: 4,
                    fontWeight: 500,
                    fontSize: '0.8125rem',
                    color: 'body.800',
                  }}
                >
                  {noRecordsMessage}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
          {enablePagination && (
            <TableFooter {...tablePropsObject?.tableFooterProps}>
              <DefaultTablePagination
                rowsPerPageOptions={rowsPerPageOptions}
                count={table.getFilteredRowModel().rows.length}
                pageSize={pageSize}
                pageIndex={pageIndex}
                onPageChange={(_, page) => {
                  table.setPageIndex(page);
                }}
                onRowsPerPageChange={e => {
                  const DEFAULT_PAGE_SIZE = 10;
                  const size = e.target.value ? Number(e.target.value) : DEFAULT_PAGE_SIZE;
                  table.setPageSize(size);
                }}
                tablePaginationProps={tablePropsObject?.tablePaginationProps}
              />
            </TableFooter>
          )}
        </MuiTable>
      </TableContainer>
      {manualPagination && (
        <TableCustomPagination
          currentPage={currentPage}
          currentRowsPerPage={currentRowsPerPage}
          totalCount={count ?? 0}
          rowsPerPageOptions={rowsPerPageOptions.filter(option => typeof option === 'number')}
          onPageChange={onPageChange}
          onRowsPerPageChange={onRowsPerPageChange}
          setLimit={setLimit}
          setOffset={setOffset}
        />
      )}
    </>
  );
};

// React.memo produced typed error while using generic
// Made a type to fix it
// https://github.com/DefinitelyTyped/DefinitelyTyped/issues/37087 refer this issue
type TableType = <T extends AnyObject>(props: TableProps<T>) => JSX.Element;

export const Table = memo(ARCTable) as TableType;
