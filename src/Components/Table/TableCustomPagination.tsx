import {Box, FormControl, MenuItem, Pagination, Select, Typography} from '@mui/material';
import {Integers} from 'Helpers/integers';
import {getDefaultTheme} from 'Providers/theme/ThemeProvider';
import {memo, useCallback, useEffect} from 'react';

export interface TablePaginationProps {
  currentPage: number;
  currentRowsPerPage: number;
  totalCount: number;
  rowsPerPageOptions?: number[];
  onPageChange?: (page: number) => void;
  onRowsPerPageChange?: (rowsPerPage: number) => void;
  setLimit?: (newLimit: number) => void;
  setOffset?: (newOffset: number) => void;
  showEntriesText?: boolean;
  disabled?: boolean;
}

// Constants for pagination
const PAGE_SIZE_SMALL = 10;
const PAGE_SIZE_MEDIUM = 20;
const PAGE_SIZE_LARGE = 50;
const DEFAULT_ROWS_PER_PAGE_OPTIONS = [PAGE_SIZE_SMALL, PAGE_SIZE_MEDIUM, PAGE_SIZE_LARGE];

// Constants for styling dimensions
const SELECT_MIN_WIDTH = 60;
const whiteMain = 'white.main';

const borderStyle = '0.0625rem solid' + getDefaultTheme()?.palette.body[Integers.OneHundred]; // Fallback to a default color if theme is not available

// Styles extracted as constants for better performance
const CONTAINER_STYLES = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  p: 2,
  backgroundColor: 'white',
} as const;

const ROWS_PER_PAGE_CONTAINER_STYLES = {
  display: 'flex',
  alignItems: 'center',
  gap: 1,
} as const;

const TEXT_STYLES = {
  fontSize: '0.8125rem',
  color: 'body.700',
  fontWeight: 600,
} as const;

const SELECT_STYLES = {
  fontSize: '0.875rem',
  minWidth: SELECT_MIN_WIDTH,
  '& .MuiOutlinedInput-notchedOutline': {
    border: borderStyle,
  },
} as const;

const PAGINATION_STYLES = {
  '& .MuiPaginationItem-root': {
    fontSize: '0.875rem',
    minWidth: '2rem',
    height: '2rem',
    margin: '0 0.125rem',
    color: 'body.800',
    borderRadius: '0.5rem',
    border: borderStyle,
    backgroundColor: whiteMain,
    '&:hover': {
      backgroundColor: 'body.100',
    },
    '&.Mui-selected': {
      backgroundColor: 'secondary.main',
      color: whiteMain,
      border: borderStyle,
      '&:hover': {
        backgroundColor: 'body.100',
      },
    },
  },
  '& .MuiPaginationItem-ellipsis': {
    border: 'none',
    backgroundColor: 'transparent',
  },
  '& .MuiPaginationItem-previousNext': {
    backgroundColor: whiteMain,
    border: borderStyle,
    '&:hover': {
      backgroundColor: 'body.100',
    },
  },
} as const;

/**
 * Custom pagination component for tables.
 *
 * Renders a pagination control along with a "rows per page" selector.
 * Handles changes to page and rows per page, and supports disabling, custom options, and entry text display.
 *
 * @param {TablePaginationProps} props - The props for the pagination component.
 * @param {number} props.currentPage - The current page number (1-based).
 * @param {number} props.currentRowsPerPage - The current number of rows displayed per page.
 * @param {number} props.totalCount - The total number of items in the table.
 * @param {number[]} [props.rowsPerPageOptions=DEFAULT_ROWS_PER_PAGE_OPTIONS] - The selectable options for rows per page.
 * @param {(page: number) => void} [props.onPageChange] - Callback fired when the page changes.
 * @param {(rowsPerPage: number) => void} [props.onRowsPerPageChange] - Callback fired when the rows per page changes.
 * @param {(limit: number) => void} [props.setLimit] - Callback to set the limit (rows per page).
 * @param {(offset: number) => void} [props.setOffset] - Callback to set the offset (start index for pagination).
 * @param {boolean} [props.showEntriesText=true] - Whether to show the "Show X entries" text.
 * @param {boolean} [props.disabled=false] - Whether the pagination controls are disabled.
 *
 * @returns {JSX.Element} The rendered pagination component.
 */
const TableCustomPagination: React.FC<TablePaginationProps> = ({
  currentPage,
  currentRowsPerPage,
  totalCount,
  rowsPerPageOptions = DEFAULT_ROWS_PER_PAGE_OPTIONS,
  onPageChange,
  onRowsPerPageChange,
  setLimit,
  setOffset,
  showEntriesText = true,
  disabled = false,
}) => {
  const totalPages = Math.ceil(totalCount / currentRowsPerPage);

  const handleRowsPerPageChange = useCallback(
    (newLimit: number) => {
      if (setLimit) {
        setLimit(newLimit);
      }
      if (setOffset) {
        setOffset(0); // Reset to first page
      }
      if (onRowsPerPageChange) {
        onRowsPerPageChange(newLimit);
      }
    },
    [setLimit, setOffset, onRowsPerPageChange],
  );

  // Ensure currentRowsPerPage is in the available options, fallback to first option if not
  const validRowsPerPage = rowsPerPageOptions.includes(currentRowsPerPage) ? currentRowsPerPage : rowsPerPageOptions[0];

  // Auto-correct invalid rowsPerPage values
  useEffect(() => {
    if (!rowsPerPageOptions.includes(currentRowsPerPage) && rowsPerPageOptions.length > 0) {
      handleRowsPerPageChange(rowsPerPageOptions[0]);
    }
  }, [currentRowsPerPage, rowsPerPageOptions, handleRowsPerPageChange]);

  const handlePageChange = useCallback(
    (_: unknown, page: number) => {
      const newOffset = (page - 1) * currentRowsPerPage;
      if (setOffset) {
        setOffset(newOffset);
      }
      if (onPageChange) {
        onPageChange(page);
      }
    },
    [currentRowsPerPage, setOffset, onPageChange],
  );

  return (
    <Box sx={CONTAINER_STYLES}>
      {/* Rows per page selector */}
      <Box sx={ROWS_PER_PAGE_CONTAINER_STYLES}>
        {showEntriesText && <Typography sx={TEXT_STYLES}>Show</Typography>}
        <FormControl size="small" disabled={disabled}>
          <Select
            value={validRowsPerPage}
            onChange={e => handleRowsPerPageChange(Number(e.target.value))}
            sx={SELECT_STYLES}
            disabled={disabled}
            data-testid="rows-per-page"
          >
            {rowsPerPageOptions.map(option => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        {showEntriesText && <Typography sx={TEXT_STYLES}>entries</Typography>}
      </Box>

      {/* Pagination controls */}
      <Pagination
        count={totalPages}
        page={currentPage}
        onChange={handlePageChange}
        shape="rounded"
        siblingCount={2}
        boundaryCount={1}
        disabled={disabled}
        data-testid="next-page"
        sx={PAGINATION_STYLES}
      />
    </Box>
  );
};

export default memo(TableCustomPagination);
