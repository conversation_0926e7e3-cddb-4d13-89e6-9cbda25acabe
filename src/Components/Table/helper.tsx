import {
  Box,
  TablePaginationProps as MuiTablePaginationProps,
  TableRowProps as MuiTableRowProps,
  TablePagination,
  TableRow,
} from '@mui/material';
import TableCell, {TableCellProps} from '@mui/material/TableCell';
import TableSortLabel from '@mui/material/TableSortLabel';
import {Header, Row, flexRender} from '@tanstack/react-table';
import {JSX} from 'react';
import {DebouncedInput} from './DebounceInput';
import TablePaginationActions from './PaginationActions';
import {AnyObject} from './Table';

interface ColumnProps<T extends AnyObject> {
  header: Header<T, unknown>;
  index: number;
  enableSorting?: boolean;
  enableColumnFiltering?: boolean;
  columnCellProps?: TableCellProps;
  hasActiveSorting?: boolean;
  excludeSortColumns?: string[];
}

type TableRowProps<T extends AnyObject> = {
  row: Row<T>;
  index: number;
  bodyCellProps?: TableCellProps;
  bodyRowProps?: MuiTableRowProps & {
    onClickRow?: (index: number) => void;
  };
};

const getSortDirection = <T extends AnyObject>(header: Header<T, unknown>): 'asc' | 'desc' => {
  const sortStatus = header.column.getIsSorted();
  if (sortStatus === false) {
    if (header.column.id === 'createdDate') {
      return 'desc';
    }
    return 'asc';
  }
  return sortStatus;
};

const defaultExcludeSortColumns = [
  'Actions',
  'status',
  'billingCycle',
  'configureDevice',
  'invoiceStatus',
  'leadFullName',
  'leadPhoneNumber',
  'leadEmail',
];

export const DefaultColumn = <T extends AnyObject>({
  header,
  enableSorting,
  enableColumnFiltering,
  columnCellProps,
  hasActiveSorting = false,
  excludeSortColumns = defaultExcludeSortColumns,
}: ColumnProps<T>): JSX.Element => {
  return (
    <TableCell key={header.id} sx={{fontWeight: 'bold'}} {...columnCellProps}>
      {header.isPlaceholder ? null : flexRender(header.column.columnDef.header, header.getContext())}
      {enableSorting && !excludeSortColumns.includes(header.column.id) && (
        <TableSortLabel
          active={!!header.column.getIsSorted() || (header.column.id === 'createdDate' && !hasActiveSorting)}
          onClick={header.column.getToggleSortingHandler()}
          direction={getSortDirection(header)}
          sx={{
            '& .MuiTableSortLabel-icon': {
              width: '0.75rem',
              height: '0.75rem',
            },
          }}
        />
      )}
      {enableColumnFiltering && header.column.getCanFilter() ? (
        <DebouncedInput
          id={`${header.column.columnDef.header}-search`}
          value={(() => {
            const filterValue = header.column.getFilterValue();
            if (typeof filterValue === 'number') {
              return filterValue;
            }
            return (filterValue ?? '') as string;
          })()}
          onChange={value => header.column.setFilterValue(value)}
          placeholder={`Search ${header.column.columnDef.header}`}
          variant="standard"
          sx={{width: '9.375rem', height: '2rem', marginTop: '0.3125rem'}}
        />
      ) : null}
    </TableCell>
  );
};

export const DefaultRow = <T extends AnyObject>({row, bodyCellProps, bodyRowProps}: TableRowProps<T>) => {
  return (
    <TableRow
      key={row.id}
      sx={{'&:last-child td, &:last-child th': {border: 0}}}
      {...bodyRowProps}
      onClick={() => bodyRowProps?.onClickRow?.(row.index)}
    >
      {row.getVisibleCells().map(cell => (
        <TableCell key={cell.id} {...bodyCellProps}>
          {flexRender(cell.column.columnDef.cell, cell.getContext())}
        </TableCell>
      ))}
    </TableRow>
  );
};

interface GlobalFilterProps {
  globalFilter: string;
  setGlobalFilter: React.Dispatch<React.SetStateAction<string>>;
}

export const GlobalFilter: React.FC<GlobalFilterProps> = ({globalFilter, setGlobalFilter}: GlobalFilterProps) => {
  return (
    <Box display="flex" justifyContent="flex-end">
      <DebouncedInput
        id="global-search"
        value={globalFilter ?? ''}
        onChange={value => setGlobalFilter(String(value))}
        variant="outlined"
      />
    </Box>
  );
};

type TablePaginationProps = {
  rowsPerPageOptions: (number | {label: string; value: number})[];
  count: number;
  pageSize: number;
  pageIndex: number;
  onPageChange: (event: React.MouseEvent<HTMLButtonElement> | null, newPage: number) => void;
  onRowsPerPageChange: (event: React.ChangeEvent<HTMLTextAreaElement | HTMLInputElement>) => void;
  tablePaginationProps?: MuiTablePaginationProps;
};

export const DefaultTablePagination: React.FC<TablePaginationProps> = ({
  rowsPerPageOptions,
  count,
  pageSize,
  pageIndex,
  onPageChange,
  onRowsPerPageChange,
  tablePaginationProps,
}: TablePaginationProps) => {
  return (
    <TablePagination
      rowsPerPageOptions={rowsPerPageOptions}
      component="div"
      count={count}
      rowsPerPage={pageSize}
      page={pageIndex}
      SelectProps={{
        inputProps: {'aria-label': 'rows per page'},
        native: true,
      }}
      onPageChange={onPageChange}
      onRowsPerPageChange={onRowsPerPageChange}
      ActionsComponent={TablePaginationActions}
      {...tablePaginationProps}
    />
  );
};
