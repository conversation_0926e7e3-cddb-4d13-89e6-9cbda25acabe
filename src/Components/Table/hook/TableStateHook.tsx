import {DEFAULT_LIMIT, DEFAULT_OFFSET} from 'Pages/Tenants/tenants.utils';
import React, {useCallback} from 'react';

/**
 * Custom hook to manage table state for pagination .
 * @returns {Object} An object containing table state and handlers.
 */
export const useTableState = () => {
  const [limit, setLimit] = React.useState<number>(DEFAULT_LIMIT);
  const [offset, setOffset] = React.useState<number>(DEFAULT_OFFSET);

  const handlePageChange = useCallback(
    (page: number) => {
      const newOffset = (page - 1) * limit;
      setOffset(newOffset);
    },
    [limit],
  );

  const handleRowsPerPageChange = useCallback((newLimit: number) => {
    setLimit(newLimit);
    setOffset(0); // Reset to first page when changing page size
  }, []);

  return {
    limit,
    setLimit,
    offset,
    setOffset,
    handlePageChange,
    handleRowsPerPageChange,
  };
};
