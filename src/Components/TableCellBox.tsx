import {Box, BoxProps} from '@mui/material';
import {FC} from 'react';
import {DefaultToolTip} from './ToolTipTypography/ToolTipTypography';

/**
 * A wrapper component for table cell content that ensures text overflow is handled gracefully.
 * Renders a Material-UI `Box` as a `span` element with ellipsis for overflowing text.
 *
 * @param props - Props extending Material-UI `BoxProps`.
 * @returns A `span` element containing the children, styled for text overflow.
 */
export const TableCellBox: FC<BoxProps> = props => (
  <Box
    {...props}
    component={'span'}
    sx={{
      overflow: 'hidden',
      textOverflow: 'ellipsis',
      display: 'inline-block',
      maxWidth: '100%',
      ...props.sx,
    }}
  >
    {props.children}
  </Box>
);

export const TableCellBoxWithToolTip: FC<BoxProps> = props => (
  <DefaultToolTip title={props.children}>
    <TableCellBox {...props} />
  </DefaultToolTip>
);
