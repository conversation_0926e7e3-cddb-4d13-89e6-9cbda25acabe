// @vitest-environment jsdom
import {fireEvent, render, screen} from '@testing-library/react';
import {MemoryRouter} from 'react-router-dom';
import {afterEach, beforeEach, describe, expect, it, vi} from 'vitest';
import TableOfContent from './TableOfContent';

const headingsMock = [
  {id: 'h1', innerHTML: 'Heading 1'},
  {id: 'h2', innerHTML: 'Heading 2'},
];

const getElementsByClassNameMock = vi.fn();

beforeEach(() => {
  vi.useFakeTimers();
  // Mock getElementsByClassName for useTitles and useObserver
  getElementsByClassNameMock.mockImplementation(className => {
    if (className === 'headings') {
      return headingsMock;
    }
    return [];
  });
  Object.defineProperty(document, 'getElementsByClassName', {
    value: getElementsByClassNameMock,
    configurable: true,
  });

  // Mock IntersectionObserver with explicit this typing
  global.IntersectionObserver = vi.fn(function (this: any, cb) {
    this.observe = vi.fn();
    this.disconnect = vi.fn();
    // Immediately call callback with all headings as intersecting
    cb(
      headingsMock.map(h => ({
        target: h,
        isIntersecting: true,
      })),
    );
  }) as any;

  // Mock getElementById for scrollIntoView
  document.getElementById = vi.fn(id => ({
    scrollIntoView: vi.fn(),
  })) as any;
});

afterEach(() => {
  vi.useRealTimers();
  vi.clearAllMocks();
});

function renderWithRouter(ui: React.ReactElement) {
  return render(<MemoryRouter>{ui}</MemoryRouter>);
}

describe('TableOfContent', () => {
  it('renders headings from DOM', () => {
    renderWithRouter(<TableOfContent />);
    expect(screen.getByText('Heading 1')).toBeInTheDocument();
    expect(screen.getByText('Heading 2')).toBeInTheDocument();
  });

  it('renders TimelineDot for active heading', () => {
    renderWithRouter(<TableOfContent />);
    vi.runAllTimers();
    // Try to find TimelineDot by tag and style (span with backgroundColor)
    const dot = Array.from(document.querySelectorAll('span')).find(
      el => el.style.backgroundColor === 'rgb(25, 165, 255)' || el.className.includes('MuiTimelineDot'),
    );
    expect(dot).toBeDefined();
  });

  it('calls scrollIntoView on click', () => {
    renderWithRouter(<TableOfContent />);
    const link = screen.getByRole('link', {name: 'Heading 1'});
    fireEvent.click(link);
    vi.runAllTimers();
    expect(document.getElementById).toHaveBeenCalledWith('h1');
  });

  it('handles no headings gracefully', () => {
    getElementsByClassNameMock.mockReturnValueOnce([]);
    renderWithRouter(<TableOfContent />);
    expect(screen.queryByRole('list')).not.toBeInTheDocument();
  });
});
