// @vitest-environment jsdom
import {useTheme} from '@mui/material/styles';
import {fireEvent, render, screen} from '@testing-library/react';
import ThemeSwitch from './ThemeSwitch';

vi.mock('@mui/material/styles', async () => {
  const actual = await vi.importActual<any>('@mui/material/styles');
  return {
    ...actual,
    useTheme: vi.fn(),
  };
});

import {ThemeContext} from 'Providers/theme/ThemeProvider';
vi.mock('Providers/theme/ThemeProvider', () => {
  const React = require('react');
  return {
    ThemeContext: React.createContext({toggleColorMode: () => {}}),
  };
});

describe('ThemeSwitch', () => {
  const toggleColorMode = vi.fn();
  beforeEach(() => {
    vi.resetAllMocks();
  });

  it('renders Brightness7Icon when theme is dark', () => {
    vi.mocked(useTheme).mockReturnValue({palette: {mode: 'dark'}});
    render(
      <ThemeContext.Provider value={{toggleColorMode}}>
        <ThemeSwitch />
      </ThemeContext.Provider>,
    );
    expect(screen.getByTestId('Brightness7Icon')).toBeInTheDocument();
  });

  it('renders Brightness4Icon when theme is light', () => {
    vi.mocked(useTheme).mockReturnValue({palette: {mode: 'light'}});
    render(
      <ThemeContext.Provider value={{toggleColorMode}}>
        <ThemeSwitch />
      </ThemeContext.Provider>,
    );
    expect(screen.getByTestId('Brightness4Icon')).toBeInTheDocument();
  });

  it('calls toggleColorMode on button click', () => {
    vi.mocked(useTheme).mockReturnValue({palette: {mode: 'dark'}});
    render(
      <ThemeContext.Provider value={{toggleColorMode}}>
        <ThemeSwitch />
      </ThemeContext.Provider>,
    );
    fireEvent.click(screen.getByRole('button'));
    expect(toggleColorMode).toHaveBeenCalled();
  });
});
