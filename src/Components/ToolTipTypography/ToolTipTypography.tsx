import {SxProps, Tooltip, TooltipProps, Typography} from '@mui/material';
import {FC} from 'react';
import {toolTipStyles} from 'styles/pages/TenantPage.styles';

/**
 * Props for the TooltipTypography component.
 *
 */ export interface TooltipTypographyProps {
  message: string;
  sx?: SxProps;
  disableInteraction?: boolean;
}

/**
 * Default tooltip component.
 * @param props - The props for the tooltip component {@link TooltipProps}.
 * @returns The rendered tooltip component.
 */
export const DefaultToolTip: FC<TooltipProps> = props => (
  <Tooltip arrow placement="top" {...props} slotProps={toolTipStyles} />
);

/**
 * TooltipTypography component.
 * @param param - The props for the component {@link TooltipTypographyProps}.
 * @returns The rendered component.
 */
export const ToolTipTypography = ({message, sx, disableInteraction}: TooltipTypographyProps) => (
  <DefaultToolTip title={message} arrow disableInteractive={disableInteraction} slotProps={toolTipStyles}>
    <Typography sx={{...sx}}>{message}</Typography>
  </DefaultToolTip>
);
export default ToolTipTypography;
