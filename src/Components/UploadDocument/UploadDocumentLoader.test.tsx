// import { render, screen } from '@testing-library/react';
// import { vi } from 'vitest';
// import UploadDocumentLoader from './UploadDocumentLoader';

// // No need to mock MUI styles unless they break tests
// describe('UploadDocumentLoader', () => {
//     beforeEach(() => {
//         vi.clearAllMocks();
//     });

//     it('should render the loader container', () => {
//         render(<UploadDocumentLoader />);

//         const loader = screen.getByTestId('upload-document-loader');
//         expect(loader).toBeInTheDocument();
//     });

//     it('should render title skeleton', () => {
//         render(<UploadDocumentLoader />);

//         const titleSkeleton = screen.getAllByRole('presentation').find(
//             (el) => el.className.includes('MuiSkeleton-text')
//         );

//         expect(titleSkeleton).toBeInTheDocument();
//     });

//     it('should render 3 loading items in the stack', () => {
//         render(<UploadDocumentLoader />);

//         const textSkeletons = screen.getAllByRole('presentation').filter((el) =>
//             el.className.includes('MuiSkeleton-text')
//         );

//         // One title + 3 stack items + 1 footer = 5 text skeletons
//         expect(textSkeletons.length).toBeGreaterThanOrEqual(5); // or === 5 strictly
//     });

//     it('should render rectangular preview skeleton', () => {
//         render(<UploadDocumentLoader />);

//         const previewSkeleton = screen.getAllByRole('presentation').find(
//             (el) => el.className.includes('MuiSkeleton-rectangular')
//         );

//         expect(previewSkeleton).toBeInTheDocument();
//     });
// });

import {render, screen} from '@testing-library/react';
import {vi} from 'vitest';
import UploadDocumentLoader from './UploadDocumentLoader';

describe('UploadDocumentLoader', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the loader container', () => {
    render(<UploadDocumentLoader />);
    expect(screen.getByTestId('upload-document-loader')).toBeInTheDocument();
  });

  it('should render title skeleton', () => {
    render(<UploadDocumentLoader />);
    expect(screen.getByTestId('title-skeleton')).toBeInTheDocument();
  });

  it('should render 3 item skeletons', () => {
    render(<UploadDocumentLoader />);
    expect(screen.getByTestId('item-skeleton-0')).toBeInTheDocument();
    expect(screen.getByTestId('item-skeleton-1')).toBeInTheDocument();
    expect(screen.getByTestId('item-skeleton-2')).toBeInTheDocument();
  });

  it('should render rectangular preview skeleton', () => {
    render(<UploadDocumentLoader />);
    expect(screen.getByTestId('preview-skeleton')).toBeInTheDocument();
  });

  it('should render footer text skeleton', () => {
    render(<UploadDocumentLoader />);
    expect(screen.queryByTestId('footer-skeleton')).toBeTruthy();
  });
});
