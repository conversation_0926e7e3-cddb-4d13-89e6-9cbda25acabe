import {Box, Paper, Skeleton, Stack} from '@mui/material';
import {uploadDocumentLoaderStyles as styles} from './uploadDocumentLoader.styles';

const UploadDocumentLoader = () => {
  return (
    <Box sx={styles.container}>
      <Paper sx={styles.paper} data-testid="upload-document-loader">
        <Box sx={styles.contentContainer}>
          <Skeleton variant="text" sx={styles.titleSkeleton} data-testid="title-skeleton" />

          <Stack spacing={2}>
            {[...Array(3)].map((_, idx) => {
              const uniqueKey = `skeleton-item-${idx}`;
              return (
                <Box key={uniqueKey} sx={styles.itemContainer}>
                  <Skeleton variant="text" sx={styles.itemSkeleton} data-testid={`item-skeleton-${idx}`} />
                </Box>
              );
            })}
          </Stack>
        </Box>

        <Box sx={styles.previewContainer}>
          <Skeleton variant="rectangular" sx={styles.previewSkeleton} data-testid="preview-skeleton" />
          <Skeleton variant="text" sx={styles.footerSkeleton} data-testid="footer-skeleton" />
        </Box>
      </Paper>
    </Box>
  );
};

export default UploadDocumentLoader;
