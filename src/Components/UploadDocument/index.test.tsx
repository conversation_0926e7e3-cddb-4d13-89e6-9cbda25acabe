import {fireEvent, render, screen} from '@testing-library/react';
import {vi} from 'vitest';
import UploadDocuments from './index';

const mockOnUpload = vi.fn();
const mockOnRemoveFile = vi.fn();
const mockOnRemoveExistingFile = vi.fn();

const mockFiles = [
  new File(['file-content'], 'test1.pdf', {type: 'application/pdf'}),
  new File(['file-content'], 'test2.docx', {
    type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  }),
];

const mockExistingFiles = [
  {
    id: '1',
    originalName: 'existing1.pdf',
    url: 'http://example.com/existing1.pdf',
    tenantId: 'tenant1',
    fileKey: 'fileKey1',
    source: 1,
    size: 12345,
  },
  {
    id: '2',
    originalName: 'existing2.docx',
    url: 'http://example.com/existing2.docx',
    tenantId: 'tenant2',
    fileKey: 'fileKey2',
    source: 2,
    size: 67890,
  },
];

vi.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: vi.fn(),
  }),
}));

vi.mock('Components/FileDropZone', () => ({
  __esModule: true,
  default: ({onDrop}: any) => (
    <button data-testid="dropzone" onClick={() => onDrop(mockFiles)}>
      MockDropZone
    </button>
  ),
}));

vi.mock('Components/FileViewCard', () => ({
  __esModule: true,
  default: ({fileDetail, handleRemoveFile, handleDownload}: any) => (
    <div>
      <span>{fileDetail.originalName || fileDetail.name}</span>
      {handleRemoveFile && (
        <button data-testid={`remove-${fileDetail.originalName || fileDetail.name}`} onClick={handleRemoveFile}>
          Remove
        </button>
      )}
      {handleDownload && (
        <button data-testid={`download-${fileDetail.originalName || fileDetail.name}`} onClick={handleDownload}>
          Download
        </button>
      )}
    </div>
  ),
}));

describe('UploadDocuments', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders upload section and empty state', () => {
    render(<UploadDocuments onUpload={mockOnUpload} onRemoveFile={mockOnRemoveFile} />);
    expect(screen.getByText(/Upload documents/i)).toBeInTheDocument();
    expect(screen.getByText(/Please upload a signed copy/i)).toBeInTheDocument();
    expect(screen.getByTestId('dropzone')).toBeInTheDocument();
  });

  it('calls onUpload when files are dropped', () => {
    render(<UploadDocuments onUpload={mockOnUpload} onRemoveFile={mockOnRemoveFile} />);
    fireEvent.click(screen.getByTestId('dropzone'));
    expect(mockOnUpload).toHaveBeenCalledWith(mockFiles);
    expect(screen.getByText('test1.pdf')).toBeInTheDocument();
    expect(screen.getByText('test2.docx')).toBeInTheDocument();
  });

  it('removes a file when remove button is clicked', () => {
    render(<UploadDocuments onUpload={mockOnUpload} onRemoveFile={mockOnRemoveFile} files={mockFiles} />);
    const removeBtn = screen.getByTestId('remove-test1.pdf');
    fireEvent.click(removeBtn);
    expect(mockOnRemoveFile).toHaveBeenCalled();
  });

  it('renders existing files and removes them', () => {
    render(
      <UploadDocuments
        onUpload={mockOnUpload}
        onRemoveFile={mockOnRemoveFile}
        existingFiles={mockExistingFiles}
        onRemoveExistingFile={mockOnRemoveExistingFile}
      />,
    );
    expect(screen.getByText('existing1.pdf')).toBeInTheDocument();
    expect(screen.getByText('existing2.docx')).toBeInTheDocument();
    fireEvent.click(screen.getByTestId('remove-existing1.pdf'));
    expect(mockOnRemoveExistingFile).toHaveBeenCalled();
  });

  it('calls handleDownload for existing files', () => {
    render(
      <UploadDocuments
        onUpload={mockOnUpload}
        onRemoveFile={mockOnRemoveFile}
        existingFiles={mockExistingFiles}
        onRemoveExistingFile={mockOnRemoveExistingFile}
      />,
    );
    const downloadBtn = screen.getByTestId('download-existing1.pdf');
    fireEvent.click(downloadBtn);
    // handleDownload is a helper, so just check button exists and is clickable
  });
});
