import StatusChip from 'Components/StatusChip/StatusChip';
import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import {isNull} from 'lodash';
import {UserStatus} from 'Pages/UserManagementPage/userManagement.utils';
import {UserViewType} from 'redux/app/types';

/**
 * Returns the label for a given user status.
 * @param status - The user status or its numeric value.
 * @returns Status label string.
 */
export const getUserStatusLabel = (status: UserStatus | number): string => {
  const statusLabelMap: Record<UserStatus | number, string> = {
    [UserStatus.ACTIVE]: 'Active',
    [UserStatus.INACTIVE]: 'Inactive',
    [UserStatus.PENDINGPROVISION]: 'Pending activation',
  };
  return statusLabelMap[status] ?? 'Pending activation';
};

const userStatusMapToStatusChip: Record<UserStatus, StatusChipState> = {
  [UserStatus.ACTIVE]: StatusChipState.ACTIVE,
  [UserStatus.PENDINGPROVISION]: StatusChipState.PENDINGPROVISION,
  [UserStatus.INACTIVE]: StatusChipState.INACTIVE,
};

/**
 * UserStatusChip component displays a status chip for a given user.
 * It maps the user's status to a corresponding chip state and label.
 *
 * Props:
 * - user: UserViewType - The user object containing status information.
 *
 * Example usage:
 * <UserStatusChip user={user} />
 */
const UserStatusChip = ({user}: {user: UserViewType}) => {
  const status = userStatusMapToStatusChip[user.status as UserStatus];

  return <>{!isNull(status) && <StatusChip label={getUserStatusLabel(user.status)} status={status} />}</>;
};

export default UserStatusChip;
