import dayjs from 'dayjs';

export * from './permissions';

// Enum identifying different API service slices used in the application
export enum ApiSliceIdentifier {
  TENANT_FACADE, // Provides an interface for tenant-related operations
  AUDIT_FACADE, // Provides an interface for audit-related operations
}

export const REGISTERED_DOMAIN = '.distek-bione.com';

export const defaultTableDateFormatting = (date: Date | number | string) => {
  try {
    const d = new Date(date);
    const formattedDate = dayjs(d).format('DD MMM, YYYY');
    return formattedDate;
  } catch {
    return '';
  }
};
export const defaultDateFormat = (date: Date | number | string) => defaultTableDateFormatting(date);
export const emailRegEx = /^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;
