import PermissionKey from 'Constants/enums/permissions';

const PK = PermissionKey;

const serverPermissionMapper: Record<string, string[]> = {
  // Tenant Permission
  [PermissionKey.CreateTenant]: [
    PK.ViewConfigureDevices,
    PK.ViewBillingCycle,
    PK.CreateBillingCustomer,
    PK.CreatePrice,
  ],
  [PermissionKey.UpdateTenant]: [
    PK.ViewConfigureDevices,
    PK.ViewBillingCycle,
    PK.CreateBillingCustomer,
    PK.CreatePrice,
  ],
  [PermissionKey.ViewTenant]: [PK.ViewAllStatuses, PK.ViewBillingCycle, PK.ViewTenantBillings, PK.ViewSubscription],
  [PermissionKey.DeleteTenant]: [],

  // Plan Permission
  [PermissionKey.CreatePlan]: [PK.ViewConfigureDevices, PK.ViewCurrency],
  [PermissionKey.UpdatePlan]: [PK.ViewPlanHistory, PK.CreatePlanHistory],
  [PermissionKey.ViewPlan]: [PK.ViewBillingCycle],
  [PermissionKey.DeletePlan]: [],

  // Billing & Invoices
  [PermissionKey.ViewInvoice]: [PK.ViewTenantBillings, PK.ViewBillingCycle, PK.DownloadInvoice],

  // User Management
  [PermissionKey.CreateTenantUser]: [],
  [PermissionKey.UpdateTenantUser]: [],
  [PermissionKey.ViewTenantUser]: [],
  [PermissionKey.DeleteTenant]: [],
};

export const getAllCombinedPermissions = (permissionFromView: string[]): string[] => {
  const restOfThePermissions = new Set<string>();
  permissionFromView.forEach(permission => {
    const mappedPermissions = serverPermissionMapper[permission] || [];
    mappedPermissions.forEach(perm => restOfThePermissions.add(perm));
    restOfThePermissions.add(permission);
  });
  restOfThePermissions.add(PermissionKey.DashboardMetrics); // add this to all categories
  return Array.from(restOfThePermissions);
};

export default serverPermissionMapper;
