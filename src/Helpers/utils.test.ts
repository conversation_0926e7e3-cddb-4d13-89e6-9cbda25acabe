// @vitest-environment jsdom
import {
  convertToDate,
  convertToTitleCase,
  fileExtensionsToUpload,
  getErrorMessage,
  getValue,
  handleDownload,
  MAX_FILE_SIZE,
  MAX_FILES,
} from './utils';

describe('utils', () => {
  it('handleDownload triggers download with correct attributes', () => {
    const appendChild = vi.spyOn(document.body, 'appendChild');
    const removeChild = vi.spyOn(document.body, 'removeChild');
    const click = vi.fn();
    const origCreateElement = document.createElement;
    vi.spyOn(document, 'createElement').mockImplementation((tag: string) => {
      if (tag === 'a') {
        const a = origCreateElement.call(document, tag);
        Object.defineProperty(a, 'click', {value: click});
        return a;
      }
      return origCreateElement.call(document, tag);
    });
    handleDownload('http://example.com/file.pdf', 'file.pdf', '_blank');
    expect(appendChild).toHaveBeenCalled();
    expect(click).toHaveBeenCalled();
    expect(removeChild).toHaveBeenCalled();
    appendChild.mockRestore();
    removeChild.mockRestore();
  });

  it('handleDownload does nothing if url is falsy', () => {
    expect(handleDownload('', 'file.pdf')).toBeUndefined();
  });

  it('convertToTitleCase works for camelCase and empty', () => {
    expect(convertToTitleCase('helloWorld')).toBe('Hello World');
    expect(convertToTitleCase('')).toBe('');
    expect(convertToTitleCase(undefined as any)).toBe(undefined);
  });

  it('convertToDate returns formatted date', () => {
    expect(convertToDate('2020-01-01')).toMatch(/\d{2}\.\d{2}\.\d{4}/);
    expect(convertToDate(1577836800000)).toMatch(/\d{2}\.\d{2}\.\d{4}/);
    expect(convertToDate(new Date('2020-01-01'))).toMatch(/\d{2}\.\d{2}\.\d{4}/);
    expect(convertToDate(undefined as any)).toBe(undefined);
  });

  it('getValue gets nested value and handles missing', () => {
    const obj = {a: {b: [{c: 5}]}};
    expect(getValue(obj, 'a.b[0].c')).toBe(5);
    expect(getValue(obj, 'a.b[1].c')).toBe('');
    expect(getValue(obj, 'x.y')).toBe('');
  });

  describe('getErrorMessage', () => {
    it('returns composite constraint message', () => {
      const error = {
        data: {
          error: {
            message: 'Unique constraint violation error: Key (name, billing_cycle_id)=(PlanA, 123) already exists.',
          },
        },
      };
      expect(getErrorMessage(error as any)).toMatch(/already exists for the selected subscription type/);
    });

    it('returns lower(name::text) format message', () => {
      const error = {
        data: {
          error: {
            message: 'Unique constraint violation error: Key (lower(name::text))=(planb) already exists.',
          },
        },
      };
      expect(getErrorMessage(error as any)).toMatch(/already in use/);
    });

    it('returns plain unique constraint message', () => {
      const error = {
        data: {
          error: {
            message: 'Unique constraint violation error: Key (name)=(PlanC) already exists.',
          },
        },
      };
      expect(getErrorMessage(error as any)).toMatch(/already in use/);
    });

    it('returns default unique constraint message', () => {
      const error = {
        data: {
          error: {
            message: 'Unique constraint violation error: something else',
          },
        },
      };
      expect(getErrorMessage(error as any)).toBe('Unique constraint violation error');
    });

    it('returns invalid input message', () => {
      const error = {
        data: {
          error: {
            message: 'The request body is invalid',
          },
        },
      };
      expect(getErrorMessage(error as any)).toBe('Invalid input. Please check the entered details.');
    });

    it('returns internal server error message', () => {
      const error = {
        data: {
          error: {
            message: 'Internal Server Error',
          },
        },
      };
      expect(getErrorMessage(error as any)).toBe('An unexpected error occurred. Please try again.');
    });

    it('returns raw error message if no match', () => {
      const error = {
        data: {
          error: {
            message: 'Some other error',
          },
        },
      };
      expect(getErrorMessage(error as any)).toBe('Some other error');
    });

    it('returns fallback message if error is undefined', () => {
      expect(getErrorMessage(undefined)).toBe('An unexpected error occurred. Please try again.');
    });
  });

  it('constants are correct', () => {
    expect(MAX_FILE_SIZE).toBe(10 * 1024 * 1024);
    expect(MAX_FILES).toBe(10);
    expect(fileExtensionsToUpload['application/pdf']).toContain('.pdf');
  });
});
