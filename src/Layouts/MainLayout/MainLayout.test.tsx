/* eslint-disable testing-library/no-node-access */
import {render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import NotificationProvider from 'Providers/NotificationProvider';
import {Provider} from 'react-redux';
import {BrowserRouter} from 'react-router-dom';
import {store} from '../../redux/store';
import Mainlayout from './Mainlayout';

const MockMainLayout = () => (
  <Provider store={store}>
    <NotificationProvider>
      <BrowserRouter>
        <Mainlayout />
      </BrowserRouter>
    </NotificationProvider>
  </Provider>
);

describe('MainLayout', () => {
  it('should be render sidenav on initially', () => {
    render(<MockMainLayout />);
    const sidenav = screen.getByTestId('sidenav');
    expect(sidenav.firstChild).toBeVisible();
  });
  it('should toggle the sidenav on clicking menu icon', async () => {
    render(<MockMainLayout />);
    await userEvent.click(screen.getByRole('button', {name: /menu/i}));
    await waitFor(() => expect(screen.getByTestId('sidenav').firstChild).not.toBeVisible());
    await userEvent.click(screen.getByRole('button', {name: /menu/i}));
    await waitFor(() => expect(screen.getByTestId('sidenav').firstChild).toBeVisible());
  });
});
