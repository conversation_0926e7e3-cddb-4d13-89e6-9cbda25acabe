// @vitest-environment jsdom
import '@testing-library/jest-dom';
import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {vi} from 'vitest';
import FilterStatusChips from './BillingStatusFilterChip';

const mockUseGetInvoiceStatusQuery = vi.fn();

vi.mock('redux/app/invoiceManagementApiSlice', () => ({
  useGetInvoiceStatusQuery: (...args: any[]) => mockUseGetInvoiceStatusQuery(...args),
}));

vi.mock('../../Components/FilterChip/FilterChip', () => ({
  __esModule: true,
  default: (props: any) => (
    <button
      data-testid={`chip-${props.label}`}
      onClick={props.onClick}
      aria-selected={props.selected}
      onDoubleClick={props.onDelete}
    >
      {props.label}
    </button>
  ),
}));
vi.mock('../../Components/FilterChip/FilterSectionView', () => ({
  __esModule: true,
  default: (props: any) => (
    <div data-testid="section">
      {props.title}
      {props.children}
    </div>
  ),
}));

vi.mock('./billings.utils', () => ({
  getStatusLabel: (status: string) => {
    if (status === 'PAID') return 'Paid';
    if (status === 'UNPAID') return 'Unpaid';
    return status;
  },
}));

describe('BillingStatusFilterChip', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state', () => {
    mockUseGetInvoiceStatusQuery.mockReturnValue({isLoading: true});
    render(<FilterStatusChips />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('renders error state', () => {
    mockUseGetInvoiceStatusQuery.mockReturnValue({isLoading: false, error: true});
    render(<FilterStatusChips />);
    expect(screen.getByText(/Something Went Wrong/i)).toBeInTheDocument();
  });

  it('renders empty state', () => {
    mockUseGetInvoiceStatusQuery.mockReturnValue({isLoading: false, error: false, data: {statuses: {}}});
    render(<FilterStatusChips />);
    expect(screen.getByTestId('section')).toBeInTheDocument();
  });

  it('renders chips and handles selection and deletion', () => {
    mockUseGetInvoiceStatusQuery.mockReturnValue({
      isLoading: false,
      error: false,
      data: {statuses: {PAID: 'Paid', UNPAID: 'Unpaid'}},
    });
    const onSelect = vi.fn();
    render(<FilterStatusChips onSelect={onSelect} />);
    // Select PAID
    const paidChip = screen.getByTestId('chip-Paid');
    fireEvent.click(paidChip);
    expect(onSelect).toHaveBeenCalled();
    // Delete PAID
    fireEvent.doubleClick(paidChip);
    expect(onSelect).toHaveBeenCalled();
  });

  it('clears selection via imperative handle', () => {
    mockUseGetInvoiceStatusQuery.mockReturnValue({
      isLoading: false,
      error: false,
      data: {statuses: {PAID: 'Paid'}},
    });
    const ref = React.createRef<any>();
    render(<FilterStatusChips ref={ref} value={new Set(['Paid'])} />);
    // Clear selection via ref
    ref.current?.clearSelection();
    // No assertion, just coverage for imperative handle
  });
});
