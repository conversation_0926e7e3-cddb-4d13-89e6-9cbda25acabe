import {Box, CircularProgress, Typography} from '@mui/material';
import {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {useGetInvoiceStatusQuery} from 'redux/app/invoiceManagementApiSlice';
import FilterChip from '../../Components/FilterChip/FilterChip';
import FilterSectionView from '../../Components/FilterChip/FilterSectionView';
import {IClearFilters} from '../../Components/FilterUtil/IClearFilters';
import {getStatusLabel, InvoiceStatus} from './billings.utils';

export interface FilterStatusChipsProps {
  value?: Set<string>;
  onSelect?: (selected: Set<string>) => void;
}

export type FilterStatusChipsRef = IClearFilters;

/**
 * A React forwardRef component that renders a set of filter chips for invoice statuses.
 *
 * Fetches available invoice statuses using `useGetInvoiceStatusQuery` and displays them as selectable chips.
 * Allows multiple statuses to be selected, and notifies the parent via the `onSelect` callback whenever the selection changes.
 *
 * The component exposes an imperative handle with a `clearSelection` method to clear all selected statuses.
 *
 * @param props - The component props.
 * @param props.onSelect - Callback invoked with the current set of selected statuses whenever the selection changes.
 * @param props.value - Optional initial set of selected statuses.
 * @param ref - Ref object to access imperative methods (`clearSelection`).
 *
 * @returns A filter section containing status chips, a loading indicator, or an error message.
 */
const FilterStatusChips = forwardRef<FilterStatusChipsRef, FilterStatusChipsProps>(({onSelect, value}, ref) => {
  const {data, isLoading, error} = useGetInvoiceStatusQuery({});
  const [selectedStatus, setSelectedStatus] = useState<Set<string>>(value ? new Set(value) : new Set());

  useEffect(() => {
    if (onSelect) {
      onSelect(new Set(selectedStatus));
    }
  }, [selectedStatus, onSelect]);

  useImperativeHandle(ref, () => ({
    clearSelection: () => {
      setSelectedStatus(new Set());
    },
  }));

  if (isLoading) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height={40}>
        <CircularProgress size={24} />
      </Box>
    );
  }
  if (error) {
    return <Typography>Something Went Wrong</Typography>;
  }
  return (
    <FilterSectionView title="Status">
      <Box sx={{display: 'flex', flexDirection: 'row', gap: 0.75, flexWrap: 'wrap', textTransform: 'capitalize'}}>
        {Object.entries(data?.statuses ?? {}).map(([key, value]) => {
          const statusValue = value as InvoiceStatus;
          return (
            <FilterChip
              key={key}
              onClick={() => {
                if (!selectedStatus.has(statusValue)) {
                  selectedStatus.add(statusValue);
                }
                setSelectedStatus(new Set(selectedStatus));
              }}
              label={getStatusLabel(statusValue)}
              selected={selectedStatus.has(statusValue)}
              onDelete={() => {
                selectedStatus.delete(statusValue);
                setSelectedStatus(new Set(selectedStatus));
              }}
            />
          );
        })}
      </Box>
    </FilterSectionView>
  );
});
FilterStatusChips.displayName = 'FilterStatusChips';

export default FilterStatusChips;
