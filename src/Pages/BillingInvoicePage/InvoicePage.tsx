import {Box, CircularProgress, Stack, Tooltip, Typography} from '@mui/material';
import EyeIcon from 'Assets/EyeIcon';
import {DebouncedInput, Table} from 'Components/Table';
import {useSnackbar} from 'notistack';
import React, {useEffect, useState} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';

import {CellContext} from '@tanstack/react-table';
import SearchIcon from 'Assets/search-icon.svg';
import FilterIcon from 'Assets/tenant-filter-icon.svg';
import SVGImageFromPath from 'Components/SVGImageFromPath';

import DownloadIcon from 'Assets/DownloadIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import Filter, {IFilter} from 'Components/Filter/Filter';
import {useTableState} from 'Components/Table/hook/TableStateHook';
import {Integers} from 'Helpers/integers';
import {
  useDownloadInvoiceByIdQuery,
  useGetInvoicesCountQuery,
  useGetInvoicesQuery,
} from 'redux/app/invoiceManagementApiSlice';
import {TenantBillingsApiDTO, TenantBillingsApiForCountDTO} from 'redux/app/types/invoice.type';
import {
  actionStyles,
  bodyCellProps,
  coloumnCellProps,
  headerBoxStyle,
  leftHeaderStyle,
  tableContainerProps,
  tableHeadProps,
  toolTipStyles,
} from 'styles/pages/TenantPage.styles';
import {renderFilterButton} from '../utils';
import {canNotLoadInvoicePage, getBackendColumnName, getInvoiceTableColumns} from './billings.utils';
import FilterStatusChips from './BillingStatusFilterChip';

interface IActionButtonsProps {
  row: CellContext<unknown, unknown>;
  tenantName: string;
  tenantId: string;
}

/**
 * Renders action buttons for each invoice row, including "View details" and "Download" options.
 *
 * - "View details" navigates to the invoice details page with relevant tenant and billing information.
 * - "Download" triggers an invoice PDF download, showing a loading spinner while the download is in progress.
 * - Displays error notifications if the download fails.
 *
 * @param row - The row data containing invoice and billing information.
 * @param tenantName - The name of the tenant associated with the invoice.
 * @param tenantId - The unique identifier of the tenant.
 *
 * @returns A React component with action buttons for invoice operations.
 */
export const ActionButtons: React.FC<IActionButtonsProps> = ({row, tenantName, tenantId}) => {
  const navigate = useNavigate();
  const [downloadParams, setDownloadParams] = useState<{invoiceId: string; stripeInvoiceId: string} | null>(null);
  const {enqueueSnackbar} = useSnackbar();
  const [isDownloading, setIsDownloading] = useState(false);

  const tempParams = {invoiceId: '', stripeInvoiceId: ''};
  const {data, error, isLoading} = useDownloadInvoiceByIdQuery(downloadParams ?? tempParams, {skip: !downloadParams});

  useEffect(() => {
    if (data?.pdfUrl) {
      const link = document.createElement('a');
      link.href = data.pdfUrl;
      link.download = 'invoice.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      setTimeout(() => {
        setIsDownloading(false);
        setDownloadParams(null);
      }, Integers.FourThousand);
    }
    if (error) {
      setIsDownloading(false);
      enqueueSnackbar('Failed to download invoice', {variant: 'error'});
      setDownloadParams(null);
    }
  }, [data, error]);

  const handleDownloadInvoice = (invoiceId: string, stripeInvoiceId: string) => {
    setDownloadParams({invoiceId, stripeInvoiceId});
    setIsDownloading(true);
  };

  const handleRedirectToDetails = (
    invoiceId: string,
    tenantName: string,
    tenantId: string,
    billingCustomerId: string,
  ) => {
    navigate(`/invoices/${invoiceId}`, {
      state: {
        tenantName: tenantName,
        invoiceId: invoiceId,
        tenantId: tenantId,
        billingCustomerId: billingCustomerId,
      },
    });
  };

  return (
    <Stack display="flex" flexDirection="row" gap={1}>
      <Tooltip title="View details" placement="top" arrow slotProps={toolTipStyles}>
        <EyeIcon
          sx={actionStyles}
          onClick={() =>
            handleRedirectToDetails(
              (row.row.original as {id: string}).id,
              tenantName,
              tenantId,
              (row.row.original as {billingCustomerId: string}).billingCustomerId,
            )
          }
        />
      </Tooltip>

      <Tooltip title="Download" placement="top" arrow slotProps={toolTipStyles}>
        {isDownloading || isLoading ? (
          <CircularProgress size={20} />
        ) : (
          <DownloadIcon
            sx={actionStyles}
            data-testid="download-icon"
            onClick={() =>
              handleDownloadInvoice(
                (row.row.original as {id: string}).id,
                (row.row.original as {invoiceId: string}).invoiceId,
              )
            }
          />
        )}
      </Tooltip>
    </Stack>
  );
};

const buttonHeight = '2.375rem';

const isKeysMissing = (tenantName: string, id: string, billingCustomerId: string) =>
  !tenantName || !id || !billingCustomerId;

/**
 * Constructs the filter parameters object for fetching tenant billing invoices.
 *
 * @param limit - The maximum number of records to retrieve.
 * @param offset - The number of records to skip for pagination.
 * @param sortBy - The field by which to sort the results.
 * @param searchTerm - The search string to filter invoice results.
 * @param selectedInvoiceFilter - The currently selected invoice filter, containing status and date range.
 * @param billingCustomerId - The unique identifier of the billing customer.
 * @returns An object conforming to `TenantBillingsApiDTO` with the specified filter parameters.
 */
function getFilterParams(
  limit: number,
  offset: number,
  sortBy: string,
  searchTerm: string,
  selectedInvoiceFilter: IFilter | undefined,
  billingCustomerId: string,
): TenantBillingsApiDTO {
  return {
    limit,
    offset,
    order: sortBy,
    searchValue: searchTerm,
    status: selectedInvoiceFilter?.status ? Array.from(selectedInvoiceFilter.status) : undefined,
    dateRange: selectedInvoiceFilter?.dateRange,
    billingCustomerId,
  };
}

/**
 * Constructs the filter parameters object for counting tenant billings based on the provided criteria.
 *
 * @param sortBy - The field by which to sort the results.
 * @param searchTerm - The search term to filter the results.
 * @param selectedInvoiceFilter - The selected invoice filter object, which may include status and date range.
 * @param billingCustomerId - The ID of the billing customer to filter the results.
 * @returns An object conforming to `TenantBillingsApiForCountDTO` containing the filter parameters.
 */
function getCountFilterParams(
  sortBy: string,
  searchTerm: string,
  selectedInvoiceFilter: IFilter | undefined,
  billingCustomerId: string,
): TenantBillingsApiForCountDTO {
  return {
    order: sortBy,
    searchValue: searchTerm,
    status: selectedInvoiceFilter?.status ? Array.from(selectedInvoiceFilter.status) : undefined,
    dateRange: selectedInvoiceFilter?.dateRange,
    billingCustomerId,
  };
}

/**
 * Calculates the total number of active filters applied to invoices.
 *
 * @param selectedInvoiceFilter - The currently selected invoice filter object, or undefined.
 * @returns The total count of active filters, including the number of selected statuses and whether a date range is set.
 */
function getFilterSize(selectedInvoiceFilter: IFilter | undefined): number {
  return (selectedInvoiceFilter?.status?.size ?? 0) + (selectedInvoiceFilter?.dateRange ? 1 : 0);
}

/**
 * Determines whether any filters are applied based on the filter size.
 *
 * @param filterSize - The number of active filters.
 * @returns `true` if there is at least one filter applied; otherwise, `false`.
 */
function getHasFilters(filterSize: number): boolean {
  return filterSize > 0;
}

/**
 * InvoicePage component displays a list of invoices for a specific tenant.
 *
 * Features:
 * - Fetches and displays invoice data and count using provided query hooks.
 * - Supports searching, sorting, filtering, and pagination of invoices.
 * - Handles loading and error states with appropriate UI feedback.
 * - Displays breadcrumbs for navigation context.
 * - Provides a filter dialog and search input for invoice filtering.
 *
 * State Management:
 * - Uses local state for search term, sorting, and selected filters.
 * - Uses custom hooks for table pagination and snackbar notifications.
 *
 * Side Effects:
 * - Shows error notifications if invoice data or count fetching fails.
 * - Initializes sorting on component mount.
 *
 * UI Structure:
 * - Header with tenant name and breadcrumbs.
 * - Top-right section with search and filter controls.
 * - Invoice table with manual pagination and sorting.
 * - Loading overlay during data fetch.
 * - Filter dialog for advanced filtering.
 *
 * @component
 * @returns {JSX.Element} The rendered InvoicePage component.
 */
const InvoicePage: React.FC = () => {
  const location = useLocation();
  const {tenantName, id, billingCustomerId} = location.state || {};

  const {enqueueSnackbar} = useSnackbar();
  const {limit, setLimit, offset, setOffset, handlePageChange, handleRowsPerPageChange} = useTableState();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('createdOn DESC');
  const initialSortState = [{id: 'createdDate', desc: true}];
  const [selectedInvoiceFilter, setSelectedInvoiceFilter] = React.useState<IFilter | undefined>(undefined);
  const filterButtonRef = React.useRef<HTMLButtonElement>(null);
  const [openFilter, setOpenFilter] = React.useState(false);

  const handleSortChange = (columnId: string, sort: boolean) => {
    // Map frontend column name to backend column name
    const backendColumnName = getBackendColumnName(columnId);
    const sortParam = `${backendColumnName} ${sort ? 'DESC' : 'ASC'}`;
    setOffset(0);
    setSortBy(sortParam);
  };

  React.useEffect(() => {
    setOffset(0);
  }, [searchTerm]);

  const filterParams = getFilterParams(limit, offset, sortBy, searchTerm, selectedInvoiceFilter, billingCustomerId);
  const countFilterParams = getCountFilterParams(sortBy, searchTerm, selectedInvoiceFilter, billingCustomerId);

  const skipApi = isKeysMissing(tenantName, id, billingCustomerId);

  const {
    data: invoices,
    error: billingsError,
    isFetching: isLoading,
  } = useGetInvoicesQuery(filterParams, {
    refetchOnMountOrArgChange: true,
    skip: skipApi,
  }) || {};
  const {
    data: billingsCount,
    error: countError,
    isFetching: countLoading,
  } = useGetInvoicesCountQuery(countFilterParams, {
    refetchOnMountOrArgChange: true,
    skip: skipApi,
  }) || {};

  // Show error notifications
  React.useEffect(() => {
    if (billingsError) {
      enqueueSnackbar('Failed to fetch invoice data', {variant: 'error'});
    }
    if (countError) {
      enqueueSnackbar('Failed to fetch invoice count', {variant: 'error'});
    }
  }, [billingsError, countError, enqueueSnackbar]);
  React.useEffect(() => {
    handleSortChange('createdDate', true);
  }, []);

  // Extracted filter size and hasFilters to variables for readability and complexity reduction
  const filterSize = getFilterSize(selectedInvoiceFilter);
  const hasFilters = getHasFilters(filterSize);

  const buildTopRightSection = () => {
    return (
      <Box sx={{display: 'flex', gap: 1, flexDirection: 'row', alignItems: 'center', ml: 'auto'}}>
        <DebouncedInput
          placeholder={`Search invoices of ${tenantName}`}
          data-testid="search-invoices"
          sx={{
            fontSize: '0.675rem',
            pl: 0,
            width: '19rem',
          }}
          debounceTime={500}
          leftAdornment={<SVGImageFromPath path={SearchIcon} sx={{width: '1rem', height: '1rem', mr: 1}} />}
          inputSx={{
            fontSize: '1rem',
            fontWeight: 400,
            color: 'black.main',
          }}
          value={searchTerm}
          onChange={value => {
            setSearchTerm('' + value);
          }}
        />
        {renderFilterButton({
          filterButtonRef,
          buttonHeight,
          filterSize: filterSize,
          hasFilters: hasFilters,
          setOpenFilter,
          FilterIcon,
          BorderButton,
          SVGImageFromPath,
        })}
      </Box>
    );
  };

  const breadcrumbItems = [
    {label: 'Billings & Invoices', url: '/billing-invoices'},
    {label: 'All Invoices', url: `/billing-invoices/${id}`},
  ];

  if (skipApi) {
    return canNotLoadInvoicePage();
  }

  return (
    <Box>
      {/* Header */}
      <Box sx={headerBoxStyle}>
        <Box sx={{display: 'flex', flexDirection: 'column'}}>
          <Typography variant="h6" sx={leftHeaderStyle}>
            {tenantName}
          </Typography>
          <Stack>
            <Breadcrumb items={breadcrumbItems} separator="|" />
          </Stack>
        </Box>
        {buildTopRightSection()}
      </Box>
      <Box sx={{position: 'relative', minHeight: '200px'}}>
        <Table
          data={invoices || []}
          columns={getInvoiceTableColumns(tenantName, id)}
          enableSorting={true}
          initialSortingState={initialSortState}
          tablePropsObject={{
            tableHeadProps: {sx: tableHeadProps},
            columnCellProps: {sx: coloumnCellProps},
            tableContainerProps: {sx: tableContainerProps},
            bodyCellProps: {sx: bodyCellProps},
          }}
          limit={limit}
          setLimit={setLimit}
          offset={offset}
          setOffset={setOffset}
          count={billingsCount?.count || 0}
          manualPagination={true}
          onSortChange={handleSortChange}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          data-testid="invoice-table"
        />
        {(isLoading || countLoading) && (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              bgcolor: 'white.main',
              zIndex: 1,
            }}
          >
            <CircularProgress />
          </Box>
        )}
      </Box>
      <Filter
        open={openFilter}
        value={selectedInvoiceFilter}
        onClose={() => setOpenFilter(false)}
        anchorEl={filterButtonRef.current}
        onFilterChange={filter => {
          setOffset(0);
          setSelectedInvoiceFilter(filter);
        }}
        FilterStatusChips={FilterStatusChips}
      />
    </Box>
  );
};

export default InvoicePage;
