import {Box, Typography} from '@mui/material';
import Stack from '@mui/material/Stack';
import {Integers} from 'Helpers/integers';
import {AnyObject, defaultFormatNumber} from 'Helpers/utils';
import React from 'react';
import {Cell, Legend, Pie, PieChart, Tooltip, TooltipProps} from 'recharts';
import {Props} from 'recharts/types/component/DefaultLegendContent';
import {NameType, ValueType} from 'recharts/types/component/DefaultTooltipContent';
import {legendColor, pieChartStyles} from './styles';

export interface IPieData {
  name: string;
  value: number;
  color: string;
  tag?: AnyObject;
}
interface ICustomPieChart {
  data: IPieData[];
  width?: number;
  height?: number;
  onClick?: (data: AnyObject) => void;
}
interface CustomLegendProps {
  payload?: Props;
}

const CustomLegend: React.FC<CustomLegendProps> = ({payload: passedPayload}) => {
  const payload = passedPayload?.payload;
  return (
    <Stack sx={{...pieChartStyles.legendsContainer, gap: 0}}>
      {payload?.map(entry => (
        <Box
          key={`legend-item-${entry.value}`}
          sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', gap: 0, flex: 1}}
        >
          <Box sx={{...pieChartStyles.legendValueContainer, flexDirection: 'row', gap: 0}}>
            <Box component={'span'} sx={legendColor(entry?.color ?? '')} />
            <Box component={'span'} sx={pieChartStyles.legendText}>
              {entry.value}
            </Box>
          </Box>
          <Typography sx={{fontSize: '1.125rem', fontWeight: 700, color: 'body.dark'}}>
            {defaultFormatNumber((entry.payload as AnyObject)?.value ?? 0)}
          </Typography>
        </Box>
      ))}
    </Stack>
  );
};

const CustomLegendContent: React.FC<Props> = props => <CustomLegend payload={props} />;

const CustomTooltip = (props: TooltipProps<ValueType, NameType>) => {
  const {active, payload} = props;
  if (active && payload?.length) {
    const {name, value, color} = payload[0].payload;
    const boxSize = '0.5rem';
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'row',
          backgroundColor: 'white.main',
          alignItems: 'center',
          justifyItems: 'center',
          p: 1,
          borderRadius: '0.5rem',
          boxShadow: theme => theme.shadows[1],
          fontSize: '0.75rem',
          fontWeight: 700,
          color: 'body.dark',
          gap: 0.5,
        }}
      >
        <Box component={'span'} sx={{height: boxSize, width: boxSize, borderRadius: '20%', backgroundColor: color}} />
        <span>
          <span>{name}:</span>
          <span>
            {' ' + defaultFormatNumber(value)} Tenant{value > 1 ? 's' : ''}
          </span>
        </span>
      </Box>
    );
  }
  return <></>;
};

const CustomPieChart: React.FC<ICustomPieChart> = ({data, width = 300, height = 200, onClick}) => {
  if (data.length === 0) {
    return (
      <Typography height={height} sx={pieChartStyles.noDataContainer}>
        No Data Available
      </Typography>
    );
  }

  return (
    <PieChart width={width} height={height}>
      <Pie
        cornerRadius={Integers.Five}
        paddingAngle={Integers.Two}
        data={data}
        innerRadius={Integers.Sixty}
        outerRadius={Integers.OneHundred}
        dataKey="value"
        cx="50%"
        cy="50%"
        onClick={onClick}
        animationDuration={Integers.TwelveHundred}
      >
        {data.map(entry => (
          <Cell key={entry.name} fill={entry.color} />
        ))}
      </Pie>
      <Tooltip content={<CustomTooltip />} />
      <Legend iconType="circle" iconSize={10} layout="horizontal" align="center" content={<CustomLegendContent />} />
    </PieChart>
  );
};

export default CustomPieChart;
