import {SxProps} from '@mui/material';

interface Props {
  [key: string]: SxProps;
}

export const pieChartStyles: Props = {
  noDataContainer: {display: 'flex', justifyContent: 'center', alignItems: 'center', width: '100%'},
  legendsContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-around',
    flexWrap: 'wrap',
  },
  legendValueContainer: {
    display: 'flex',
    alignItems: 'center',
    gap: 1,
    padding: 0.75,
  },
  legendText: {
    paddingLeft: '0.25rem',
    color: 'body.700',
    fontWeight: 400,
    fontSize: '0.75rem',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
  },
};

export const legendColor = (entryColor: string): SxProps => ({
  backgroundColor: entryColor,
  borderRadius: '50%',
  width: '0.625rem',
  height: '0.625rem',
});
