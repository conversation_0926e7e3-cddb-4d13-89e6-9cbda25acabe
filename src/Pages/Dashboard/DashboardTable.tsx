import ArrowForwardIosIcon from '@mui/icons-material/ArrowForwardIos';
import {Box, Typography} from '@mui/material';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import PermissionWrapper from 'Components/PermissionWrapper';
import {Table} from 'Components/Table';
import PermissionKey from 'Constants/enums/permissions';
import {tenantTableColumns} from 'Pages/Tenants/tenants.utils';
import {useNavigate} from 'react-router';
import {TenantType} from 'redux/app/types';
import {RouteNames} from 'Routes/routeNames';
import {bodyCellProps, coloumnCellProps, tableContainerProps, tableHeadProps} from 'styles/pages/TenantPage.styles';
import {headerTypographyProps} from './styles';

const dashboardTableColumns = (() => tenantTableColumns.filter(column => column.header.toLowerCase() !== 'actions'))();

interface DashboardTableProps {
  tenants?: TenantType[];
}

const DashboardTable = ({tenants}: DashboardTableProps) => {
  const navigate = useNavigate();
  const hasPermissionForViewTenant = usePermissions().hasPermission(PermissionKey.ViewTenant);

  const buildTitleBar = () => {
    return (
      <Box display={'flex'} justifyContent={'space-between'} alignItems={'center'} width={'100%'}>
        <Typography sx={headerTypographyProps}>Recent Tenants</Typography>
        <PermissionWrapper permission={PermissionKey.ViewTenant}>
          <Box sx={{cursor: 'pointer', display: 'flex', alignItems: 'center', gap: 0.5, flexDirection: 'row'}}>
            <Typography
              sx={{fontSize: '0.875rem', fontWeight: 500, color: 'body.dark', textDecoration: 'underline'}}
              onClick={() => {
                navigate(RouteNames.TENANTS);
              }}
            >
              View all
            </Typography>
            <ArrowForwardIosIcon sx={{height: 12, width: 12}} />
          </Box>
        </PermissionWrapper>
      </Box>
    );
  };

  return (
    <Box display={'flex'} flexDirection={'column'} width={'100%'} alignItems={'start'}>
      {buildTitleBar()}
      <Table
        data={tenants || []}
        columns={dashboardTableColumns}
        enableSorting={false}
        tablePropsObject={{
          tableHeadProps: {sx: tableHeadProps},
          columnCellProps: {sx: coloumnCellProps},
          tableContainerProps: {sx: tableContainerProps},
          bodyRowProps: {
            sx: {cursor: hasPermissionForViewTenant ? 'pointer' : 'default'},
            onClickRow: idx => {
              const tenantId = tenants?.[idx]?.id;
              if (!tenantId) return;
              navigate(RouteNames.TENANT_DETAILS.replace(':tenantId', tenantId));
            },
          },
          bodyCellProps: {
            sx: bodyCellProps,
          },
        }}
        count={tenants?.length || 0}
        manualPagination={false}
        data-testid="tenant-table"
      />
    </Box>
  );
};

DashboardTable.displayName = 'DashboardTable';
export default DashboardTable;
