import {ThemeProvider, createTheme} from '@mui/material/styles';
import {render, screen} from '@testing-library/react';
import React from 'react';
import StatCard from './StatCard';

const renderWithTheme = (ui: React.ReactElement) => {
  const theme = createTheme({
    palette: {
      white: {main: '#fff'},
      body: {
        dark: '#222',
        100: '#eee',
      },
    },
  } as any);
  return render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);
};

describe('StatCard', () => {
  it('renders title and value', () => {
    renderWithTheme(<StatCard title="Users" value={100} />);
    expect(screen.getByText('Users')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
  });

  it('renders icon if provided', () => {
    const Icon = () => <span data-testid="icon">icon</span>;
    renderWithTheme(<StatCard title="Revenue" value={200} icon={<Icon />} />);
    expect(screen.getByTestId('icon')).toBeInTheDocument();
  });

  it('formats value using defaultFormatNumber', () => {
    renderWithTheme(<StatCard title="Amount" value={1234567} />);
    // defaultFormatNumber uses toLocaleString, so expect comma
    expect(screen.getByText(/1,234,567/)).toBeInTheDocument();
  });

  it('applies custom sx and boxProps', () => {
    renderWithTheme(
      <StatCard title="Custom" value={1} sx={{background: 'red'}} data-testid="stat-card" id="custom-id" />,
    );
    const card = screen.getByTestId('stat-card');
    expect(card).toHaveAttribute('id', 'custom-id');
    // sx is merged, so background should be red (inline style)
    expect(card).toHaveStyle({background: 'red'});
  });

  it('has correct displayName', () => {
    expect(StatCard.displayName).toBe('StatCard');
  });

  it('renders with border and padding styles', () => {
    renderWithTheme(<StatCard title="Styled" value={10} data-testid="stat-card" />);
    const card = screen.getByTestId('stat-card');
    expect(card).toHaveStyle({borderRadius: '0.625rem'});
    expect(card).toHaveStyle({padding: '0.75rem'});
  });

  it('renders title with correct typography styles', () => {
    renderWithTheme(<StatCard title="Typography" value={1} />);
    const title = screen.getByText('Typography');
    expect(title).toHaveStyle({fontWeight: '500'});
    expect(title).toHaveStyle({fontSize: '1.0625rem'});
  });

  it('renders value with correct typography styles', () => {
    renderWithTheme(<StatCard title="Typography" value={42} />);
    const value = screen.getByText('42');
    expect(value).toHaveStyle({fontWeight: '700'});
    expect(value).toHaveStyle({fontSize: '1.625rem'});
    expect(value).toHaveStyle({textAlign: 'start'});
  });
});
