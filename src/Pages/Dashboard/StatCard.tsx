import {Box, BoxProps, Typography} from '@mui/material';
import {Integers} from 'Helpers/integers';
import {defaultFormatNumber} from 'Helpers/utils';
import React from 'react';

interface StatCardProps extends BoxProps {
  title: string;
  value: number | string;
  icon?: React.ReactNode;
}

const StatCard: React.FC<StatCardProps> = ({title, value, icon, sx, ...boxProps}) => {
  return (
    <Box
      sx={{
        background: 'white.main',
        borderRadius: '0.625rem',
        border: theme => `1px solid ${theme.palette.body?.[Integers.OneHundred]}`,
        p: '0.75rem',
        ...sx,
      }}
      {...boxProps}
    >
      <Box display="flex" flexDirection={'row'} alignItems="center" justifyItems="center" mb={1}>
        {icon}
        <Typography
          sx={{
            ml: 1,
            fontSize: '1.0625rem',
            color: 'body.dark',
            fontWeight: 500,
          }}
        >
          {title}
        </Typography>
      </Box>
      <Typography
        sx={{
          fontWeight: 700,
          fontSize: '1.625rem',
          textAlign: 'start',
          color: 'body.dark',
        }}
      >
        {defaultFormatNumber(value)}
      </Typography>
    </Box>
  );
};

StatCard.displayName = 'StatCard';

export default StatCard;
