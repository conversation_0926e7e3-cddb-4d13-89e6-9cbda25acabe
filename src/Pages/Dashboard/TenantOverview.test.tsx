import {render, screen} from '@testing-library/react';
import React from 'react';
import {TenantStatus} from 'redux/app/types';
import {TenantOverviewStatusDto} from 'redux/app/types/tenantOverview.dto';
import TenantOverview, {defaultValues} from './TenantOverview';

vi.mock('react-router', () => ({
  useNavigate: () => vi.fn(),
}));

vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: () => ({
    hasPermission: () => true,
  }),
  PermissionProvider: ({children}: {children: React.ReactNode}) => <div>{children}</div>,
}));

const theme = {
  palette: {
    body: {
      100: '#e0e0e0',
      dark: '#222222',
      main: '#ffffff',
    },
  },
};

import {ThemeProvider, createTheme} from '@mui/material/styles';

function renderWithTheme(children: React.ReactNode) {
  return render(<ThemeProvider theme={createTheme(theme)}>{children}</ThemeProvider>);
}

describe('TenantOverview - defaultValues', () => {
  it('should have defaultValues as undefined for total, active, inactive', () => {
    expect(defaultValues).toEqual({
      total: undefined,
      active: undefined,
      inactive: undefined,
    });
  });

  it('renders with no statusMetrics and no error (defaultValues)', () => {
    renderWithTheme(<TenantOverview />);
    expect(screen.getByTestId('TenantOverview')).toBeInTheDocument();
    expect(screen.getByText('Tenant Overview')).toBeInTheDocument();
    // Should not render any StatCard since all values are undefined
    expect(screen.queryByText('Total Tenants')).not.toBeInTheDocument();
    expect(screen.queryByText('Active Tenants')).not.toBeInTheDocument();
    expect(screen.queryByText('Inactive Tenants')).not.toBeInTheDocument();
  });

  it('renders error message when error is present', () => {
    renderWithTheme(<TenantOverview error={{message: 'error'}} />);
    expect(screen.getByText('Something Went Wrong')).toBeInTheDocument();
  });

  it('renders StatCards with correct values from statusMetrics', () => {
    const statusMetrics: TenantOverviewStatusDto = {
      [TenantStatus.ACTIVE]: {count: 5, status: 'ACTIVE'},
      [TenantStatus.INACTIVE]: {count: 2, status: 'INACTIVE'},
    };
    renderWithTheme(<TenantOverview statusMetrics={statusMetrics} />);
    expect(screen.getByText('Total Tenants')).toBeInTheDocument();
    expect(screen.getByText('Active Tenants')).toBeInTheDocument();
    expect(screen.getByText('Inactive Tenants')).toBeInTheDocument();
    expect(screen.getByText('7')).toBeInTheDocument(); // total
    expect(screen.getByText('5')).toBeInTheDocument(); // active
    expect(screen.getByText('2')).toBeInTheDocument(); // inactive
  });
});
