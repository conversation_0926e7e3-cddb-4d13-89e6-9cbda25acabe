import {Box, Typography} from '@mui/material';
import {SerializedError} from '@reduxjs/toolkit';
import {FetchBaseQueryError} from '@reduxjs/toolkit/query';
import ActiveIcon from 'Assets/dashboard-active-ic.svg';
import InactiveIcon from 'Assets/dashboard-inactive-ic.svg';
import TotalIcon from 'Assets/dashboard-total-ic.svg';
import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import PermissionKey from 'Constants/enums/permissions';
import {isNil} from 'lodash';
import {useCallback, useEffect, useState} from 'react';
import {useNavigate} from 'react-router';
import {TenantStatus} from 'redux/app/types';
import {TenantOverviewStatusDto} from 'redux/app/types/tenantOverview.dto';
import {RouteNames} from 'Routes/routeNames';
import StatCard from './StatCard';
import {headerTypographyProps} from './styles';

interface StatInfo {
  title: string;
  value?: number | string;
  icon?: string;
  status?: number;
}

export const defaultValues = {
  total: undefined,
  active: undefined,
  inactive: undefined,
};

const TenantOverview = ({
  statusMetrics,
  error,
}: {
  statusMetrics?: TenantOverviewStatusDto;
  error?: FetchBaseQueryError | SerializedError;
}) => {
  const [countValues, setCountValues] = useState<{total?: number; active?: number; inactive?: number}>(defaultValues);
  const navigate = useNavigate();
  const {hasPermission} = usePermissions();
  const hasViewTenantPermission = hasPermission(PermissionKey.ViewTenant);

  const stats: StatInfo[] = [
    {title: 'Total Tenants', value: countValues.total, icon: TotalIcon},
    {title: 'Active Tenants', value: countValues.active, icon: ActiveIcon, status: TenantStatus.ACTIVE},
    {title: 'Inactive Tenants', value: countValues.inactive, icon: InactiveIcon, status: TenantStatus.INACTIVE},
  ];

  useEffect(() => {
    if (statusMetrics) {
      const obj = {
        total: 0,
        active: 0,
        inactive: 0,
      };
      Object.keys(statusMetrics).forEach(key => {
        obj.total += statusMetrics[Number(key)].count;
        if (TenantStatus.ACTIVE === Number(key)) {
          obj.active = statusMetrics[Number(key)].count;
        }
        if (TenantStatus.INACTIVE === Number(key)) {
          obj.inactive = statusMetrics[Number(key)].count;
        }
      });
      setCountValues(obj);
    } else {
      setCountValues(defaultValues);
    }
  }, [statusMetrics]);

  const handleClick = useCallback(
    (stat: StatInfo) => {
      if (!hasViewTenantPermission) return;
      navigate(RouteNames.TENANTS, {
        state: {...(!isNil(stat?.status) && {statusFilter: new Set([String(stat.status)])})},
      });
    },
    [navigate, hasViewTenantPermission],
  );

  const buildContent = () => {
    if (error) {
      return <Typography>Something Went Wrong</Typography>;
    }
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: {xs: 'column', sm: 'row'},
          justifyContent: 'space-evenly',
          width: '100%',
          gap: 1,
        }}
      >
        {stats.map(
          stat =>
            !isNil(stat.value) && (
              <StatCard
                key={stat.title}
                title={stat.title}
                value={stat.value}
                icon={<SVGImageFromPath path={stat.icon ?? ''} />}
                sx={{width: 1, cursor: hasViewTenantPermission ? 'pointer' : 'default'}}
                onClick={() => handleClick(stat)}
              />
            ),
        )}
      </Box>
    );
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'flex-start',

        width: '100%',
        textAlign: 'center',
      }}
      data-testid="TenantOverview"
    >
      <Typography sx={headerTypographyProps}>Tenant Overview</Typography>
      {buildContent()}
    </Box>
  );
};

TenantOverview.displayName = 'TenantOverview';

export default TenantOverview;
