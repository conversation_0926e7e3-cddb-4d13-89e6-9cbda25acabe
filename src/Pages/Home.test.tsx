/* @vitest-environment jsdom */
import {screen, waitFor} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import Home from './Home';

// Mock dependencies
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetTenantStatusMetricsQuery: () => ({
    data: {
      status: {
        '1': {count: 5, status: 'Active'},
        '2': {count: 2, status: 'Inactive'},
      },
      tenants: [
        {id: 1, name: 'Tenant 1'},
        {id: 2, name: 'Tenant 2'},
      ],
    },
    error: undefined,
  }),
}));
vi.mock('redux/auth/authApiSlice', () => ({
  useGetUserQuery: () => ({
    data: {firstName: 'john'},
    isLoading: false,
  }),
}));
vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: () => ({
    hasPermission: () => true,
  }),
}));
vi.mock('react-router', () => ({
  useNavigate: () => vi.fn(),
}));
vi.mock('./Dashboard/DashboardTable', () => ({
  __esModule: true,
  default: () => <div data-testid="DashboardTable" />,
}));
vi.mock('./Dashboard/TenantOverview', () => ({
  __esModule: true,
  default: () => <div data-testid="TenantOverview" />,
}));
vi.mock('Pages/Dashboard/CustomPieChart/CustomPieChart', () => ({
  __esModule: true,
  default: () => <div data-testid="CustomPieChart" />,
}));

describe('Home', () => {
  it('should render the HomePage container', () => {
    renderWithTheme(<Home />);
    expect(screen.getByTestId('HomePage')).toBeInTheDocument();
  });

  it('should render the greeting with user name', async () => {
    renderWithTheme(<Home />);
    await waitFor(() => {
      expect(screen.getByText(/Hi John!👋/i)).toBeInTheDocument();
    });
  });

  it('should render TenantOverview and DashboardTable', () => {
    renderWithTheme(<Home />);
    expect(screen.getByTestId('TenantOverview')).toBeInTheDocument();
    expect(screen.getByTestId('DashboardTable')).toBeInTheDocument();
  });

  it('should render the CustomPieChart', () => {
    renderWithTheme(<Home />);
    expect(screen.getByTestId('CustomPieChart')).toBeInTheDocument();
  });
});
