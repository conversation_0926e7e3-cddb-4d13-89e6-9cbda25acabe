import ReportProblemOutlinedIcon from '@mui/icons-material/ReportProblemOutlined';
import {Box, InputAdornment} from '@mui/material';
import Form from 'Components/Forms/Form';
import {AnyObject} from 'Helpers/utils';
import {loginValidationSchema} from 'Helpers/validations/auth-validations';
import useAuth from 'Hooks/useAuth';
import {useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {ILoginForm} from 'redux/auth/authApiSlice';
import {RouteNames} from 'Routes/routeNames';
import {
  FieldLabel,
  ForgotPasswordContainer,
  ForgotPasswordLink,
  FormFieldContainer,
  HeaderContainer,
  IconDivider,
  LoginCard,
  LoginContainer,
  LogoContainer,
  StyledButton,
  StyledDistekLogo,
  StyledEmailIcon,
  StyledFormInput,
  StyledFormPasswordInput,
  StyledLockIcon,
  SubTitle,
  WelcomeTitle,
  errorIconStyles,
  errorStyles,
  inputErrorStyles,
  inputStyles,
} from '../styles/pages/Login.styles';

const initialValues = {
  username: '',
  password: '',
};

/**
 * Login component renders the login form for user authentication.
 *
 * This component provides a user interface for users to sign in to their account.
 * It handles form submission, validation, and displays error messages on failed login attempts.
 * On successful authentication, it navigates the user to the intended route.
 *
 * @component
 * @returns {JSX.Element} The rendered login page component.
 *
 * @example
 * <Login />
 *
 * @remarks
 * - Uses Formik for form state management and validation.
 * - Displays loading state and error messages.
 * - Redirects to the previous or default route after successful login.
 */
const Login = () => {
  // Define input styles as a constant

  const {login, loginLoading} = useAuth();
  const [loginError, setLoginError] = useState<string | null>(null);

  const navigate = useNavigate();
  const from = '/';

  const handleNavigation = () => {
    navigate(from, {replace: true});
  };

  const handleRedirect = () => {
    navigate(RouteNames.FORGOT_PASSWORD);
  };

  const handleSubmit = async (values: ILoginForm, actions: AnyObject) => {
    setLoginError(null);
    const {success, message} = await login(values);
    if (!success) {
      if (message === 'Invalid Credentials') {
        setLoginError('Invalid credentials. Please try again.');
      } else {
        setLoginError(message);
      }
    } else {
      handleNavigation();
    }
    if (actions.setSubmitting) {
      actions.setSubmitting(false);
    }
  };

  return (
    <LoginContainer data-testid="LoginPage">
      <LoginCard elevation={0}>
        <HeaderContainer>
          <LogoContainer>
            <StyledDistekLogo />
          </LogoContainer>

          <WelcomeTitle variant="h3">Sign in to your account</WelcomeTitle>

          <SubTitle variant="h6">Please provide your credentials to continue</SubTitle>
        </HeaderContainer>

        <Form
          initialValues={initialValues}
          onSubmit={handleSubmit}
          validationSchema={loginValidationSchema}
          validateOnBlur={true}
          validateOnChange={true}
        >
          <FormFieldContainer>
            <FieldLabel variant="body2">Email</FieldLabel>
            <StyledFormInput
              id="username"
              placeholder="Enter your email address"
              fullWidth
              sx={inputStyles}
              errorSx={inputErrorStyles}
              startAdornment={
                <InputAdornment position="start" sx={{margin: 0}}>
                  <StyledEmailIcon />
                  <IconDivider />
                </InputAdornment>
              }
            />
          </FormFieldContainer>

          <FormFieldContainer>
            <FieldLabel variant="body2">Password</FieldLabel>
            <StyledFormPasswordInput
              id="password"
              placeholder="Enter your password"
              fullWidth
              sx={inputStyles}
              errorSx={inputErrorStyles}
              startAdornment={
                <InputAdornment position="start" sx={{margin: 0}}>
                  <StyledLockIcon />
                  <IconDivider />
                </InputAdornment>
              }
            />
          </FormFieldContainer>

          <ForgotPasswordContainer>
            <ForgotPasswordLink variant="body2" onClick={handleRedirect}>
              Forgot password?
            </ForgotPasswordLink>
          </ForgotPasswordContainer>
          {
            <Box sx={loginError ? errorStyles(loginError) : errorStyles(null)}>
              <InputAdornment position="start">
                <ReportProblemOutlinedIcon sx={errorIconStyles} />
              </InputAdornment>
              {loginError}
            </Box>
          }

          <StyledButton type="submit" variant="contained" fullWidth isLoading={loginLoading}>
            {loginLoading ? 'Signing in...' : 'Sign In'}
          </StyledButton>
        </Form>
      </LoginCard>
    </LoginContainer>
  );
};

export default Login;
