// @vitest-environment jsdom
import {render, screen} from '@testing-library/react';
import {describe, expect, it} from 'vitest';
import ObservabilityPage from './ObservabilityPage';

describe('ObservabilityPage', () => {
  it('renders the Observability Page heading', () => {
    render(<ObservabilityPage />);
    expect(screen.getByRole('heading', {name: /Observability Page/i})).toBeInTheDocument();
  });
});
