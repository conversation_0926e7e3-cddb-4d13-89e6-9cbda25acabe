// Copyright (c) Distek. All rights reserved.

import {screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {MemoryRouter} from 'react-router-dom';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import ForgotPassword from './ForgotPassword';

// Mock RTK Query mutation
const mockForgotPassword = vi.fn();
vi.mock('redux/auth/authApiSlice', async original => ({
  ...(await original()),
  useForgotPasswordMutation: () => [mockForgotPassword, {isLoading: false}],
}));

describe('ForgotPassword Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockForgotPassword.mockReset();
    mockForgotPassword.mockReturnValue({
      unwrap: vi.fn().mockResolvedValue({}),
    });
  });

  const renderForgotPassword = () =>
    renderWithTheme(
      <MemoryRouter>
        <ForgotPassword />
      </MemoryRouter>,
    );

  it('renders the forgot password form', () => {
    renderForgotPassword();
    expect(screen.getByTestId('ForgotPasswordPage')).toBeInTheDocument();
    expect(screen.getByText(/Forgot your Password/i)).toBeInTheDocument();
    expect(screen.getByText('Email')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your email address')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Reset Password/i})).toBeInTheDocument();
  });

  it('shows validation error when email is empty', async () => {
    renderForgotPassword();
    const emailInput = screen.getByPlaceholderText('Enter your email address');
    await userEvent.click(emailInput);
    await userEvent.tab();
    await waitFor(() => {
      expect(screen.getByText('Email is required')).toBeInTheDocument();
    });
  });

  it('shows validation error for invalid email', async () => {
    renderForgotPassword();
    const emailInput = screen.getByPlaceholderText('Enter your email address');
    await userEvent.type(emailInput, 'invalid-email');
    await userEvent.tab();
    await waitFor(() => {
      expect(screen.getByText(/Please enter a valid email/i)).toBeInTheDocument();
    });
  });

  it('calls forgotPassword mutation on valid submit', async () => {
    renderForgotPassword();
    const emailInput = screen.getByPlaceholderText('Enter your email address');
    await userEvent.type(emailInput, '<EMAIL>');
    const submitButton = screen.getByRole('button', {name: /Reset Password/i});
    await userEvent.click(submitButton);
    await waitFor(() => {
      expect(mockForgotPassword).toHaveBeenCalledWith({email: '<EMAIL>'});
    });
  });

  it('shows error message on mutation error', async () => {
    mockForgotPassword.mockReturnValueOnce({
      unwrap: vi.fn().mockRejectedValue(new Error('Failed to send reset email. Please try again.')),
    });
    renderForgotPassword();
    const emailInput = screen.getByPlaceholderText('Enter your email address');
    await userEvent.type(emailInput, '<EMAIL>');
    const submitButton = screen.getByRole('button', {name: /Reset Password/i});
    await userEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.getByText(/Failed to send reset email/i)).toBeInTheDocument();
    });
  });

  it('shows reset sent state and allows resending link', async () => {
    // Simulate successful reset
    mockForgotPassword.mockReturnValueOnce({
      unwrap: vi.fn().mockResolvedValue({}),
    });
    renderForgotPassword();
    const emailInput = screen.getByPlaceholderText('Enter your email address');
    await userEvent.type(emailInput, '<EMAIL>');
    const submitButton = screen.getByRole('button', {name: /Reset Password/i});
    await userEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/Please check your email for verification/i)).toBeInTheDocument();
      expect(screen.getByText(/Reset password link send successfully/i)).toBeInTheDocument();
      expect(screen.getByRole('button', {name: /Resend Link/i})).toBeInTheDocument();
    });

    // Click resend link
    const resendButton = screen.getByRole('button', {name: /Resend Link/i});
    await userEvent.click(resendButton);
    await waitFor(() => {
      expect(mockForgotPassword).toHaveBeenCalled();
    });
  });

  it('renders "Sign in" link with correct href', () => {
    renderForgotPassword();
    const signInLink = screen.getByText(/Sign in/i);
    expect(signInLink).toBeInTheDocument();
    expect(signInLink.closest('a')).toHaveAttribute('href', '/login');
  });
});
