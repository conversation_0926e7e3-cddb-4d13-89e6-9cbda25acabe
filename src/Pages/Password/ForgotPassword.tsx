import ReportProblemOutlinedIcon from '@mui/icons-material/ReportProblemOutlined';
import {Box, InputAdornment, Typography} from '@mui/material';
import EmailIcon from 'Assets/EmailIcon';
import KeyIcon from 'Assets/KeyIcon';
import Form from 'Components/Forms/Form';
import {emailRegEx} from 'Constants/enums';
import {AnyObject} from 'Helpers/utils';
import React, {useState} from 'react';
import {useForgotPasswordMutation} from 'redux/auth/authApiSlice';
import {
  emailTextStyles,
  IconWrapper,
  KeyIconContainer,
  LogoContainer,
  signedInTextStyles,
  signInTextStyles,
  signInWrapper,
  StyledButton,
  WelcomeTitle,
} from 'styles/pages/ForgotPassword.styles';
import * as Yup from 'yup';
import {
  errorIconStyles,
  errorStyles,
  FieldLabel,
  FormFieldContainer,
  HeaderContainer,
  IconDivider,
  inputError<PERSON>ty<PERSON>,
  inputSty<PERSON>,
  <PERSON>gin<PERSON>ard,
  <PERSON>gin<PERSON>ontainer,
  <PERSON>d<PERSON>ist<PERSON><PERSON><PERSON>,
  StyledEmailIcon,
  StyledFormInput,
  SubTitle,
} from '../../styles/pages/Login.styles';

const emailMaxLen = 254;
const forgotPasswordValidationSchema = Yup.object().shape({
  email: Yup.string()
    .required('Email is required')
    .email('Please enter a valid email address')
    .matches(emailRegEx, 'Email format is invalid')
    .max(emailMaxLen, 'Email must be less than 255 characters'),
});

const initialValues = {
  email: '',
};

interface PageLayoutProps {
  children: React.ReactNode;
  title: string;
  subtitle: React.ReactNode;
  isEmailSent?: boolean;
}

/**
 * PageLayout is a reusable layout component for authentication-related pages.
 * It displays a logo, an icon (either a key or an email icon depending on the `isEmailSent` prop),
 * a title, a subtitle, and renders its children content. It also provides a link to the sign-in page.
 *
 * @param {PageLayoutProps} props - The props for the PageLayout component.
 * @param {React.ReactNode} props.children - The content to be rendered inside the layout.
 * @param {string} props.title - The main title displayed on the page.
 * @param {string} props.subtitle - The subtitle displayed below the title.
 * @param {boolean} [props.isEmailSent=false] - Determines whether to show the email icon (if true) or the key icon (if false).
 *
 * @returns {JSX.Element} The rendered PageLayout component.
 */
const PageLayout: React.FC<PageLayoutProps> = ({children, title, subtitle, isEmailSent = false}) => (
  <LoginContainer data-testid="ForgotPasswordPage">
    <LoginCard elevation={0}>
      <HeaderContainer>
        <LogoContainer>
          <StyledDistekLogo />
        </LogoContainer>

        <IconWrapper>
          <KeyIconContainer>{isEmailSent ? <EmailIcon /> : <KeyIcon width={40} height={40} />}</KeyIconContainer>
        </IconWrapper>

        <WelcomeTitle variant="h4">{title}</WelcomeTitle>
        <SubTitle variant="h6">{subtitle}</SubTitle>
      </HeaderContainer>

      {children}

      <Box sx={signInWrapper}>
        <Typography sx={signedInTextStyles}>Go back to </Typography>
        <Box sx={signInTextStyles}>
          <a href="/login">Sign in</a>
        </Box>
      </Box>
    </LoginCard>
  </LoginContainer>
);

/**
 * ForgotPasswordPage component handles the "Forgot Password" flow for users.
 *
 * - Renders a form for users to enter their email address to request a password reset link.
 * - Handles form submission, displays loading state, and manages error messages.
 * - After a successful reset request, shows a confirmation message and allows users to resend the reset link if needed.
 * - Utilizes custom hooks and components for form handling, mutation, and UI styling.
 *
 * @component
 * @returns {JSX.Element} The Forgot Password page UI.
 */
const ForgotPasswordPage = () => {
  const [resetError, setResetError] = useState<string | null>(null);
  const [emailId, setEmailId] = useState('');
  const [resetSent, setResetSent] = useState(false);

  const [forgotPassword, {isLoading: mutationLoading}] = useForgotPasswordMutation();

  const handleSubmit = async (values: {email: string}, actions: AnyObject) => {
    setResetError(null);
    setEmailId(values.email);

    try {
      await forgotPassword({email: values.email}).unwrap();
      setResetSent(true);
    } catch {
      const errorMessage = `Failed to send reset email. Please try again.`;
      setResetError(errorMessage);
    } finally {
      if (typeof actions.setSubmitting === 'function') {
        actions.setSubmitting(false);
      }
    }
  };

  const renderResetForm = () => (
    <Form
      initialValues={initialValues}
      onSubmit={handleSubmit}
      validationSchema={forgotPasswordValidationSchema}
      validateOnBlur={true}
      validateOnChange={true}
    >
      <FormFieldContainer>
        <FieldLabel variant="body2">Email</FieldLabel>
        <StyledFormInput
          id="email"
          name="email"
          placeholder="Enter your email address"
          fullWidth
          sx={inputStyles}
          errorSx={inputErrorStyles}
          startAdornment={
            <InputAdornment position="start" sx={{margin: 0}}>
              <StyledEmailIcon />
              <IconDivider />
            </InputAdornment>
          }
        />
      </FormFieldContainer>

      {resetError && (
        <Box sx={errorStyles(resetError)}>
          <InputAdornment position="start">
            <ReportProblemOutlinedIcon sx={errorIconStyles} />
          </InputAdornment>
          {resetError}
        </Box>
      )}

      <StyledButton type="submit" variant="contained" fullWidth isLoading={mutationLoading}>
        {mutationLoading ? 'Sending...' : 'Reset Password'}
      </StyledButton>
    </Form>
  );

  const handleResendLink = async () => {
    setResetError(null);
    try {
      await forgotPassword({email: emailId}).unwrap();
      setResetSent(true);
    } catch {
      const errorMessage = `Failed to send reset email. Please try again.`;
      setResetError(errorMessage);
    }
  };

  const renderResetSent = () => (
    <>
      {resetError && (
        <Box sx={errorStyles(resetError)}>
          <InputAdornment position="start">
            <ReportProblemOutlinedIcon sx={errorIconStyles} />
          </InputAdornment>
          {resetError}
        </Box>
      )}
      <StyledButton type="button" variant="contained" fullWidth onClick={handleResendLink} isLoading={mutationLoading}>
        {`Didn't Get An Email? Resend Link`}
      </StyledButton>
    </>
  );

  return (
    <>
      {resetSent ? (
        <PageLayout
          title="Please check your email for verification"
          subtitle={
            <>
              Reset password link send successfully to <Box sx={emailTextStyles}>{emailId}</Box>
            </>
          }
          isEmailSent={true}
        >
          {renderResetSent()}
        </PageLayout>
      ) : (
        <PageLayout
          title="Forgot your Password? We've got you covered!"
          subtitle="To reset your password, enter your email address below."
        >
          {renderResetForm()}
        </PageLayout>
      )}
    </>
  );
};

export default ForgotPasswordPage;
