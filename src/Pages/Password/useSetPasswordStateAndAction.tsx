import {useCallback} from 'react';
import {IResetPassword, useResetPasswordMutation, useSetActivationPasswordMutation} from 'redux/auth/authApiSlice';

export const enum Mode {
  SetPassword = 'set-password',
  ResetPassword = 'reset-password',
}

/**
 * Custom hook to manage password setting and resetting actions.
 * @param mode - The mode of the action (set or reset password).
 * @returns An object containing various strings and the performPasswordAction function.
 */
const useSetPasswordStateAndAction = (mode: Mode) => {
  const [resetPassword, {isLoading: mutationLoading}] = useResetPasswordMutation();
  const [setPassword, {isLoading: setPasswordLoading}] = useSetActivationPasswordMutation();

  /**
   * Title strings for the different modes.
   */
  const titleString: {[key in Mode]: string} = {
    // NOSONAR -- UI text, not a hardcoded password
    [Mode.SetPassword]: 'Set Your password', // NOSONAR
    [Mode.ResetPassword]: 'Reset your password', // NOSONAR
  };

  /**
   * Subtitle strings for the different modes.
   */
  const subTitleString: {[key in Mode]: string} = {
    [Mode.SetPassword]: 'Create a strong password to get started with your account.', // NOSONAR - UI text, not hardcoded password
    [Mode.ResetPassword]: 'Enter a new password to reset your password', // NOSONAR - UI text, not hardcoded password
  };

  /**
   * Success message parts for the different modes.
   */
  const successMessagePart: {[key in Mode]: string} = {
    [Mode.SetPassword]: 'created', // NOSONAR
    [Mode.ResetPassword]: 'updated', // NOSONAR
  };

  /**
   * Submit button titles for the different modes.
   */
  const submitButtonTitle: {[key in Mode]: string} = {
    [Mode.SetPassword]: 'Set Password', // NOSONAR
    [Mode.ResetPassword]: 'Change Password', // NOSONAR
  };

  const tokenNotFoundErrorMessage: {[key in Mode]: string} = {
    [Mode.SetPassword]: 'No token found. Please use the link from your set password email.', // NOSONAR
    [Mode.ResetPassword]: 'No token found. Please use the link from your reset password email.', // NOSONAR
  };

  const passwordAction = useCallback(
    (arg: IResetPassword) => {
      switch (mode) {
        case Mode.SetPassword:
          return setPassword(arg);
        case Mode.ResetPassword:
          return resetPassword(arg);
      }
    },
    [mode, resetPassword, setPassword],
  );

  return {
    titleString,
    subTitleString,
    successMessagePart,
    submitButtonTitle,
    tokenNotFoundErrorMessage,
    performPasswordAction: passwordAction,
    isLoading: mutationLoading || setPasswordLoading,
  };
};

export default useSetPasswordStateAndAction;
