import {Box, Grid, SxProps, Theme, useTheme} from '@mui/material';
import Button from 'Components/Button';
import {FormikValues, useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import React from 'react';
import {buttonGridStyles, cancelButtonStyles} from 'styles/pages/AddPlan.styles';

interface PlanFormButtonsProps {
  onCancel: () => void;
  isLoading: boolean;
}

export const submitButtonStyles: SxProps<Theme> = {
  width: '12.5rem',
  height: '3.125rem',
  backgroundColor: theme => theme.palette.secondary.main,
  color: theme => theme.palette.white.main,
  '&:hover': {
    backgroundColor: theme => theme.palette.secondary.main,
  },
  '&.Mui-disabled': {
    backgroundColor: theme => theme.palette.secondary[Integers.OneHundred],
    color: theme => theme.palette.white.main, // optional: set disabled text color
  },
  fontSize: '1rem',
};

const FormButtons: React.FC<PlanFormButtonsProps> = ({onCancel, isLoading}) => {
  const {isValid, dirty} = useFormikContext<FormikValues>();
  const theme = useTheme();

  const isFormValid = isValid && dirty;

  return (
    <>
      <Grid size={{xs: 12}} sx={{mt: 3, mb: 2, padding: 0, margin: 0}}>
        <Box
          sx={{borderTop: `0.0625rem solid ${theme.palette.white[Integers.ThreeHundred]}`, width: '100%', padding: 0}}
        />
      </Grid>
      <Grid size={{xs: 12}} sx={buttonGridStyles}>
        <Button
          type="button"
          variant="outlined"
          color="secondary"
          data-testid="cancel-button"
          sx={cancelButtonStyles}
          onClick={onCancel}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          isLoading={isLoading}
          variant="contained"
          color="primary"
          sx={submitButtonStyles}
          disabled={!isFormValid}
          data-testid="submit-button"
        >
          {'Add Pending Tenant'}
        </Button>
      </Grid>
    </>
  );
};

export default FormButtons;
