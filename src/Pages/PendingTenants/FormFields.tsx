import {Grid, InputAdornment, MenuItem, Select, Typography} from '@mui/material';
import FormInput from 'Components/Forms/FormInput';
import FormSelect from 'Components/Forms/FormSelect/FormSelect';
import {useFormikContext} from 'formik';
import {StyleUtils} from 'Helpers/styleUtils';
import {countryCodes} from 'Pages/Tenants/AddTenant/addTenantsUtils';
import React, {useEffect, useState} from 'react';
import {GetCity, GetState} from 'react-country-state-city';
import {ICurrency, PlanResponse} from 'redux/app/types/plan.type';
import NoItemRenderer from './NoItemRenderer';
import {FormAddPendingTenant} from './pendingTenants.utils';

interface PlanFormFieldsProps {
  deviceOptions?: Array<{value: string; label: string}>;
  tenureOptions?: Array<{value: string; label: string}>;
  currencyCodeOptions?: ICurrency;
  injectedValues?: PlanResponse;
}
type CustomNoItemRendererProps = {
  stateSelected: boolean;
};

export const CustomNoItemRenderer: React.FC<CustomNoItemRendererProps> = ({stateSelected}) => (
  <NoItemRenderer stateSelected={stateSelected} />
);

const renderNoItem = (stateSelected: boolean) => () => <CustomNoItemRenderer stateSelected={stateSelected} />;

const FormFields: React.FC<PlanFormFieldsProps> = () => {
  const {values, setFieldValue} = useFormikContext<FormAddPendingTenant>();
  const [cityOptions, setCityOptions] = useState<{value: string; label: string}[]>([]);
  const [stateOptions, setStateOptions] = useState<{value: string; label: string}[]>([]);

  const USA_ID = 233;

  useEffect(() => {
    // Load states on mount
    const loadStates = async () => {
      try {
        const states = await GetState(USA_ID);
        setStateOptions(
          (states || []).map(state => ({
            value: state.id?.toString() || state.name,
            label: state.name,
          })),
        );
      } catch {
        setStateOptions([]);
      }
    };
    loadStates();
  }, []);
  useEffect(() => {
    if (!values.state) {
      setCityOptions([]);
      setFieldValue('city', '');
      return;
    }
    GetCity(USA_ID, Number(values.state)).then(cities => {
      setCityOptions(
        (cities || []).map(city => ({
          value: city.id?.toString() || city.name,
          label: city.name,
        })),
      );
      setFieldValue('city', '');
    });
  }, [values.state]);

  return (
    <Grid
      container
      spacing={2}
      rowSpacing={2}
      sx={{height: '100%', boxSizing: 'border-box', padding: 2, width: '100%'}}
    >
      {/* Section Header */}
      <Grid size={{xs: 12}}>
        <Typography variant="h6" fontWeight={600} sx={{fontSize: '1rem'}}>
          Tenant Information
        </Typography>
      </Grid>

      {/* Company Name */}
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Company *</Typography>
        <FormInput
          fullWidth
          id="companyName"
          name="companyName"
          required
          sx={{...StyleUtils.inputBoxStyles}}
          placeholder="Enter company"
          data-testid="company-name-input"
        />
      </Grid>

      {/* Contact Info Header */}
      <Grid size={{xs: 12}} sx={{mt: 2}}>
        <Typography variant="h6" fontWeight={600} sx={{fontSize: '1rem'}}>
          Contact Information
        </Typography>
      </Grid>

      {/* Contact Fields */}
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>First name *</Typography>
        <FormInput
          fullWidth
          id="firstName"
          name="firstName"
          placeholder="Enter first name"
          required
          sx={{...StyleUtils.inputBoxStyles}}
          data-testid="first-name-input"
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6, md: 6, lg: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Last name *</Typography>
        <FormInput
          fullWidth
          id="lastName"
          name="lastName"
          placeholder="Enter last name"
          required
          sx={{...StyleUtils.inputBoxStyles}}
          data-testid="last-name-input"
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Job title</Typography>
        <FormInput
          fullWidth
          id="designation"
          name="designation"
          placeholder="Enter job title"
          sx={{...StyleUtils.inputBoxStyles}}
          data-testid="job-title-input"
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Primary phone number</Typography>
        <FormInput
          fullWidth
          id="phoneNumber"
          name="phoneNumber"
          placeholder="Enter primary phone number"
          onInput={e => {
            const target = e.target as HTMLInputElement;
            target.value = target.value.replace(/\D/g, '');
          }}
          data-testid="phone-number-input"
          sxProps={{paddingLeft: 0}}
          sx={{...{...StyleUtils.inputBoxStyles}, p: 0}}
          startAdornment={
            <InputAdornment position="start" sx={{...StyleUtils.inputAdornment}}>
              <Select
                data-testid="country-code-select"
                variant="standard"
                disableUnderline
                disabled
                defaultValue={values?.countryCode?.code}
                sx={{
                  ...StyleUtils.selectBox,
                  '& .Mui-disabled': {
                    '-webkit-text-fill-color': 'black.main',
                  },
                }}
                IconComponent={() => null}
              >
                {countryCodes.map(option => (
                  <MenuItem key={option.code} value={option.code} data-testid={`country-code-${option.code}`}>
                    {option.code}
                  </MenuItem>
                ))}
              </Select>
            </InputAdornment>
          }
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>Email address *</Typography>
        <FormInput
          fullWidth
          id="email"
          name="email"
          type="email"
          placeholder="Enter email address"
          required
          sx={{...StyleUtils.inputBoxStyles}}
          data-testid="email-input"
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>State *</Typography>
        <FormSelect
          fullWidth
          id="state"
          name="state"
          placeholder="Select state"
          required
          sx={{...StyleUtils.selectBoxStyles}}
          options={stateOptions}
          menuPlacement="top"
          placeholderSx={{color: 'body.300'}}
          onChange={value => {
            const selected = stateOptions.find(opt => opt.value === value);
            setFieldValue('state', value);
            setFieldValue('stateId', selected ? selected.label : '');
          }}
          data-testid="state-select"
        />
      </Grid>
      <Grid size={{xs: 12, sm: 6}}>
        <Typography sx={StyleUtils.lalelStyles}>City *</Typography>
        <FormSelect
          fullWidth
          id="city"
          name="city"
          placeholder="Select city"
          required
          sx={{...StyleUtils.selectBoxStyles}}
          options={cityOptions}
          disabled={!values.state}
          menuPlacement="top"
          placeholderSx={{color: 'body.300'}}
          onChange={value => {
            const selected = cityOptions.find(opt => opt.value === value);
            setFieldValue('city', value);
            setFieldValue('cityId', selected ? selected.label : '');
          }}
          noItemRenderer={renderNoItem(!!values.state)}
          data-testid="city-select"
        />
      </Grid>
    </Grid>
  );
};
export default FormFields;
