import {Box, styled, Typography} from '@mui/material';
import InvalidIcon from 'Assets/InvalidIcon';
import BorderButton from 'Components/BorderButton/BorderButton';
import Button from 'Components/Button';
import {DefaultDialog} from 'Components/DefaultDialog/DefaultDialog';
import {Integers} from 'Helpers/integers';

interface InvalidDialogProps {
  open: boolean;
  actionType: string;
  onClose: () => void;
  onConfirm: () => void;
  title: string;
  isLoading: boolean;
}

export const IconWrapper = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
}));

export const KeyIconContainer = styled(Box)(({theme}) => {
  return {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '5rem',
    height: '5rem',
    borderRadius: '50%',
    border: `1px solid ${theme.palette.white[Integers.TwoHundred]}`,
    padding: '1rem',
    backgroundColor: theme.palette.alert.error.bg,
    fill: 'transparent',
    color: theme.palette.alert.success.bg,
  };
});

/**
 * Displays a dialog for confirming actions on a Pending Tenant, such as converting to a Tenant or marking as invalid.
 *
 * @param open - Controls whether the dialog is open.
 * @param actionType - The type of action to confirm ('convert' or 'invalid').
 * @param onClose - Callback fired when the dialog is closed.
 * @param onConfirm - Callback fired when the confirm action is taken.
 * @param title - Additional title or message to display in the dialog.
 * @param isLoading - Indicates if the confirm action is in a loading state.
 *
 * @returns A dialog component prompting the user to confirm the selected action.
 */
export const InvalidDialog = ({open, actionType, onClose, onConfirm, title, isLoading}: InvalidDialogProps) => {
  const icon = <InvalidIcon sx={{fill: 'body.500', color: 'body.500'}} />;
  const actionMessage =
    actionType === 'convert'
      ? 'Are you sure you want to convert this Pending Tenant to Tenant?'
      : 'Are you sure you want to mark this Pending Tenant as invalid?';

  return (
    <DefaultDialog
      title={actionType === 'convert' ? 'Convert to Tenant' : 'Mark as invalid'}
      maxWidth={400}
      open={open}
      onClose={onClose}
    >
      <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', mt: 4, p: 2}}>
        <IconWrapper>
          <KeyIconContainer>{icon}</KeyIconContainer>
        </IconWrapper>

        <Typography sx={{mt: 2, fontSize: '1.125rem', fontWeight: 700, color: 'body.dark', textAlign: 'center'}}>
          {actionMessage}
        </Typography>
        <Typography
          sx={{
            mt: 1,
            fontSize: '0.813rem',
            fontWeight: 600,
            color: 'body.500',
            textAlign: 'center',
          }}
        >
          {title}
        </Typography>
        <Box
          sx={{mt: 4, display: 'flex', gap: 1, flexDirection: 'row', width: '100%', fontSize: '1rem', fontWeight: 600}}
        >
          <BorderButton sx={{flex: 1, color: 'body.dark', fontWeight: '600', fontSize: '1rem'}} onClick={onClose}>
            Cancel
          </BorderButton>
          <Button
            variant="contained"
            isLoading={isLoading}
            sx={{
              borderColor: 'body.100',
              borderRadius: '0.375rem',
              color: isLoading ? 'primary' : 'other.white',
              fontWeight: '600',
              fontSize: '1rem',
              backgroundColor: 'secondary.main',
              flex: 1,
              py: '0.875rem',
              px: '1.75rem',
            }}
            onClick={onConfirm}
            data-testid={'dialog-invalid-button'}
          >
            {actionType === 'convert' ? 'Convert' : 'Mark As Invalid'}
          </Button>
        </Box>
      </Box>
    </DefaultDialog>
  );
};
