import {Box, Breakpoint, Divider, Grid, Typography} from '@mui/material';
import ConvertCheckIcon from 'Assets/ConvertCheckIcon';
import InvalidIcon from 'Assets/InvalidIcon';
import BackdropLoader from 'Components/BackdropLoader';
import BorderButton from 'Components/BorderButton/BorderButton';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import StatusChip from 'Components/StatusChip/StatusChip';
import {TableCellBoxWithToolTip} from 'Components/TableCellBox';
import {defaultDateFormat} from 'Constants/enums';
import {getFullName} from 'Helpers/utils';
import {useSnackbar} from 'notistack';
import {getCityOptions, getStateOptions} from 'Pages/utils';
import React, {useState} from 'react';
import {useNavigate, useParams} from 'react-router';
import {useGetLeadByIdQuery, useUpdateLeadByIdMutation} from 'redux/app/leadManagementApiSlice';
import {RouteNames} from 'Routes/routeNames';
import {InvalidDialog} from './InvalidDialog';
import {
  formatUSPhoneNumber,
  getFontColor,
  getIndicatorColor,
  getStatusColor,
  getStatusLabel,
  LeadStatus,
} from './pendingTenants.utils';

const PX = 1.5;

export const titleLabelSx = {
  fontWeight: 600,
  fontSize: '0.75rem',
  color: 'body.500',
};

/**
 * Styles for the subtitle label in the user detail view.
 */
export const subtitleLabelSx = {
  fontWeight: 600,
  fontSize: '0.875rem',
  color: 'body.800',
  textTransform: 'capitalize',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
};

const canNotLoadTenantPage = () => (
  <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center', minHeight: 300}}>
    <Typography variant="h6" color="text.secondary" sx={{mb: 2}}>
      Unable to load pending tenant page: Missing pending tenant information.
    </Typography>
  </Box>
);

const gridSize: {[key in Breakpoint]?: number | null} = {xs: 12, sm: 4, md: 3};

const getIconStyle = (leadStatus: LeadStatus) => {
  return {
    color: leadStatus === LeadStatus.CONVERTED || leadStatus === LeadStatus.INVALID ? 'body.200' : 'body.500',
    fill: leadStatus === LeadStatus.CONVERTED || leadStatus === LeadStatus.INVALID ? 'body.200' : 'body.500',
    width: '0.95rem',
  };
};

/**
 * Displays the details of a pending tenant lead, allowing users to view information,
 * convert the lead to a tenant, or mark the lead as invalid.
 *
 * - Fetches lead data by ID from the URL params.
 * - Shows lead information including name, job title, email, phone, city, state, and creation date.
 * - Provides actions to convert the lead or mark as invalid, with confirmation dialogs.
 * - Handles loading and error states.
 * - Uses Material UI components for layout and styling.
 *
 * @returns {JSX.Element} The PendingTenantDetail page component.
 */
export default function PendingTenantDetail() {
  const {leadId} = useParams<{leadId: string}>();
  const {enqueueSnackbar} = useSnackbar();
  const [openDialog, setOpenDialog] = useState(false);
  const [actionType, setActionType] = useState<string | null>(null);

  const [updateStatus, {isLoading: isUpdatingStatus}] = useUpdateLeadByIdMutation();

  const handleDialog = (str: string) => {
    setOpenDialog(true);
    setActionType(str);
  };

  const handleDialogClose = () => {
    setOpenDialog(false);
  };

  const {
    data: lead,
    isFetching: isLeadLoading,
    error: leadError,
    refetch: refetchLead,
  } = useGetLeadByIdQuery(leadId || '', {
    skip: !leadId,
  }) || {};

  const navigate = useNavigate();

  const handleInvalidDialog = async (leadId: string, companyName: string) => {
    if (actionType === 'convert') {
      const stateId = await getStateOptions(lead?.address?.state ?? '');
      const cityId = await getCityOptions(stateId, lead?.address?.city ?? '');
      const leadInfo = {
        leadId: leadId,
        firstName: lead?.firstName,
        lastName: lead?.lastName,
        companyName: lead?.companyName,
        email: lead?.email,
        designation: lead?.designation,
        phoneNumber: lead?.phoneNumber,
        countryCode: lead?.countryCode,
        city: lead?.address?.city,
        state: lead?.address?.state,
        cityId: cityId,
        stateId: stateId,
      };
      navigate(RouteNames.ADD_TENANT, {state: {leadInfo: leadInfo}});
      setOpenDialog(false);
      return;
    }
    updateStatus({leadId: leadId, status: LeadStatus.INVALID})
      .then(result => {
        if (result.error) {
          setOpenDialog(false);
          return;
        }
        enqueueSnackbar(`Pending tenant marked as invalid successfully!`, {
          variant: 'success',
          subMessage: getFullName(lead) + ' - ' + companyName,
        });
        setOpenDialog(false);
        refetchLead();
      })
      .catch(() => {});
  };

  React.useEffect(() => {
    if (leadError) {
      enqueueSnackbar('Failed to pending tenant data', {variant: 'error'});
    }
  }, [leadError, enqueueSnackbar]);

  const breadcrumbs = (leadName: string) => {
    return [
      {
        label: 'Pending Tenants',
        url: RouteNames.PENDING_TENANTS,
      },
      {
        label: leadName,
        url: `${RouteNames.PENDING_TENANTS}/${leadId}`,
      },
    ];
  };

  const buildBottomSection = () => {
    return (
      <Box sx={{display: 'flex', flexDirection: 'column', mt: 'auto', mb: 1.5}} data-testid="bottom-section">
        <Divider sx={{my: 2}} />
        <Box sx={{alignSelf: 'flex-end'}} px={PX}>
          <BorderButton
            sx={{height: '3.125rem', minWidth: '6.25rem', fontSize: '1rem', fontWeight: 700}}
            fullWidth={false}
            onClick={() => navigate(-1)}
          >
            Close
          </BorderButton>
        </Box>
      </Box>
    );
  };

  const buildTopPart = () => {
    const status = lead?.status as LeadStatus;
    const backgroundColor = getStatusColor(status);
    const color = getFontColor(status);
    const indicatorColor = getIndicatorColor(status);
    const label = getStatusLabel(status);
    return (
      <Box display="flex" alignItems="center" gap={1} data-testid="top-part">
        <Box display={'flex'} flexDirection={'column'} gap={1}>
          <Box display={'flex'} flexDirection={'row'} alignItems="center" gap={1}>
            <Typography sx={{fontSize: '1.25rem', fontWeight: 700}}>{lead?.companyName}</Typography>
            <StatusChip label={label} backgroundColor={backgroundColor} indicatorColor={indicatorColor} color={color} />
          </Box>
          <Box sx={{display: 'flex', alignItems: 'center'}}>
            <Box
              sx={{
                height: 12,
                borderColor: 'body.100',
              }}
            />
            <Breadcrumb items={breadcrumbs(lead?.companyName ?? '')} separator="|" />
          </Box>
        </Box>
        <Box sx={{ml: 'auto', display: 'flex', flexDirection: 'row', gap: 1, alignItems: 'center'}}>
          <BorderButton
            data-testid="convert-button"
            sx={{fontWeight: 700, px: '1.56rem', color: 'body.dark'}}
            disabled={status === LeadStatus.CONVERTED || status === LeadStatus.INVALID}
            onClick={() => handleDialog('convert')}
          >
            <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}} data-testid="convert-button">
              <ConvertCheckIcon sx={getIconStyle(status)} data-testid="download-icon" />
              Convert To Tenant
            </Box>
          </BorderButton>
          <BorderButton
            data-testid="invalid-button"
            sx={{fontWeight: 700, px: '1.56rem', color: 'body.dark'}}
            onClick={() => handleDialog('invalid')}
            disabled={status === LeadStatus.CONVERTED || status === LeadStatus.INVALID}
          >
            <Box sx={{display: 'flex', alignItems: 'center', gap: 0.5}} data-testid="invalid-button">
              <InvalidIcon sx={getIconStyle(status)} data-testid="download-icon" />
              Mark As Invalid
            </Box>
          </BorderButton>
        </Box>
      </Box>
    );
  };

  if (isLeadLoading) {
    return <BackdropLoader />;
  }

  if (!leadId) {
    return canNotLoadTenantPage();
  }

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 0.5,
      }}
    >
      {buildTopPart()}
      {openDialog && lead && (
        <InvalidDialog
          onConfirm={() => handleInvalidDialog(lead.id, lead.companyName)}
          open={openDialog}
          actionType={actionType as 'invalid' | 'convert'}
          onClose={handleDialogClose}
          isLoading={isUpdatingStatus}
          title={lead.companyName + ' - ' + lead.firstName + ' ' + lead.lastName}
        />
      )}
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          gap: 1.5,
        }}
      >
        <Box
          sx={{
            border: '1px solid',
            borderColor: 'body.200',
            borderRadius: '0.375rem',
            pt: 1,
            minHeight: '80vh',
            display: 'flex',
            flexDirection: 'column',
          }}
          data-testid="lead-detail"
        >
          <Typography sx={{fontSize: '1rem', fontWeight: 700, color: 'body.900'}} px={PX}>
            Pending tenant information
          </Typography>

          <Grid container spacing={2} px={PX} mt={1}>
            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Name</Typography>
              <TableCellBoxWithToolTip sx={subtitleLabelSx}>
                {lead?.firstName + ' ' + lead?.lastName}
              </TableCellBoxWithToolTip>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Job title</Typography>
              <Typography sx={subtitleLabelSx}>{lead?.designation ?? '-'}</Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Email address</Typography>
              {lead?.email}
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Phone number</Typography>
              {formatUSPhoneNumber(lead?.countryCode ?? '', lead?.phoneNumber)}
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>City</Typography>
              <Typography sx={subtitleLabelSx}>{lead?.address?.city}</Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>State</Typography>
              <Typography sx={subtitleLabelSx}>{lead?.address?.state}</Typography>
            </Grid>

            <Grid size={gridSize}>
              <Typography sx={titleLabelSx}>Created on</Typography>
              <Typography sx={subtitleLabelSx}>{defaultDateFormat(lead?.createdOn ?? '')}</Typography>
            </Grid>
          </Grid>
          {buildBottomSection()}
        </Box>
      </Box>
    </Box>
  );
}
