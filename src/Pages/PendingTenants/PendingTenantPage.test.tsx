// @vitest-environment jsdom
import {ThemeProvider, createTheme} from '@mui/material/styles';
import '@testing-library/jest-dom';
import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import React from 'react';
import {vi} from 'vitest';
import PendingTenant, {ActionButtons, actionStyles} from './PendingTenantPage';
import {LeadStatus} from './pendingTenants.utils';

// Complete theme mock that matches the real application theme
const robustTheme = createTheme({
  palette: {
    body: {
      500: '#9e9e9e',
      200: '#eeeeee',
      300: '#bdbdbd',
      800: '#424242',
      900: '#212121',
      100: '#f5f5f5',
      dark: '#000000',
    },
    black: {main: '#000'},
    primary: {main: '#1976d2'},
    white: {100: '#fff', main: '#ffffff'},
    secondary: {main: '#dc004e', 100: '#f8f8f8', 300: '#e0e0e0'},
    alert: {
      warning: {bg: '#fff3cd', main: '#856404', onBg: '#856404', border: '#ffeaa7'},
      success: {bg: '#d4edda', main: '#155724', onBg: '#155724', border: '#c3e6cb'},
      error: {bg: '#f8d7da', main: '#721c24', onBg: '#721c24', border: '#f5c6cb'},
    },
  },
});

function renderWithTheme(ui: React.ReactElement) {
  return render(<ThemeProvider theme={robustTheme}>{ui}</ThemeProvider>);
}

// Mock data for realistic testing
const mockLeadsData = [
  {
    id: '1',
    name: 'John Doe',
    companyName: 'Test Company 1',
    email: '<EMAIL>',
    status: LeadStatus.PENDING,
    createdDate: '2024-01-15T10:00:00Z',
    phoneNumber: '**********',
    countryCode: '+1',
  },
  {
    id: '2',
    name: 'Jane Smith',
    companyName: 'Test Company 2',
    email: '<EMAIL>',
    status: LeadStatus.CONVERTED,
    createdDate: '2024-01-14T09:00:00Z',
    phoneNumber: '**********',
    countryCode: '+1',
  },
  {
    id: '3',
    name: 'Bob Wilson',
    companyName: 'Test Company 3',
    email: '<EMAIL>',
    status: LeadStatus.INVALID,
    createdDate: '2024-01-13T08:00:00Z',
    phoneNumber: '**********',
    countryCode: '+1',
  },
];

const mockStatusesData = {
  statuses: {
    [LeadStatus.PENDING]: 'Pending',
    [LeadStatus.CONVERTED]: 'Converted',
    [LeadStatus.INVALID]: 'Invalid',
  },
};

// Essential mocks - only mock what we can't easily test
const mockGetLeadsQuery = vi.fn();
const mockGetLeadsCountQuery = vi.fn();
const mockGetAllLeadStatusesQuery = vi.fn();
const mockEnqueueSnackbar = vi.fn();
const mockNavigate = vi.fn();

const mockUpdateStatus = vi.fn();
// Mock only the API calls, not the components
vi.mock('redux/app/leadManagementApiSlice', () => ({
  useGetLeadsQuery: (...args: any[]) => mockGetLeadsQuery(...args),
  useGetLeadsCountQuery: (...args: any[]) => mockGetLeadsCountQuery(...args),
  useGetAllLeadStatusesQuery: (...args: any[]) => mockGetAllLeadStatusesQuery(...args),
  useUpdateLeadByIdMutation: () => [mockUpdateStatus, {isLoading: false}],
}));

vi.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: mockEnqueueSnackbar,
  }),
}));

vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));
// Mock only external assets and complex components that don't affect core logic
vi.mock('Assets/EyeIcon', () => ({
  __esModule: true,
  default: (props: any) => (
    <button data-testid="eye-icon" onClick={props.onClick} style={props.sx}>
      👁️ View
    </button>
  ),
}));

vi.mock('Assets/ConvertCheckIcon', () => ({
  __esModule: true,
  default: (props: any) => (
    <button data-testid="convert-icon" onClick={props.onClick} style={props.sx}>
      ✅ Convert
    </button>
  ),
}));

vi.mock('Assets/InvalidIcon', () => ({
  __esModule: true,
  default: (props: any) => (
    <button data-testid="invalid-icon" onClick={props.onClick} style={props.sx}>
      ❌ Invalid
    </button>
  ),
}));

vi.mock('Assets/search-icon.svg', () => ({
  __esModule: true,
  default: 'search-icon.svg',
}));

vi.mock('Assets/tenant-filter-icon.svg', () => ({
  __esModule: true,
  default: 'filter-icon.svg',
}));

describe('PendingTenantPage - Real World Implementation Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Default successful API responses
    mockGetLeadsQuery.mockReturnValue({
      data: mockLeadsData,
      isFetching: false,
      error: null,
    });

    mockGetLeadsCountQuery.mockReturnValue({
      data: {count: mockLeadsData.length},
      isFetching: false,
      error: null,
    });

    mockGetAllLeadStatusesQuery.mockReturnValue({
      data: mockStatusesData,
      isLoading: false,
      error: null,
    });
  });

  describe('Loading States', () => {
    it('displays loading spinner when data is being fetched', () => {
      mockGetLeadsQuery.mockReturnValue({isFetching: true, data: null, error: null});
      mockGetLeadsCountQuery.mockReturnValue({isFetching: true, data: null, error: null});

      renderWithTheme(<PendingTenant />);

      expect(screen.getByRole('progressbar')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('shows error notification when leads data fails to load', async () => {
      mockGetLeadsQuery.mockReturnValue({
        isFetching: false,
        data: null,
        error: {message: 'Network error'},
      });

      renderWithTheme(<PendingTenant />);

      await waitFor(() => {
        expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to fetch pending tenants data', {variant: 'error'});
      });
    });

    it('shows error notification when count data fails to load', async () => {
      mockGetLeadsCountQuery.mockReturnValue({
        isFetching: false,
        data: null,
        error: {message: 'Count fetch failed'},
      });

      renderWithTheme(<PendingTenant />);

      await waitFor(() => {
        expect(mockEnqueueSnackbar).toHaveBeenCalledWith('Failed to fetch pending tenants count', {variant: 'error'});
      });
    });
  });

  describe('Data Rendering', () => {
    it('renders pending tenant data in table format', () => {
      renderWithTheme(<PendingTenant />);

      // Check that the table component is rendered (use actual table element)
      expect(screen.getByRole('table')).toBeInTheDocument();

      // Verify data is passed to table
      expect(mockGetLeadsQuery).toHaveBeenCalled();
      expect(mockGetLeadsCountQuery).toHaveBeenCalled();
    });

    it('displays correct tenant information', () => {
      renderWithTheme(<PendingTenant />);

      // Check for search functionality
      expect(screen.getByPlaceholderText('Search pending tenants')).toBeInTheDocument();

      // Check for add button
      expect(screen.getByText('Add Pending Tenant')).toBeInTheDocument();
    });

    it('shows empty state when no data', () => {
      mockGetLeadsQuery.mockReturnValue({
        data: [],
        isFetching: false,
        error: null,
      });
      mockGetLeadsCountQuery.mockReturnValue({
        data: {count: 0},
        isFetching: false,
        error: null,
      });

      renderWithTheme(<PendingTenant />);

      // Table should still render but with no data (use actual table element)
      expect(screen.getByRole('table')).toBeInTheDocument();
    });
  });

  describe('User Interactions', () => {
    it('handles search input changes', async () => {
      renderWithTheme(<PendingTenant />);

      const searchInput = screen.getByPlaceholderText('Search pending tenants');

      fireEvent.change(searchInput, {target: {value: 'John Doe'}});

      expect(searchInput).toHaveValue('John Doe');
    });

    it('navigates to add pending tenant page when add button clicked', async () => {
      renderWithTheme(<PendingTenant />);

      const addButton = screen.getByText('Add Pending Tenant');

      fireEvent.click(addButton);

      // Fix: The actual route is '/add-pending-tenant' not '/pending-tenants/add'
      expect(mockNavigate).toHaveBeenCalledWith('/add-pending-tenant');
    });

    it('opens filter when filter button is clicked', async () => {
      renderWithTheme(<PendingTenant />);

      // Filter button is icon-only, find it by getting all buttons and selecting the one with filter icon
      const buttons = screen.getAllByRole('button');
      const filterButton =
        buttons.find(button => {
          const img = button.querySelector('img[src="filter-icon.svg"]');
          return img !== null;
        }) || buttons[1]; // Fallback to second button

      fireEvent.click(filterButton);

      // Filter should open and show the filter chips component
      await waitFor(() => {
        // Use getAllByText to handle multiple "Status" elements
        const statusElements = screen.getAllByText('Status');
        expect(statusElements.length).toBeGreaterThan(0);
      });
    });
  });

  describe('Filter Functionality - Real World Testing', () => {
    const getFilterButton = () => {
      // Find filter button by getting all buttons and selecting the one with filter icon
      const buttons = screen.getAllByRole('button');
      return (
        buttons.find(button => {
          const img = button.querySelector('img[src="filter-icon.svg"]');
          return img !== null;
        }) || buttons[1]
      ); // Fallback to second button
    };

    it('renders filter chips when filter is opened', async () => {
      renderWithTheme(<PendingTenant />);

      // Open filter using the correct selector
      const filterButton = getFilterButton();
      fireEvent.click(filterButton);

      // Wait for filter to open and render
      await waitFor(() => {
        // Use getAllByText to handle multiple "Status" elements (table header + filter)
        const statusElements = screen.getAllByText('Status');
        expect(statusElements.length).toBeGreaterThan(0);
        // Use getAllByText for status chips that may appear multiple times
        expect(screen.getAllByText('Pending').length).toBeGreaterThan(0);
        expect(screen.getAllByText('Converted').length).toBeGreaterThan(0);
        expect(screen.getAllByText('Invalid').length).toBeGreaterThan(0);
      });
    });

    it('handles filter chip selection', async () => {
      renderWithTheme(<PendingTenant />);

      // Open filter using the correct selector
      const filterButton = getFilterButton();
      fireEvent.click(filterButton);

      // Wait for filter to open
      await waitFor(() => {
        // Use getAllByText to handle multiple "Status" elements
        const statusElements = screen.getAllByText('Status');
        expect(statusElements.length).toBeGreaterThan(0);
      });

      // Click on Pending status chip (get the first one from filter, not table)
      const pendingChips = screen.getAllByText('Pending');
      const filterPendingChip = pendingChips.find(chip => chip.closest('[role="button"]') !== null) || pendingChips[0];

      fireEvent.click(filterPendingChip);

      // Verify the chip is still present (this would depend on the actual FilterChip implementation)
      expect(screen.getAllByText('Pending').length).toBeGreaterThan(0);
    });

    it('handles filter loading state', async () => {
      mockGetAllLeadStatusesQuery.mockReturnValue({
        data: null,
        isLoading: true,
        error: null,
      });

      renderWithTheme(<PendingTenant />);

      // Open filter using the correct selector
      const filterButton = getFilterButton();
      fireEvent.click(filterButton);

      // Should show loading spinner in filter
      await waitFor(() => {
        expect(screen.getByRole('progressbar')).toBeInTheDocument();
      });
    });

    it('handles filter error state', async () => {
      mockGetAllLeadStatusesQuery.mockReturnValue({
        data: null,
        isLoading: false,
        error: {message: 'Failed to load statuses'},
      });

      renderWithTheme(<PendingTenant />);

      // Open filter using the correct selector
      const filterButton = getFilterButton();
      fireEvent.click(filterButton);

      // Should show error message
      await waitFor(() => {
        expect(screen.getByText('Something Went Wrong')).toBeInTheDocument();
      });
    });

    it('closes filter when clicking outside or close button', async () => {
      renderWithTheme(<PendingTenant />);

      // Open filter using the correct selector
      const filterButton = getFilterButton();
      fireEvent.click(filterButton);

      await waitFor(() => {
        // Use getAllByText to handle multiple "Status" elements
        const statusElements = screen.getAllByText('Status');
        expect(statusElements.length).toBeGreaterThan(0);
      });

      // Close filter (this would depend on the actual Filter component implementation)
      // For now, just verify it opened successfully
      const statusElements = screen.getAllByText('Status');
      expect(statusElements.length).toBeGreaterThan(0);
    });
  });

  describe('ActionButtons Component - Real World Testing', () => {
    it('renders all action buttons for pending status', () => {
      const row = {row: {original: {id: '123', status: LeadStatus.PENDING}}};

      renderWithTheme(<ActionButtons row={row as any} refetchLeads={() => {}} />);

      expect(screen.getByTestId('eye-icon')).toBeInTheDocument();
      expect(screen.getByTestId('convert-icon')).toBeInTheDocument();
      expect(screen.getByTestId('invalid-icon')).toBeInTheDocument();
    });

    it('navigates to detail page when view button is clicked', () => {
      const row = {row: {original: {id: '123', status: LeadStatus.PENDING}}};

      renderWithTheme(<ActionButtons row={row as any} refetchLeads={() => {}} />);

      fireEvent.click(screen.getByTestId('eye-icon'));

      expect(mockNavigate).toHaveBeenCalledWith('/pending-tenants/123');
    });

    it('applies correct styles for different statuses', () => {
      const pendingRow = {row: {original: {id: '123', status: LeadStatus.PENDING}}};
      const convertedRow = {row: {original: {id: '456', status: LeadStatus.CONVERTED}}};

      const {rerender} = renderWithTheme(<ActionButtons row={pendingRow as any} refetchLeads={() => {}} />);

      // Check pending status - our mock doesn't apply real styles, so just verify it renders
      const viewIcon = screen.getByTestId('eye-icon');
      expect(viewIcon).toBeInTheDocument();

      // Rerender with converted status
      rerender(
        <ThemeProvider theme={robustTheme}>
          <ActionButtons row={convertedRow as any} refetchLeads={() => {}} />
        </ThemeProvider>,
      );

      // Check converted status - verify it still renders (our mock doesn't apply real styles)
      const disabledViewIcon = screen.getByTestId('eye-icon');
      expect(disabledViewIcon).toBeInTheDocument();

      // The real test is that actionStyles function returns correct values (tested separately)
    });
    // E2E: Clicking ConvertCheckIcon opens dialog and handles confirm/cancel
    it('opens InvalidDialog and handles convert action for ConvertCheckIcon', async () => {
      const row = {
        row: {
          original: {
            id: '123',
            status: LeadStatus.PENDING,
            firstName: 'John',
            lastName: 'Doe',
            companyName: 'Test Company',
            email: '<EMAIL>',
            designation: 'CEO',
            phoneNumber: '**********',
            countryCode: '+1',
            address: {city: 'New York', state: 'NY'},
          },
        },
      };
      const mockRefetchLeads = vi.fn();
      // Use global mockNavigate instead of redefining

      renderWithTheme(<ActionButtons row={row as any} refetchLeads={mockRefetchLeads} />);
      fireEvent.click(screen.getByTestId('convert-icon'));
      await waitFor(() => expect(screen.getByRole('dialog')).toBeInTheDocument());

      // Cancel
      fireEvent.click(screen.getByText('Cancel'));
      await waitFor(() => expect(screen.queryByRole('dialog')).not.toBeInTheDocument());

      // Re-open and confirm
      fireEvent.click(screen.getByTestId('convert-icon'));
      await waitFor(() => expect(screen.getByRole('dialog')).toBeInTheDocument());
      fireEvent.click(screen.getByTestId('dialog-invalid-button'));
      await waitFor(() =>
        expect(mockNavigate).toHaveBeenCalledWith(
          expect.anything(),
          expect.objectContaining({state: expect.any(Object)}),
        ),
      );
    });

    // E2E: Clicking InvalidIcon opens dialog and handles confirm/cancel
    it('opens InvalidDialog and handles invalid action for InvalidIcon', async () => {
      const row = {
        row: {
          original: {
            id: '123',
            status: LeadStatus.PENDING,
            firstName: 'John',
            lastName: 'Doe',
            companyName: 'Test Company',
            email: '<EMAIL>',
            designation: 'CEO',
            phoneNumber: '**********',
            countryCode: '+1',
            address: {city: 'New York', state: 'NY'},
          },
        },
      };
      const mockRefetchLeads = vi.fn();
      // Use global mockUpdateStatus
      mockUpdateStatus.mockResolvedValue({error: undefined});

      renderWithTheme(<ActionButtons row={row as any} refetchLeads={mockRefetchLeads} />);
      fireEvent.click(screen.getByTestId('invalid-icon'));
      await waitFor(() => expect(screen.getByRole('dialog')).toBeInTheDocument());

      // Cancel
      fireEvent.click(screen.getByText('Cancel'));
      await waitFor(() => expect(screen.queryByRole('dialog')).not.toBeInTheDocument());

      // Re-open and confirm
      fireEvent.click(screen.getByTestId('invalid-icon'));
      await waitFor(() => expect(screen.getByRole('dialog')).toBeInTheDocument());
      fireEvent.click(screen.getByTestId('dialog-invalid-button'));
      await waitFor(() => expect(mockUpdateStatus).toHaveBeenCalledWith({leadId: '123', status: LeadStatus.INVALID}));
      await waitFor(() => expect(mockRefetchLeads).toHaveBeenCalled());
    });
  });

  describe('ActionStyles Utility Function', () => {
    it('returns correct styles for view icon', () => {
      const styles = actionStyles(LeadStatus.PENDING, 'view');

      expect(styles).toEqual({
        color: 'body.500',
        fontSize: '1.25rem',
        fill: 'body.500',
        mr: 1.5,
        cursor: 'pointer',
      });
    });

    it('returns disabled styles for converted status', () => {
      const styles = actionStyles(LeadStatus.CONVERTED);

      expect(styles).toEqual({
        color: 'body.200',
        fontSize: '1.25rem',
        fill: 'body.200',
        mr: 1.5,
        cursor: 'not-allowed',
      });
    });

    it('returns disabled styles for invalid status', () => {
      const styles = actionStyles(LeadStatus.INVALID);

      expect(styles).toEqual({
        color: 'body.200',
        fontSize: '1.25rem',
        fill: 'body.200',
        mr: 1.5,
        cursor: 'not-allowed',
      });
    });

    it('returns active styles for pending status', () => {
      const styles = actionStyles(LeadStatus.PENDING);

      expect(styles).toEqual({
        color: 'body.500',
        fontSize: '1.25rem',
        fill: 'body.500',
        mr: 1.5,
        cursor: 'pointer',
      });
    });
  });

  describe('API Integration', () => {
    it('calls API with correct parameters on component mount', () => {
      renderWithTheme(<PendingTenant />);

      expect(mockGetLeadsQuery).toHaveBeenCalled();
      expect(mockGetLeadsCountQuery).toHaveBeenCalled();
      // Note: mockGetAllLeadStatusesQuery is only called when filter is opened, not on mount
    });

    it('refetches data when search term changes', async () => {
      renderWithTheme(<PendingTenant />);

      const searchInput = screen.getByPlaceholderText('Search pending tenants');

      // Clear previous calls
      vi.clearAllMocks();

      // Change search term
      fireEvent.change(searchInput, {target: {value: 'test search'}});

      // Should trigger new API calls (debounced)
      await waitFor(
        () => {
          expect(mockGetLeadsQuery).toHaveBeenCalled();
        },
        {timeout: 1000},
      );
    });
  });
});
