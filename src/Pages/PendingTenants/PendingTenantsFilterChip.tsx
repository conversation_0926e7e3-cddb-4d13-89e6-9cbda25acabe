import {Box, CircularProgress, Typography} from '@mui/material';
import {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {useGetAllLeadStatusesQuery} from 'redux/app/leadManagementApiSlice';
import FilterChip from '../../Components/FilterChip/FilterChip';
import FilterSectionView from '../../Components/FilterChip/FilterSectionView';
import {IClearFilters} from '../../Components/FilterUtil/IClearFilters';
import {getStatusLabel} from './pendingTenants.utils';

export interface FilterStatusChipsProps {
  value?: Set<string>;
  onSelect?: (selected: Set<string>) => void;
}

export type FilterStatusChipsRef = IClearFilters;

const FilterStatusChips = forwardRef<FilterStatusChipsRef, FilterStatusChipsProps>(({onSelect, value}, ref) => {
  const {data, isLoading, error} = useGetAllLeadStatusesQuery({});
  const [selectedStatus, setSelectedStatus] = useState<Set<string>>(value ? new Set(value) : new Set());

  useEffect(() => {
    if (onSelect) {
      onSelect(new Set(selectedStatus));
    }
  }, [selectedStatus, onSelect]);

  useImperativeHandle(ref, () => ({
    clearSelection: () => {
      setSelectedStatus(new Set());
    },
  }));

  if (isLoading) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height={40}>
        <CircularProgress size={24} />
      </Box>
    );
  }
  if (error) {
    return <Typography>Something Went Wrong</Typography>;
  }
  return (
    <FilterSectionView title="Status">
      <Box sx={{display: 'flex', flexDirection: 'row', gap: 0.75, flexWrap: 'wrap', textTransform: 'capitalize'}}>
        {Object.entries(data?.statuses ?? {}).map(([key]) => {
          return (
            <FilterChip
              key={key}
              onClick={() => {
                if (!selectedStatus.has(key)) {
                  selectedStatus.add(key);
                }
                setSelectedStatus(new Set(selectedStatus));
              }}
              label={getStatusLabel(Number(key))}
              selected={selectedStatus.has(key)}
              onDelete={() => {
                selectedStatus.delete(key);
                setSelectedStatus(new Set(selectedStatus));
              }}
            />
          );
        })}
      </Box>
    </FilterSectionView>
  );
});
FilterStatusChips.displayName = 'FilterStatusChips';

export default FilterStatusChips;
