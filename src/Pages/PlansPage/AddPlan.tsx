import {Box, Grid, Stack, Typography} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader/BackdropLoader';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import Form from 'Components/Forms/Form';
import {useSnackbar} from 'notistack';
import React from 'react';
import {useNavigate} from 'react-router-dom';
import {
  useCreatePlanMutation,
  useGetCurrenciesQuery,
  useGetDevicesQuery,
  useGetTenuresQuery,
} from 'redux/app/planManagementAPiSlice';
import {IBillingCycle, IConfigureDevice, PlanResponse} from 'redux/app/types/plan.type';
import {gridContainerStyle} from 'styles/pages/AddPlan.styles';
import * as yup from 'yup';
import PlanFormButtons from './PlanFormButtons';
import PlanFormFields from './PlanFormFields';
import {capitalize} from './plans.utils';

const breadcrumbItems = [
  {label: 'Plans', url: '/plans'},
  {label: 'Add Plan', url: '/add-plan'},
];
const editBreadCrumbItems = [
  {label: 'Plans', url: '/plans'},
  {label: 'Edit Plan', url: '/edit-plan'},
];

type Props = {
  isEdit?: boolean;
  initialValues?: FormAddPlan; // <-- NEW
  onSubmit?: (values: FormAddPlan) => Promise<void>; // <-- NEW
  isUpdating?: boolean; // <-- NEW
};

const minNameLen = 3;
const maxNameLen = 50;

// Validation schema for the form
const addPlanValidationSchema = yup.object().shape({
  name: yup
    .string()
    .required('Plan name is required')
    .min(minNameLen, 'Plan name should have at least 3 characters')
    .max(maxNameLen, 'Plan name should have at most 50 characters')
    .matches(/^[A-Za-z]+(?: [A-Za-z]+)*$/, 'Plan name should only contain letters'),
  price: yup
    .number()
    .typeError('Price must be a number')
    .required('Price is required')
    .moreThan(0, 'Price must be greater than zero')
    .test(
      'is-decimal',
      'Price can have up to 2 decimal places',
      value => value === undefined || /^\d+(\.\d{1,2})?$/.test(value.toString()),
    ),
  configureDeviceId: yup.string().required('No. of devices is required'),
  tier: yup.string().required('Infra configuration is required'),
  billingCycleId: yup.string().required('Subscription tenure is required'),
  allowedUnlimitedUsers: yup.boolean().required(),
  costPerUser: yup
    .number()
    .nullable()
    .transform((_, val) => (val === '' ? null : Number(val)))
    .when('allowedUnlimitedUsers', {
      is: false,
      then: schema =>
        schema
          .required('Cost per user is required')
          .moreThan(0, 'Cost per user must be greater than zero')
          .test(
            'is-decimal',
            'Cost per user can have up to 2 decimal places',
            value => value === null || /^\d+(\.\d{1,2})?$/.test(value.toString()),
          )
          .typeError('Cost per user must be a number'),
      otherwise: schema => schema.notRequired(),
    }),
});

// Initial form values
const initialValues = {
  name: '',
  price: 0,
  configureDeviceId: '',
  tier: '',
  billingCycleId: '',
  costPerUser: undefined,
  allowedUnlimitedUsers: false,
};

export interface FormAddPlan {
  id?: string;
  name: string;
  price: number;
  configureDeviceId: string;
  tier: string;
  billingCycleId: string;
  costPerUser?: number | null;
  allowedUnlimitedUsers: boolean;
}

export interface ICurrencies {
  id: string;
  currencyCode: string;
  currencyName: string;
  symbol: string;
  country: string;
}

/**
 * Renders an error message box displaying which required data failed to fetch.
 *
 * @param params - An object containing possible error values for devices, tenures, and currencies.
 * @param params.devicesError - Optional error object for device fetching failure.
 * @param params.tenuresError - Optional error object for tenure fetching failure.
 * @param params.currenciesError - Optional error object for currency fetching failure.
 * @returns A React element displaying a summary of the fetch errors.
 */
const renderError = ({
  devicesError,
  tenuresError,
  currenciesError,
}: {
  devicesError?: unknown;
  tenuresError?: unknown;
  currenciesError?: unknown;
}) => {
  return (
    <Box data-testid="AddPlanError" sx={{p: 4, textAlign: 'center'}}>
      <Typography variant="h6" color="error" gutterBottom>
        Failed to fetch required data.
      </Typography>
      <Typography variant="body2" color="body.500">
        {devicesError ? 'Error getting devices. ' : ''}
        {tenuresError ? 'Error getting tenures. ' : ''}
        {currenciesError ? 'Error getting currencies.' : ''}
      </Typography>
    </Box>
  );
};

/**
 * Returns the numeric value of the given cost per user if it is defined and not null.
 * If the input is undefined or null, returns undefined.
 *
 * @param costPerUser - The cost per user value, which can be a number, null, or undefined.
 * @returns The numeric value of costPerUser, or undefined if costPerUser is null or undefined.
 */
const getCostPerUserValue = (costPerUser: number | null | undefined) =>
  costPerUser !== undefined && costPerUser !== null ? Number(costPerUser) : undefined;

function getBreadCrumbItems(isEdit: boolean) {
  if (isEdit) {
    return editBreadCrumbItems;
  }
  return breadcrumbItems;
}

const renderDataState = ({
  devicesLoading,
  tenuresLoading,
  currenciesLoading,
  devicesError,
  tenuresError,
  currenciesError,
}: {
  devicesLoading: boolean;
  tenuresLoading: boolean;
  currenciesLoading: boolean;
  devicesError: unknown;
  tenuresError: unknown;
  currenciesError: unknown;
}) => {
  if (devicesLoading || tenuresLoading || currenciesLoading) {
    return <BackdropLoader />;
  }

  if (devicesError || tenuresError || currenciesError) {
    return renderError({devicesError, tenuresError, currenciesError});
  }

  return null;
};

/**
 * AddPlan component for creating or editing a plan.
 *
 * This component fetches required data (devices, tenures, currencies) and renders a form
 * for adding a new plan or editing an existing one. It handles form submission, validation,
 * and navigation. Displays loading and error states as appropriate.
 *
 * @param {Props} props - The component props.
 * @param {boolean} [props.isEdit=false] - Determines if the form is in edit mode.
 *
 * @returns {JSX.Element} The rendered AddPlan page.
 */
const AddPlan: React.FC<Props> = ({isEdit = false, initialValues: injectedValues, onSubmit, isUpdating = false}) => {
  const {data: devicesData, isLoading: devicesLoading, error: devicesError} = useGetDevicesQuery({order: 'min asc'});
  const {
    data: tenuresData,
    isLoading: tenuresLoading,
    error: tenuresError,
  } = useGetTenuresQuery({order: 'duration asc'});
  const {data: currenciesData, isLoading: currenciesLoading, error: currenciesError} = useGetCurrenciesQuery({});
  const navigate = useNavigate();

  const {enqueueSnackbar} = useSnackbar();

  const [createPlan, {isLoading: isCreating}] = useCreatePlanMutation();

  const currencyCodeOptions: ICurrencies =
    currenciesData?.filter((currency: ICurrencies) => currency.symbol === '$')[0] ?? {};

  const handleCancel = () => {
    navigate('/plans');
  };

  const handleSubmit = async (values: FormAddPlan) => {
    const formValues = {
      ...values,
      price: Number(values.price),
      costPerUser: getCostPerUserValue(values.costPerUser),
      currencyId: currencyCodeOptions.id,
    };

    if (values.allowedUnlimitedUsers) {
      delete formValues.costPerUser;
    }

    try {
      await createPlan(formValues).unwrap();
      enqueueSnackbar('Plan added successfully!', {variant: 'success', subMessage: values.name});
      navigate('/plans');
    } catch {
      // Handle error block
    }
  };

  const deviceOptions =
    devicesData?.map((device: IConfigureDevice) => ({
      value: device.id,
      label: `${device.min} - ${device.max}`,
    })) ?? [];

  const tenureOptions =
    tenuresData?.map((tenures: IBillingCycle) => ({
      value: tenures.id,
      label: capitalize(tenures.cycleName),
    })) ?? [];

  const dataState = renderDataState({
    devicesLoading,
    tenuresLoading,
    currenciesLoading,
    devicesError,
    tenuresError,
    currenciesError,
  });

  if (dataState) return dataState;

  return (
    <Box data-testid="AddPlanPage">
      <Grid container>
        <Grid size={12}>
          <Stack spacing={2}>
            <Breadcrumb items={getBreadCrumbItems(isEdit)} separator="|" showHeader />
          </Stack>
        </Grid>

        <Grid sx={gridContainerStyle}>
          <Form<FormAddPlan>
            initialValues={{...initialValues, ...injectedValues}}
            validationSchema={addPlanValidationSchema}
            onSubmit={onSubmit ?? handleSubmit}
            validateOnChange={true}
            validateOnBlur={true}
          >
            <Grid container size={12} spacing={2} rowSpacing={2} sx={{padding: 2, paddingBottom: 12}}>
              <PlanFormFields
                isEdit={isEdit}
                injectedValues={injectedValues as unknown as PlanResponse}
                deviceOptions={deviceOptions}
                tenureOptions={tenureOptions}
                currencyCodeOptions={currencyCodeOptions}
              />
            </Grid>
            <Grid container size={12} spacing={2} rowSpacing={2} sx={{margin: 0, padding: 0}}>
              <PlanFormButtons isEdit={isEdit} isLoading={isEdit ? isUpdating : isCreating} onCancel={handleCancel} />
            </Grid>
          </Form>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AddPlan;
