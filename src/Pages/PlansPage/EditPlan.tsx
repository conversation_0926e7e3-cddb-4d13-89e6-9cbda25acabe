import {Box} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader';
import {useSnackbar} from 'notistack';
import React, {useEffect, useState} from 'react';
import {useNavigate, useParams} from 'react-router-dom';
import {useGetPlanByIdQuery, useUpdatePlanByIdMutation} from 'redux/app/tenantManagementApiSlice';
import AddPlan, {FormAddPlan} from './AddPlan';

const EditPlan: React.FC = () => {
  const {id} = useParams<{id: string}>();
  const navigate = useNavigate();
  const {enqueueSnackbar} = useSnackbar();
  const [updatePlanById, {isLoading: isUpdating}] = useUpdatePlanByIdMutation();

  const {
    data: planData,
    isLoading: planLoading,
    error: planError,
    refetch,
  } = useGetPlanByIdQuery({
    planId: id || '',
    filter: {
      include: [
        {relation: 'billingCycle'},
        {
          relation: 'planHistories',
          scope: {
            order: 'createdOn DESC',
          },
        },
        {relation: 'currency'},
        {relation: 'planSize'},
        {relation: 'configureDevice'},
      ],
    },
  });

  const [initialValues, setInitialValues] = useState<FormAddPlan | null>(null);

  useEffect(() => {
    if (planData) {
      setInitialValues(planData);
    }
  }, [planData]);

  const handleSubmit = async (values: FormAddPlan) => {
    try {
      await updatePlanById({
        planID: values.id ?? '',
        data: {
          price: Number(values.price),
          costPerUser: values.allowedUnlimitedUsers ? undefined : Number(values.costPerUser),
          allowedUnlimitedUsers: values.allowedUnlimitedUsers,
        },
      }).unwrap();

      enqueueSnackbar('Plan updated successfully!', {variant: 'success', subMessage: values.name});
      navigate('/plans');

      await refetch();
    } catch {
      enqueueSnackbar('Failed to update plan', {variant: 'error'});
    }
  };

  if (planError) {
    return (
      <Box p={4} textAlign="center" color="error.main">
        Failed to load plan details.
      </Box>
    );
  }

  if (planLoading || !initialValues) {
    return <BackdropLoader />;
  }

  return (
    <AddPlan
      isEdit={true}
      {...{
        initialValues: initialValues,
        onSubmit: handleSubmit,
        isUpdating: isUpdating,
      }}
    />
  );
};

export default EditPlan;
