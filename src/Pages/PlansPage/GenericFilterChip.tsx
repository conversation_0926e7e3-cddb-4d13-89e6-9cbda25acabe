import {Box, CircularProgress} from '@mui/material';
import {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import FilterChip from '../../Components/FilterChip/FilterChip';
import FilterSectionView from '../../Components/FilterChip/FilterSectionView';
import {IClearFilters} from '../../Components/FilterUtil/IClearFilters';

export interface GenericFilterChipProps<T> {
  title: string;
  data: Record<string, T>;
  value?: Set<string>;
  onSelect?: (selected: Set<string>) => void;
  getKey: (item: T, key: string) => string;
  getLabel: (item: T, key: string) => string;
}

export type GenericFilterChipRef = IClearFilters;

/**
 * A generic filter chip component that displays a set of selectable chips based on provided data.
 *
 * @template T - The type of the data items.
 * @param props - The properties for the filter chip component.
 * @param props.title - The title to display above the filter chips.
 * @param props.data - The data object containing items to display as filter chips.
 * @param props.value - The initial selected values as an array of strings.
 * @param props.onSelect - Callback invoked when the selection changes, receiving the new set of selected keys.
 * @param props.getKey - Function to extract a unique key for each item.
 * @param props.getLabel - Function to extract the display label for each item.
 * @param ref - A ref object to expose imperative methods (e.g., clearSelection).
 *
 * @returns A React element rendering the filter chips and handling selection logic.
 */
function GenericFilterChipInner<T>(
  {title, data, value, onSelect, getKey, getLabel}: GenericFilterChipProps<T>,
  ref: React.Ref<GenericFilterChipRef>,
) {
  const [selected, setSelected] = useState<Set<string>>(value ? new Set(value) : new Set());

  useEffect(() => {
    if (onSelect) {
      onSelect(new Set(selected));
    }
  }, [selected, onSelect]);

  useImperativeHandle(ref, () => ({
    clearSelection: () => {
      setSelected(new Set());
    },
  }));

  if (!data) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height={40}>
        <CircularProgress size={24} />
      </Box>
    );
  }

  return (
    <FilterSectionView title={title}>
      <Box sx={{display: 'flex', flexDirection: 'row', gap: 0.75, flexWrap: 'wrap', textTransform: 'capitalize'}}>
        {Object.entries(data).map(([key, item]) => {
          const itemKey = getKey(item, key);
          return (
            <FilterChip
              key={itemKey}
              onClick={() => {
                if (!selected.has(itemKey)) {
                  selected.add(itemKey);
                }
                setSelected(new Set(selected));
              }}
              label={getLabel(item, key)}
              selected={selected.has(itemKey)}
              onDelete={() => {
                selected.delete(itemKey);
                setSelected(new Set(selected));
              }}
            />
          );
        })}
      </Box>
    </FilterSectionView>
  );
}

const GenericFilterChip = forwardRef(GenericFilterChipInner) as unknown as (<T>(
  props: GenericFilterChipProps<T> & {ref?: React.Ref<GenericFilterChipRef>},
) => ReturnType<typeof GenericFilterChipInner>) & {displayName?: string};

GenericFilterChip.displayName = 'GenericFilterChip';

export default GenericFilterChip;
