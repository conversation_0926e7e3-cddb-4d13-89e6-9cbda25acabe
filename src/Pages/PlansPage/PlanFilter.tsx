import {Box, Divider, IconButton, Typography} from '@mui/material';
import {PopoverWithArrow} from 'Components/AppBar/PopoverWithArrow';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import DefaultCloseIcon from 'Components/DefaultDialog/DefaultCloseIcon';
import {useCallback, useEffect, useRef, useState} from 'react';
import FilterStatusChips, {FilterStatusChipsRef} from './StatusFilterChip';
import TenureChips, {FilterTenureChipsRef} from './TenureFilterChip';

export interface IPlanFilter {
  status: Set<string>;
  tenure: Set<string>;
}

interface PlanFilterProps {
  open: boolean;
  onClose: () => void;
  value?: IPlanFilter;
  anchorEl?: HTMLElement | null;
  onFilterChange?: (filter: IPlanFilter) => void;
}

const paddingDefault = 2; // Default padding for the popover
const buttonHeight = '2.3125rem'; // Height for the buttons

/**
 * PlanFilter component provides a popover UI for filtering plans by status and tenure.
 *
 * @component
 * @param {PlanFilterProps} props - The props for the PlanFilter component.
 * @param {boolean} props.open - Controls whether the filter popover is open.
 * @param {PlanFilterValue} [props.value] - The current filter values for status and tenure.
 * @param {HTMLElement | null} props.anchorEl - The element to anchor the popover to.
 * @param {(value: PlanFilterValue) => void} [props.onFilterChange] - Callback invoked when filter values are applied.
 * @param {() => void} props.onClose - Callback invoked when the popover is closed.
 *
 * @remarks
 * - The component manages internal state for selected status and tenure filters.
 * - Provides "Apply", "Close", and "Clear all" actions for user interaction.
 * - Uses refs to control child chip components for clearing selections.
 *
 * @returns {JSX.Element} The rendered PlanFilter popover component.
 */
const PlanFilter: React.FC<PlanFilterProps> = props => {
  const {open, value: filter, anchorEl, onFilterChange, onClose} = props;
  const [selectedStatus, setSelectedStatus] = useState<Set<string>>(filter?.status ?? new Set());
  const [selectedTenure, setSelectedTenure] = useState<Set<string>>(filter?.tenure ?? new Set());
  const filterStatusChipsRef = useRef<FilterStatusChipsRef>(null);
  const filterTenureChipsRef = useRef<FilterTenureChipsRef>(null);

  useEffect(() => {
    if (open) {
      // Reset selections when the filter is opened
      setSelectedStatus(filter?.status ?? new Set());
      setSelectedTenure(filter?.tenure ?? new Set());
    }
  }, [open]);

  const applyHandle = () => {
    onFilterChange?.({
      status: new Set(selectedStatus),
      tenure: new Set(selectedTenure),
    });
    onClose();
  };

  const handleClearSelection = useCallback(() => {
    filterStatusChipsRef.current?.clearSelection();
    filterTenureChipsRef.current?.clearSelection();
  }, []);

  const buildButtonSection = () => {
    return (
      <Box sx={{display: 'flex', justifyContent: 'flex-end', p: paddingDefault, pt: 1, gap: 1}}>
        <BorderButton sx={{height: buttonHeight}} onClick={onClose}>
          Close
        </BorderButton>
        <BlueButton onClick={applyHandle} sx={{height: buttonHeight}}>
          Apply
        </BlueButton>
      </Box>
    );
  };
  const buildHeaderSection = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: paddingDefault,
          pb: 0,
        }}
      >
        <Typography>Filter</Typography>

        {showClearButton && (
          <Box
            onClick={handleClearSelection}
            sx={{
              ml: 'auto',
              p: 0,
              backgroundColor: 'transparent',
              color: 'secondary.main',
              fontWeight: 600,
              fontSize: '0.75rem',
              cursor: 'pointer',
              textDecoration: ' underline',
            }}
          >
            Clear all
          </Box>
        )}

        <IconButton onClick={onClose} size="small" sx={{padding: 0}}>
          <DefaultCloseIcon />
        </IconButton>
      </Box>
    );
  };

  const showClearButton = selectedStatus.size + selectedTenure.size > 0;

  return (
    <>
      {/* Popper sits above backdrop */}
      <PopoverWithArrow
        id="plan-filter-popper"
        disablePortal={false}
        elevation={2}
        open={open}
        anchorEl={anchorEl}
        transformHOrigin={{
          horizontal: 'center',
        }}
        onClose={onClose}
      >
        <Box sx={{display: 'flex', flexDirection: 'column', gap: 1, minWidth: 220, maxWidth: 350}}>
          {buildHeaderSection()}
          <Divider />
          <Box sx={{padding: paddingDefault, pt: 0, display: 'flex', flexDirection: 'column', gap: 1}}>
            <FilterStatusChips ref={filterStatusChipsRef} onSelect={setSelectedStatus} value={selectedStatus} />
            <TenureChips ref={filterTenureChipsRef} onSelect={setSelectedTenure} value={selectedTenure} />
          </Box>
          <Divider />
          {buildButtonSection()}
        </Box>
      </PopoverWithArrow>
    </>
  );
};

export default PlanFilter;
