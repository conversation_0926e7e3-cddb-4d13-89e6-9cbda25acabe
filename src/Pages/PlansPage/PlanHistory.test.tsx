// PlanHistory.test.tsx
import '@testing-library/jest-dom';
import {act, render, screen} from '@testing-library/react';
import {afterEach, describe, expect, it, vi} from 'vitest';

vi.mock('./PlanHistoryCard', () => {
  const MockCard = (props: any) => <div data-testid="plan-card" data-props={JSON.stringify(props)} />;

  return {
    PlanHistoryCardCard: MockCard,
    PlanHistoryCard: MockCard,
    default: MockCard,
  };
});

import PlanHistory, {withFlags} from './PlanHistory';

// --- Unit tests for helpers ---

describe('withFlags and usersChanged', () => {
  const baseList = [
    {
      name: 'A',
      numberOfDevices: '1',
      infraConfigurations: '',
      tenure: '',
      price: '10',
      costPerUser: '2',
      allowedUnlimited: false,
      version: '1',
      createdAt: 't1',
      createdBy: 'u1',
    },
    {
      name: 'A',
      numberOfDevices: '1',
      infraConfigurations: '',
      tenure: '',
      price: '20',
      costPerUser: '3',
      allowedUnlimited: false,
      version: '2',
      createdAt: 't2',
      createdBy: 'u2',
    },
    {
      name: 'A',
      numberOfDevices: '1',
      infraConfigurations: '',
      tenure: '',
      price: '20',
      costPerUser: '3',
      allowedUnlimited: true,
      version: '3',
      createdAt: 't3',
      createdBy: 'u3',
    },
  ];

  it('flags price and users changes correctly', () => {
    const flagged = withFlags(baseList, baseList);
    expect(flagged[0]._priceChanged).toBe(true); // 10 -> 20
    expect(flagged[1]._priceChanged).toBe(false); // 20 -> 20
    expect(flagged[2]._priceChanged).toBe(false); // last

    expect(flagged[0]._usersChanged).toBe(true); // both limited, costPerUser differs (should be true per impl)
    expect(flagged[1]._usersChanged).toBe(true); // allowedUnlimited changes
    expect(flagged[2]._usersChanged).toBe(false); // last
  });

  it('usersChanged returns false if next is undefined', () => {
    // last item
    const flagged = withFlags([baseList[2]], baseList);
    expect(flagged[0]._usersChanged).toBe(true); // allowedUnlimited differs from next (undefined), so true
  });
});

afterEach(() => {
  vi.clearAllMocks();
});

describe('PlanHistory (independent)', () => {
  it('renders one PlanHistoryCardCard with default/empty values when injectedValues is undefined', () => {
    render(<PlanHistory />);

    const cards = screen.getAllByTestId('plan-card');
    expect(cards).toHaveLength(1);

    const propsAttr = cards[0].getAttribute('data-props') || '{}';
    const props = JSON.parse(propsAttr);

    expect(props).toHaveProperty('data');
    expect(props.data).toMatchObject({
      name: '',
      numberOfDevices: '-',
      infraConfigurations: '',
      tenure: '',
      price: '',
      costPerUser: '',
      allowedUnlimited: false,
      version: '',
      createdAt: '',
      createdBy: '',
    });

    expect(props.updatedBy).toBe('');
    expect(props.index).toBe(0);
  });

  it('renders 1 + planHistories.length cards and maps fields correctly', () => {
    const injectedValues = {
      name: 'Pro Plan',
      configureDevice: {min: 2, max: 5},
      tier: 'cloud',
      billingCycle: {cycleName: 'MONTHLY'},
      price: 1200,
      costPerUser: 10.5,
      allowedUnlimitedUsers: false,
      version: 'v2.0',
      // prefer modifiedOn if present
      modifiedOn: '2025-07-10T10:00:00.000Z',
      createdOn: '2025-07-01T09:00:00.000Z',
      modifiedBy: 'user-mod',
      createdBy: 'user-create',
      planHistories: [
        {
          price: 900,
          costPerUser: 9,
          allowedUnlimitedUsers: false,
          version: 'v1.5',
          createdOn: '2025-06-01T08:00:00.000Z',
          createdBy: 'hist-user-1',
        },
        {
          price: 700,
          costPerUser: 7,
          allowedUnlimitedUsers: true,
          version: 'v1.0',
          createdOn: '2025-05-01T07:00:00.000Z',
          createdBy: 'hist-user-2',
        },
      ],
    };

    render(<PlanHistory injectedValues={injectedValues as any} />);

    const cards = screen.getAllByTestId('plan-card');
    // 1 current + 2 histories = 3
    expect(cards).toHaveLength(3);

    const parsed = cards.map(c => JSON.parse(c.getAttribute('data-props') || '{}'));

    // First card corresponds to current injectedValues mapping (but createdAt rotated)
    const firstData = parsed[0].data;
    expect(firstData.name).toBe('Pro Plan');
    expect(firstData.numberOfDevices).toBe('2-5'); // `${min}-${max}`
    expect(firstData.infraConfigurations).toBe('cloud');
    expect(firstData.tenure).toBe('MONTHLY');
    expect(firstData.price).toBe(`${injectedValues.price}`); // "1200"
    expect(firstData.costPerUser).toBe(`${injectedValues.costPerUser}`); // "10.5"
    expect(firstData.allowedUnlimited).toBe(false);
    expect(firstData.version).toBe(injectedValues.version);
    // createdAt is rotated: should now be first history createdOn
    expect(firstData.createdAt).toBe(injectedValues.planHistories[0].createdOn);
    // createdBy should prefer modifiedBy (unchanged)
    expect(firstData.createdBy).toBe(injectedValues.modifiedBy);
    expect(parsed[0].updatedBy).toBe(injectedValues.modifiedBy);
    expect(parsed[0].index).toBe(0);

    // Second card corresponds to first history (but createdAt rotated)
    const secondData = parsed[1].data;
    expect(secondData.price).toBe(`${injectedValues.planHistories[0].price}`); // "900"
    expect(secondData.costPerUser).toBe(`${injectedValues.planHistories[0].costPerUser}`); // "9"
    expect(secondData.allowedUnlimited).toBe(false);
    expect(secondData.version).toBe(injectedValues.planHistories[0].version);
    // createdAt rotated: should now be second history createdOn
    expect(secondData.createdAt).toBe(injectedValues.planHistories[1].createdOn);
    expect(parsed[1].updatedBy).toBe(injectedValues.planHistories[0].createdBy);
    expect(parsed[1].index).toBe(1);

    // Third card corresponds to second history (which had allowedUnlimited true)
    const thirdData = parsed[2].data;
    expect(thirdData.price).toBe(`${injectedValues.planHistories[1].price}`); // "700"
    expect(thirdData.costPerUser).toBe(`${injectedValues.planHistories[1].costPerUser}`); // "7"
    expect(thirdData.allowedUnlimited).toBe(true);
    expect(thirdData.version).toBe(injectedValues.planHistories[1].version);
    // createdAt rotated: last item receives head's original createdAt (modifiedOn)
    expect(thirdData.createdAt).toBe(injectedValues.modifiedOn);
    expect(parsed[2].updatedBy).toBe(injectedValues.planHistories[1].createdBy);
    expect(parsed[2].index).toBe(2);
  });
});

describe('PlanHistory pagination and loading UI', () => {
  function makeBasePlan(idx = 0) {
    return {
      name: `Plan${idx}`,
      configureDevice: {min: 1, max: 2},
      tier: 'cloud',
      billingCycle: {cycleName: 'MONTHLY'},
      price: 100 + idx,
      costPerUser: 10 + idx,
      allowedUnlimitedUsers: false,
      version: `v${idx}`,
      modifiedOn: `2025-07-10T10:00:00.000Z`,
      createdOn: `2025-07-01T09:00:00.000Z`,
      modifiedBy: `user-mod${idx}`,
      createdBy: `user-create${idx}`,
      planHistories: [],
    };
  }

  it('shows "View more" button and loads more cards (local pagination)', async () => {
    // 15 items: 1 head + 14 histories
    const injectedValues = {
      ...makeBasePlan(0),
      planHistories: Array.from({length: 14}, (_, i) => ({
        price: 200 + i,
        costPerUser: 20 + i,
        allowedUnlimitedUsers: false,
        version: `v${i + 1}`,
        createdOn: `2025-06-${String(i + 1).padStart(2, '0')}T08:00:00.000Z`,
        createdBy: `hist-user-${i + 1}`,
      })),
    };
    render(<PlanHistory injectedValues={injectedValues as any} />);
    // Should show 10 cards initially
    expect(screen.getAllByTestId('plan-card')).toHaveLength(10);
    // Should show "View more" button
    const btn = screen.getByTestId('view-more-button');
    expect(btn).toBeInTheDocument();
    // Click to load more
    await act(async () => {
      btn.click();
    });
    // After click, should show 15 cards (all)
    const cards = await screen.findAllByTestId('plan-card');
    expect(cards).toHaveLength(15);
  });

  it('shows loading spinner in button when loadingMore (remote pagination)', async () => {
    // Use enough items to trigger the "View more" button
    const injectedValues = {
      ...makeBasePlan(0),
      planHistories: Array.from({length: 10}, (_, i) => ({
        price: 200 + i,
        costPerUser: 20 + i,
        allowedUnlimitedUsers: false,
        version: `v${i + 1}`,
        createdOn: `2025-06-${String(i + 1).padStart(2, '0')}T08:00:00.000Z`,
        createdBy: `hist-user-${i + 1}`,
      })),
    };
    let resolveFetch: ((value: any[]) => void) | undefined;
    const fetchMore = vi.fn(
      () =>
        new Promise<any[]>(res => {
          resolveFetch = res;
        }),
    ) as unknown as (offset: number, limit: number) => Promise<any[]>;
    render(<PlanHistory injectedValues={injectedValues as any} fetchMore={fetchMore} />);
    // Should show "View more" button
    const btn = screen.getByTestId('view-more-button');
    await act(async () => {
      btn.click();
    });
    // Button should show loading spinner
    expect(screen.getByText(/Loading more/i)).toBeInTheDocument();
    // Complete fetchMore
    await act(async () => {
      resolveFetch!([
        {
          name: 'RemotePlan',
          numberOfDevices: '1-2',
          infraConfigurations: 'cloud',
          tenure: 'MONTHLY',
          price: '999',
          costPerUser: '99',
          allowedUnlimited: false,
          version: 'vRemote',
          createdAt: '2025-08-01T08:00:00.000Z',
          createdBy: 'remote-user',
        },
      ]);
    });
    // Wait for UI to update
    const cards = await screen.findAllByTestId('plan-card');
    expect(cards.length).toBeGreaterThan(1);
  });

  it('shows loading spinner below cards when loadingMore and items.length < PAGE_SIZE', async () => {
    // Setup: baseList with 9 items, fetchMore returns empty (simulate end of remote data)
    // Inject 10 planHistories so items.length >= PAGE_SIZE and "View more" button is rendered
    const injectedValues = {
      ...makeBasePlan(0),
      planHistories: Array.from({length: 9}, (_, i) => ({
        price: 100 + i,
        costPerUser: 10 + i,
        allowedUnlimitedUsers: false,
        version: `v${i + 1}`,
        createdOn: `2025-07-${11 + i}T10:00:00.000Z`,
        createdBy: `user-mod${i + 1}`,
      })),
    };
    const fetchMore = vi.fn().mockImplementation(() => new Promise(resolve => setTimeout(() => resolve([]), 50)));
    render(<PlanHistory injectedValues={injectedValues as any} fetchMore={fetchMore} />);
    // Click "View more" to trigger loadingMore and remote fetch
    const btn = screen.getByTestId('view-more-button');
    await act(async () => {
      btn.click();
    });
    // Spinner should appear below cards while loadingMore is true and items.length < PAGE_SIZE
    expect(await screen.findByTestId('circular-progress')).toBeInTheDocument();
  });
});
