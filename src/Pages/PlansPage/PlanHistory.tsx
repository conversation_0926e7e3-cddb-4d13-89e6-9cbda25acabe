'use client';

import {<PERSON>, Button, CircularProgress, Stack} from '@mui/material';
import React, {useEffect, useMemo, useState} from 'react';
import {PlanResponse} from 'redux/app/types/plan.type';
import {PlanHistoryCard} from './PlanHistoryCard';

export interface BaseList {
  name: string;
  numberOfDevices: string;
  infraConfigurations: string;
  tenure: string;
  price: string;
  costPerUser: string;
  allowedUnlimited: boolean;
  version: string;
  createdAt: string;
  createdBy: string;
}

interface PlanHistoryProps {
  injectedValues?: PlanResponse;

  /**
   * Optional async loader for more history. If provided, component will use this to fetch
   * the next page (offset, limit) and append the results.
   * Return the SAME shape you build in `computeBaseList` (below) without _flags; the component will add flags.
   */
  fetchMore?: (offset: number, limit: number) => Promise<Array<BaseList>>;
}

export const withFlags = (list: BaseList[], baseList: BaseList[]) => {
  const lastIndex = baseList.length - 1;

  const usersChanged = (cur: (typeof baseList)[number], next?: (typeof baseList)[number]) => {
    if (!next) return false;
    if (cur.allowedUnlimited !== next.allowedUnlimited) return true;
    // both are limited users -> compare costPerUser
    if (!cur.allowedUnlimited && !next.allowedUnlimited && cur.costPerUser !== next.costPerUser) return true;
    return false;
  };

  return list.map((item, idx) => {
    const isLast = idx === lastIndex;
    const next = isLast ? undefined : baseList[idx + 1];

    return {
      ...item,
      _priceChanged: !isLast && next !== undefined && item.price !== next.price,
      _usersChanged: usersChanged(item, next),
    };
  });
};

const PAGE_SIZE = 10;

/**
 * Build the "baseList" (head + histories) from injectedValues.
 * Pulled out of component to reduce complexity inside PlanHistory.
 */
function computeBaseList(injectedValues?: PlanResponse): BaseList[] {
  if (!injectedValues) {
    return [
      {
        name: '',
        numberOfDevices: '-',
        infraConfigurations: '',
        tenure: '',
        price: '',
        costPerUser: '',
        allowedUnlimited: false,
        version: '',
        createdAt: '',
        createdBy: '',
      },
    ];
  }

  const {
    name = '',
    configureDevice: {min = '', max = ''} = {},
    tier = '',
    billingCycle: {cycleName = ''} = {},
    price = '',
    costPerUser = '',
    allowedUnlimitedUsers = false,
    version = '',
    modifiedOn,
    createdOn,
    modifiedBy,
    createdBy,
    planHistories = [],
  } = injectedValues;

  const numberOfDevices = `${min}-${max}`;
  const tenure = cycleName;
  const createdAt = modifiedOn ?? createdOn ?? '';
  const createdByName = modifiedBy ?? createdBy ?? '';

  const head: BaseList = {
    name,
    numberOfDevices,
    infraConfigurations: tier,
    tenure,
    price: `${price}`,
    costPerUser: `${costPerUser}`,
    allowedUnlimited: allowedUnlimitedUsers,
    version: `${version}`,
    createdAt,
    createdBy: createdByName,
  };

  const histories: BaseList[] = planHistories.map(h => ({
    name,
    numberOfDevices,
    infraConfigurations: tier,
    tenure,
    price: `${h.price}`,
    costPerUser: `${h.costPerUser}`,
    allowedUnlimited: !!h.allowedUnlimitedUsers,
    version: `${h.version}`,
    createdAt: h.createdOn,
    createdBy: h.createdBy,
  }));

  const list = [head, ...histories];

  // If there are at least 2 entries, rotate createdAt values anticlockwise by one.
  if (list.length > 1) {
    const createdAts = list.map(item => item.createdAt);
    const rotated = createdAts.slice(1).concat(createdAts[0]); // e2,e3,...,e1
    list.forEach((item, idx) => {
      item.createdAt = rotated[idx];
    });
  }

  return list;
}

function handleViewMoreHelper(loadingMore: boolean, hasMore: boolean) {
  if (loadingMore || !hasMore) return true;
  return false;
}

/**
 * Hook that encapsulates pagination, merging and flags for the PlanHistory list.
 * Keeps PlanHistory functional and simple.
 */
function usePaginatedList(
  baseList: BaseList[],
  fetchMore?: (offset: number, limit: number) => Promise<Array<BaseList>>,
) {
  const initialSlice = baseList.slice(0, PAGE_SIZE);

  const [items, setItems] = useState(() => withFlags(initialSlice, baseList));
  const [offset, setOffset] = useState(() => Math.min(PAGE_SIZE, baseList.length));
  const [loadingMore, setLoadingMore] = useState(false);
  const [lastBatchSize, setLastBatchSize] = useState(() => Math.min(PAGE_SIZE, baseList.length));

  useEffect(() => {
    // Reset when baseList changes
    const first = baseList.slice(0, PAGE_SIZE);
    setItems(withFlags(first, baseList));
    setOffset(first.length);
    setLastBatchSize(first.length);
  }, [baseList]);

  const hasMoreLocal = offset < baseList.length;
  const hasMoreRemote = lastBatchSize === PAGE_SIZE;
  const hasMore = fetchMore ? hasMoreRemote : hasMoreLocal;

  const loadLocalBatch = () => {
    const nextSlice = baseList.slice(offset, offset + PAGE_SIZE);
    setLastBatchSize(nextSlice.length);
    const merged = [...items.map(i => ({...i})), ...nextSlice];
    setItems(withFlags(merged, baseList));
    setOffset(offset + nextSlice.length);
  };

  const loadRemoteBatch = async () => {
    const nextBatch = await fetchMore!(offset, PAGE_SIZE);
    setLastBatchSize(nextBatch.length);
    const merged = [...items.map(i => ({...i})), ...nextBatch];
    setItems(withFlags(merged, baseList));
    setOffset(offset + nextBatch.length);
  };

  const handleViewMore = async () => {
    if (handleViewMoreHelper(loadingMore, hasMore)) return;
    setLoadingMore(true);
    try {
      if (fetchMore) {
        await loadRemoteBatch();
      } else {
        loadLocalBatch();
      }
    } finally {
      setLoadingMore(false);
    }
  };

  return {
    items,
    hasMore,
    loadingMore,
    handleViewMore,
  };
}

const PlanHistory: React.FC<PlanHistoryProps> = ({injectedValues, fetchMore}) => {
  const baseList = useMemo(() => computeBaseList(injectedValues), [injectedValues]);

  const {items, hasMore, loadingMore, handleViewMore} = usePaginatedList(baseList, fetchMore);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 2,
        width: '100%',
      }}
    >
      {items.map((planData, index) => (
        <PlanHistoryCard
          key={`${planData.createdAt}-${index}`}
          data={planData}
          updatedBy={planData.createdBy}
          index={index}
        />
      ))}

      {items.length >= PAGE_SIZE && hasMore && (
        <Stack alignItems="center" mt={1} mb={2}>
          <Button variant="outlined" onClick={handleViewMore} disabled={loadingMore} data-testid="view-more-button">
            {loadingMore ? (
              <Stack direction="row" alignItems="center" gap={1}>
                <CircularProgress size={18} data-testid="circular-progress" />
                Loading more…
              </Stack>
            ) : (
              'View more'
            )}
          </Button>
        </Stack>
      )}

      {loadingMore && items.length < PAGE_SIZE && (
        <Stack alignItems="center" mt={1} mb={2}>
          <CircularProgress size={24} data-testid="circular-progress" />
        </Stack>
      )}
    </Box>
  );
};

export default PlanHistory;
