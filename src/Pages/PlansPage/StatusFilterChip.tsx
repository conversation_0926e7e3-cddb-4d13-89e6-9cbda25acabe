import {Box, CircularProgress, Typography} from '@mui/material';
import {forwardRef} from 'react';
import {useGetStatusQuery} from 'redux/app/planManagementAPiSlice';
import {IClearFilters} from '../../Components/FilterUtil/IClearFilters';
import GenericFilterChip, {GenericFilterChipRef} from './GenericFilterChip';

export interface FilterStatusChipsProps {
  value?: Set<string>;
  onSelect?: (selected: Set<string>) => void;
}

export type FilterStatusChipsRef = IClearFilters;

const planStatus: Record<string, string> = {
  '0': 'Active',
  '1': 'Inactive',
};
export const getTitleForPlanId = (planId: string): string | undefined => planStatus[planId];

/**
 * A React forwardRef component that renders a filter chip for selecting plan statuses.
 * Fetches status data using the `useGetStatusQuery` hook and displays a loading or error message as appropriate.
 *
 * @component
 * @param {FilterStatusChipsProps} props - The props for the filter status chips.
 * @param {(value: string) => void} props.onSelect - Callback invoked when a status is selected.
 * @param {string} props.value - The currently selected status value.
 * @param {React.Ref<FilterStatusChipsRef>} ref - Ref forwarded to the underlying GenericFilterChip component.
 *
 * @returns {JSX.Element} The rendered filter chip component or a loading/error message.
 */
const FilterStatusChips = forwardRef<FilterStatusChipsRef, FilterStatusChipsProps>(({onSelect, value}, ref) => {
  const {data, isLoading, error} = useGetStatusQuery({});

  if (isLoading) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height={40}>
        <CircularProgress size={24} />
      </Box>
    );
  }
  if (error) {
    return <Typography>Something Went Wrong</Typography>;
  }

  return (
    <GenericFilterChip
      ref={ref as React.Ref<GenericFilterChipRef>}
      title="Status"
      data={data?.statuses ?? {}}
      value={value}
      onSelect={onSelect}
      getKey={(_, key) => key}
      getLabel={(_, key) => getTitleForPlanId(key) ?? String(data?.statuses?.[key])}
    />
  );
});
FilterStatusChips.displayName = 'FilterStatusChips';

export default FilterStatusChips;
