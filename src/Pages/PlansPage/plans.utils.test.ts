import {describe, expect, it} from 'vitest';
import {getDeviceConfigFormValidationSchema} from './plans.utils';

describe('getDeviceConfigFormValidationSchema', () => {
  const schema = getDeviceConfigFormValidationSchema();

  describe('Basic validation', () => {
    it('should validate a correct single row', async () => {
      const validData = {
        items: [{min: 1, max: 5}],
      };
      await expect(schema.validate(validData)).resolves.toBeTruthy();
    });

    it('should fail if min is less than 1', async () => {
      const invalidData = {
        items: [{min: 0, max: 5}],
      };
      await expect(schema.validate(invalidData)).rejects.toThrow('Min value must be at least 1');
    });

    it('should fail if max is greater than 64', async () => {
      const invalidData = {
        items: [{min: 1, max: 65}],
      };
      await expect(schema.validate(invalidData)).rejects.toThrow('Max value must be at most 64');
    });

    it('should fail if max is not greater than min', async () => {
      const invalidData = {
        items: [{min: 5, max: 5}],
      };
      await expect(schema.validate(invalidData)).rejects.toThrow('Max value must be greater than Min value');
    });
  });

  describe('Sequential validation', () => {
    it('should validate correct sequential rows', async () => {
      const validData = {
        items: [
          {min: 1, max: 5},
          {min: 6, max: 10},
          {min: 11, max: 20},
        ],
      };
      await expect(schema.validate(validData)).resolves.toBeTruthy();
    });

    it('should fail if second row min is not greater than first row max', async () => {
      const invalidData = {
        items: [
          {min: 1, max: 5},
          {min: 5, max: 10}, // min should be > 5
        ],
      };
      await expect(schema.validate(invalidData)).rejects.toThrow(
        "Min value must be greater than previous row's max value (5)",
      );
    });

    it('should fail if third row min is not greater than second row max', async () => {
      const invalidData = {
        items: [
          {min: 1, max: 5},
          {min: 6, max: 10},
          {min: 10, max: 15}, // min should be > 10
        ],
      };
      await expect(schema.validate(invalidData)).rejects.toThrow(
        "Min value must be greater than previous row's max value (10)",
      );
    });

    it('should fail when max value conflicts with next row min (edge case)', async () => {
      const invalidData = {
        items: [
          {min: 1, max: 10}, // max conflicts with next row min
          {min: 10, max: 15}, // min should be > 10
        ],
      };
      await expect(schema.validate(invalidData)).rejects.toThrow(
        "Min value must be greater than previous row's max value (10)",
      );
    });

    it('should fail when user changes max value that affects next row', async () => {
      const invalidData = {
        items: [
          {min: 1, max: 15}, // user changed max from 5 to 15, now conflicts with next row
          {min: 10, max: 20}, // this min is now invalid
        ],
      };
      await expect(schema.validate(invalidData)).rejects.toThrow(
        "Min value must be greater than previous row's max value (15)",
      );
    });
  });

  describe('Edge cases', () => {
    it('should PASS when first row max=4 and second row min=5', async () => {
      const validData = {
        items: [
          {min: 1, max: 4},
          {min: 5, max: 10},
        ],
      };
      await expect(schema.validate(validData)).resolves.toBeTruthy();
    });

    it('should handle empty items array', async () => {
      const emptyData = {
        items: [],
      };
      await expect(schema.validate(emptyData)).resolves.toBeTruthy();
    });

    it('should handle boundary values correctly', async () => {
      const boundaryData = {
        items: [
          {min: 1, max: 32},
          {min: 33, max: 64},
        ],
      };
      await expect(schema.validate(boundaryData)).resolves.toBeTruthy();
    });

    it('should fail with non-numeric values', async () => {
      const invalidData = {
        items: [{min: 'abc', max: 5}],
      };
      await expect(schema.validate(invalidData)).rejects.toThrow('Min value must be a number');
    });
  });
});
