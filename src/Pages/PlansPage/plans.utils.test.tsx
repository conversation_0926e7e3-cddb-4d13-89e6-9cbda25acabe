// @vitest-environment jsdom
import React from 'react';
import {describe, expect, it, vi} from 'vitest';
import * as utils from './plans.utils';

vi.mock('Components/StatusChip/StatusChip', () => ({
  __esModule: true,
  default: (props: any) => <div data-testid="status-chip" {...props} />,
}));
vi.mock('./PlanPage', () => ({
  ActionButtons: Object.assign((props: any) => <div data-testid="action-buttons" {...props} />, {
    displayName: 'ActionButtons',
  }),
}));

function isReactElement(node: any): node is React.ReactElement {
  return typeof node === 'object' && node !== null && 'props' in node;
}
function isFunction(val: unknown): val is Function {
  return typeof val === 'function';
}

describe('plans.utils', () => {
  it('getStatusColor returns correct color for all statuses', () => {
    expect(utils.getStatusColor(utils.PlanStatus.ACTIVE)).toBe('alert.success.bg');
    expect(utils.getStatusColor(utils.PlanStatus.INACTIVE)).toBe('alert.error.bg');
    expect(utils.getStatusColor(999 as unknown as utils.PlanStatus)).toBe('white.main');
  });

  it('getFontColor returns correct color for all statuses', () => {
    expect(utils.getFontColor(utils.PlanStatus.ACTIVE)).toBe('alert.success.onBg');
    expect(utils.getFontColor(utils.PlanStatus.INACTIVE)).toBe('alert.error.onBg');
    expect(utils.getFontColor(999 as unknown as utils.PlanStatus)).toBe('white.main');
  });

  it('getIndicatorColor returns correct color for all statuses', () => {
    expect(utils.getIndicatorColor(utils.PlanStatus.ACTIVE)).toBe('alert.success.main');
    expect(utils.getIndicatorColor(utils.PlanStatus.INACTIVE)).toBe('alert.error.main');
    expect(utils.getIndicatorColor(999 as unknown as utils.PlanStatus)).toBe('white.main');
  });

  it('getStatusLabel returns correct label for all statuses', () => {
    expect(utils.getStatusLabel(utils.PlanStatus.ACTIVE)).toBe('Active');
    expect(utils.getStatusLabel(utils.PlanStatus.INACTIVE)).toBe('Inactive');
    expect(utils.getStatusLabel(999 as unknown as utils.PlanStatus)).toBe('');
  });

  it('getBackendColumnName returns mapped or original column name', () => {
    expect(utils.getBackendColumnName('planName')).toBe('name');
    expect(utils.getBackendColumnName('status')).toBe('status');
    expect(utils.getBackendColumnName('configureDevice')).toBe('configureDevice');
    expect(utils.getBackendColumnName('currency')).toBe('currency');
    expect(utils.getBackendColumnName('billingCycle')).toBe('billingCycle');
    expect(utils.getBackendColumnName('tier')).toBe('tier');
    expect(utils.getBackendColumnName('unknown')).toBe('unknown');
  });

  it('capitalize capitalizes the first letter and lowercases the rest', () => {
    expect(utils.capitalize('hello')).toBe('Hello');
    expect(utils.capitalize('HELLO')).toBe('Hello');
    expect(utils.capitalize('h')).toBe('H');
    expect(utils.capitalize('')).toBe('');
  });

  it('getPlanTableColumns: status cell renders StatusChip with correct props', () => {
    const columns = utils.getPlanTableColumns(() => {});
    const col = columns.find(c => c.id === 'status')!;
    const context = {
      getValue: () => utils.PlanStatus.ACTIVE,
    } as any;
    const result = col.cell!(context);
    expect(isReactElement(result)).toBe(true);
    if (isReactElement(result)) {
      const props = result.props as any;
      expect(props.label).toBe('Active');
      expect(props.backgroundColor).toBe('alert.success.bg');
      expect(props.indicatorColor).toBe('alert.success.main');
      expect(props.color).toBe('alert.success.onBg');
    }
  });

  it('getPlanTableColumns: No. of devices cell renders range or dash', () => {
    const columns = utils.getPlanTableColumns(() => {});
    const col = columns.find(c => c.id === 'configureDevice')!;
    const contextWithDevice = {row: {original: {configureDevice: {min: '1', max: '10'}}}} as any;
    const contextWithoutDevice = {row: {original: {configureDevice: undefined}}} as any;
    expect(col.cell!(contextWithDevice)).toBe('1 - 10');
    expect(col.cell!(contextWithoutDevice)).toBe('-');
  });

  it('getPlanTableColumns: Subscription Tenure and Infra Configuration capitalize', () => {
    const columns = utils.getPlanTableColumns(() => {});
    const tenureCol = columns.find(c => c.id === 'billingCycle')!;
    const tierCol = columns.find(c => c.id === 'tier')!;
    const context = {row: {original: {billingCycle: {cycleName: 'monthly'}, tier: 'premium'}}} as any;
    expect(tenureCol.cell!(context)).toBe('Monthly');
    expect(tierCol.cell!(context)).toBe('Premium');
  });

  it('getPlanTableColumns: Price cell renders with currency symbol', () => {
    const columns = utils.getPlanTableColumns(() => {});
    const col = columns.find(c => c.id === 'price')!;
    const context = {row: {original: {price: 100, currency: {symbol: '$'}}}} as any;
    const contextNoSymbol = {row: {original: {price: 100, currency: {}}}} as any;
    expect(col.cell!(context)).toBe('$100');
    expect(col.cell!(contextNoSymbol)).toBe('100');
  });

  it('getPlanTableColumns: actions cell renders ActionButtons', () => {
    const refetch = vi.fn();
    const columns = utils.getPlanTableColumns(refetch);
    const col = columns.find(c => c.header === 'Actions')!;
    const context = {} as any;
    const result = col.cell!(context);
    expect(isReactElement(result)).toBe(true);
    if (isReactElement(result) && isFunction(result.type)) {
      expect((result.type as any).displayName).toBe('ActionButtons');
      expect((result.props as any).refetchPlans).toBe(refetch);
    }
  });
});
