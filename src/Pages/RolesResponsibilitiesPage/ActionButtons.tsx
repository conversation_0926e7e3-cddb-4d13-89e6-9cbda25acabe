import {Stack} from '@mui/material';
import EditIcon from 'Assets/EditIcon';
import EyeIcon from 'Assets/EyeIcon';
import RoleMarkActiveIcon from 'Assets/RoleMarkActiveIcon';
import RoleMarkDeactivateIcon from 'Assets/RoleMarkDeactivateIcon';
import PermissionWrapper from 'Components/PermissionWrapper';
import {DefaultToolTip} from 'Components/ToolTipTypography/ToolTipTypography';
import PermissionKey from 'Constants/enums/permissions';
import React from 'react';
import {useNavigate} from 'react-router';
import {IRoleView} from 'redux/auth/authApiSlice';
import {RouteNames} from 'Routes/routeNames';
import {actionStyles} from 'styles/pages/Common.styles';
import {RoleStatus} from './roles.util';

interface RoleListActionButtonsProps {
  role: IRoleView;
  onClickEditButton?: (role: IRoleView) => void;
}

/**
 * ActionButtons component for Role and Permissions
 * @param props - Component props
 * @returns JSX.Element
 */
export const ActionButtons: React.FC<RoleListActionButtonsProps> = props => {
  const navigate = useNavigate();
  const buildStatusAction = () => {
    if (props.role.status === RoleStatus.ACTIVE) {
      return (
        <DefaultToolTip
          title="Deactivate"
          data-testid="deactivate-button"
          onClick={() => {
            props.onClickEditButton?.(props.role);
          }}
        >
          <RoleMarkDeactivateIcon sx={actionStyles} />
        </DefaultToolTip>
      );
    } else if (props.role.status === RoleStatus.INACTIVE) {
      return (
        <DefaultToolTip
          title="Activate"
          data-testid="activate-button"
          onClick={() => {
            props.onClickEditButton?.(props.role);
          }}
        >
          <RoleMarkActiveIcon sx={actionStyles} />
        </DefaultToolTip>
      );
    } else {
      return null;
    }
  };

  const isRoleInactive = props.role.status === RoleStatus.INACTIVE;

  return (
    <Stack display="flex" flexDirection={'row'} gap={1.4}>
      <DefaultToolTip
        title="View details"
        data-testid="view-button"
        onClick={() =>
          navigate(RouteNames.VIEW_ROLE, {
            state: {
              role: props.role,
            },
          })
        }
      >
        <EyeIcon sx={actionStyles} />
      </DefaultToolTip>
      <PermissionWrapper permission={PermissionKey.UpdateRole}>
        <DefaultToolTip title={!isRoleInactive ? 'Edit' : 'Edit disabled for inactive roles'} data-testid="edit-button">
          <EditIcon
            sx={{
              ...actionStyles,
              color: isRoleInactive ? 'body.200' : 'body.500',
              cursor: isRoleInactive ? 'not-allowed' : 'pointer',
            }}
            data-testid="edit-button"
            onClick={isRoleInactive ? undefined : () => navigate(RouteNames.EDIT_ROLE, {state: {role: props.role}})}
          />
        </DefaultToolTip>
        {buildStatusAction()}
      </PermissionWrapper>
    </Stack>
  );
};
