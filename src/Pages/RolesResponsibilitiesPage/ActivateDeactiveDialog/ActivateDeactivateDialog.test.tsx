import {fireEvent, render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import {ActionType, ActivateDeactivateDialog} from './ActivateDeactivateDialog';

vi.mock('Assets/confirmation-icons/ActivateConfirmIcon', () => ({
  __esModule: true,
  default: (props: any) => <svg data-testid="activate-icon" {...props} />,
}));
vi.mock('Assets/confirmation-icons/DeactivateConfirmIcon', () => ({
  __esModule: true,
  default: (props: any) => <svg data-testid="deactivate-icon" {...props} />,
}));
vi.mock('Components/BaseDialogLayout', () => ({
  BaseDialogLayout: (props: any) => (
    <div data-testid="base-dialog">
      <button onClick={props.onClose}>Close</button>
      <button onClick={props.onConfirm}>{props.confirmLabel}</button>
      <span>{props.title}</span>
      <span>{props.message}</span>
      <span>{props.highlightText}</span>
      <span>{props.iconBGColor}</span>
      {props.icon}
      {props.isLoading && <span data-testid="loading">Loading...</span>}
    </div>
  ),
}));

describe('ActivateDeactivateDialog', () => {
  it('renders activate dialog with correct icon and message', () => {
    render(
      <ActivateDeactivateDialog
        open={true}
        onClose={vi.fn()}
        onConfirm={vi.fn()}
        actionType={ActionType.Activate}
        title="Test Role"
        isLoading={false}
      />,
    );
    expect(screen.getByTestId('activate-icon')).toBeInTheDocument();
    expect(screen.getByText('Activate Role')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to activate this role?')).toBeInTheDocument();
    expect(screen.getByText('Test Role')).toBeInTheDocument();
    expect(screen.getByText('alert.success.bg')).toBeInTheDocument();
  });

  it('renders deactivate dialog with correct icon and message', () => {
    render(
      <ActivateDeactivateDialog
        open={true}
        onClose={vi.fn()}
        onConfirm={vi.fn()}
        actionType={ActionType.Deactivate}
        title="Test Role"
        isLoading={false}
      />,
    );
    expect(screen.getByTestId('deactivate-icon')).toBeInTheDocument();
    expect(screen.getByText('Deactivate Role')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to deactivate this role?')).toBeInTheDocument();
    expect(screen.getByText('Test Role')).toBeInTheDocument();
    expect(screen.getByText('alert.error.bg')).toBeInTheDocument();
  });

  it('calls onClose and onConfirm when buttons are clicked', () => {
    const onClose = vi.fn();
    const onConfirm = vi.fn();
    render(
      <ActivateDeactivateDialog
        open={true}
        onClose={onClose}
        onConfirm={onConfirm}
        actionType={ActionType.Activate}
        title="Test Role"
        isLoading={false}
      />,
    );
    fireEvent.click(screen.getByText('Close'));
    fireEvent.click(screen.getByText('Activate'));
    expect(onClose).toHaveBeenCalled();
    expect(onConfirm).toHaveBeenCalled();
  });

  it('shows loading indicator when isLoading is true', () => {
    render(
      <ActivateDeactivateDialog
        open={true}
        onClose={vi.fn()}
        onConfirm={vi.fn()}
        actionType={ActionType.Activate}
        title="Test Role"
        isLoading={true}
      />,
    );
    expect(screen.getByTestId('loading')).toBeInTheDocument();
  });

  it('does not render dialog when open is false', () => {
    render(
      <ActivateDeactivateDialog
        open={false}
        onClose={vi.fn()}
        onConfirm={vi.fn()}
        actionType={ActionType.Activate}
        title="Test Role"
        isLoading={false}
      />,
    );
    expect(screen.queryByTestId('base-dialog')).not.toBeInTheDocument();
  });
});
