import {fireEvent, screen, waitFor} from '@testing-library/react';
import {memoryRenderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, vi} from 'vitest';
import AddRole from './AddRole';

const mockNavigate = vi.fn();
const mockAddRole = vi.fn();
const mockUpdateRole = vi.fn();
const mockGetRoleById = vi.fn();

vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({
      state: {},
    }),
  };
});

vi.mock('notistack', () => ({
  enqueueSnackbar: vi.fn(),
}));

const mockUseAddRoleLocationMeta = vi.fn();

vi.mock('./addRole.util', () => ({
  useAddRoleLocationMeta: () => mockUseAddRoleLocationMeta(),
}));

vi.mock('redux/auth/authApiSlice', () => ({
  useAddRoleMutation: () => [mockAddRole],
  useUpdateRoleMutation: () => [mockUpdateRole],
}));

const mockGetRoleByName = vi.fn();

vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetRoleByIdQuery: (id: any) => mockGetRoleById(id),
  useLazyGetRoleByNameQuery: () => [mockGetRoleByName],
}));

vi.mock('./RolePermission', () => ({
  __esModule: true,
  default: ({onChangePermission}: {onChangePermission: (permissions: string[], isSame: boolean) => void}) => (
    <div>
      <button onClick={() => onChangePermission(['VIEW_ROLE'], false)}>Mock RolePermissions</button>
    </div>
  ),
}));

describe('AddRole', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockGetRoleById.mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
      isFetching: false,
    });
    mockUseAddRoleLocationMeta.mockReturnValue({
      role: undefined,
    });
    // Mock the role name validation to return no existing roles
    mockGetRoleByName.mockResolvedValue({
      data: [],
    });
  });

  it('should render the component in add mode', () => {
    memoryRenderWithTheme(<AddRole />);
    expect(screen.getByText('Role details')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Add Role'})).toBeInTheDocument();
  });

  it('should show loader while fetching role data in edit mode', () => {
    mockUseAddRoleLocationMeta.mockReturnValue({
      role: {roleId: '1', name: 'Test Role'},
    });
    mockGetRoleById.mockReturnValue({
      data: [],
      isLoading: true,
      isError: false,
      isFetching: false,
    });
    memoryRenderWithTheme(<AddRole />);
    expect(screen.getByTestId('circularProgress')).toBeInTheDocument();
  });

  it('should show an error message if fetching role data fails', () => {
    mockUseAddRoleLocationMeta.mockReturnValue({
      role: {roleId: '1', name: 'Test Role'},
    });
    mockGetRoleById.mockReturnValue({
      data: [],
      isLoading: false,
      isError: true,
      isFetching: false,
    });
    memoryRenderWithTheme(<AddRole />);
    expect(screen.getByText('Something went wrong, please try again later')).toBeInTheDocument();
  });

  it('should submit the form and call addRole mutation', async () => {
    const mockUnwrap = vi.fn().mockResolvedValue({});
    mockAddRole.mockReturnValue({unwrap: mockUnwrap});
    memoryRenderWithTheme(<AddRole />);

    const roleNameInput = screen.getByPlaceholderText('Enter Role name');
    fireEvent.change(roleNameInput, {target: {value: 'TestRole'}});

    // Simulate selecting a permission
    fireEvent.click(screen.getByText('Mock RolePermissions'));

    const submitButton = screen.getByRole('button', {name: 'Add Role'});
    fireEvent.click(submitButton);

    // First wait for the mutation to be called
    await waitFor(() => {
      expect(mockAddRole).toHaveBeenCalledWith({
        name: 'TestRole',
        permissions: expect.any(Array),
      });
    });

    // Then wait for the unwrap to be called
    await waitFor(() => {
      expect(mockUnwrap).toHaveBeenCalled();
    });
  });

  it('should submit the form and call updateRole mutation in edit mode', async () => {
    const roleData = {
      roleId: '1',
      name: 'Initial Role',
      permissions: ['VIEW_USER'],
    };
    mockUseAddRoleLocationMeta.mockReturnValue({
      role: {roleId: '1', name: 'Test Role'},
    });
    mockGetRoleById.mockReturnValue({
      data: [roleData],
      isLoading: false,
      isError: false,
      isFetching: false,
    });
    const mockUnwrap = vi.fn().mockResolvedValue({});
    mockUpdateRole.mockReturnValue({unwrap: mockUnwrap});

    memoryRenderWithTheme(<AddRole />);

    await waitFor(() => {
      expect(screen.getByDisplayValue('Initial Role')).toBeInTheDocument();
    });

    const roleNameInput = screen.getByPlaceholderText('Enter Role name');
    fireEvent.change(roleNameInput, {target: {value: 'UpdatedTestRole'}});

    fireEvent.click(screen.getByText('Mock RolePermissions'));

    const submitButton = screen.getByRole('button', {name: 'Update'});
    fireEvent.click(submitButton);

    // First wait for the mutation to be called
    await waitFor(() => {
      expect(mockUpdateRole).toHaveBeenCalledWith({
        id: '1',
        name: 'UpdatedTestRole',
        permissions: expect.any(Array),
      });
    });

    // Then wait for the unwrap to be called
    await waitFor(() => {
      expect(mockUnwrap).toHaveBeenCalled();
    });
  });

  it('should disable submit button if no permissions are selected', () => {
    memoryRenderWithTheme(<AddRole />);
    const roleNameInput = screen.getByPlaceholderText('Enter Role name');
    fireEvent.change(roleNameInput, {target: {value: 'TestRole'}});
    const submitButton = screen.getByRole('button', {name: 'Add Role'});
    expect(submitButton).toBeDisabled();
  });
});
