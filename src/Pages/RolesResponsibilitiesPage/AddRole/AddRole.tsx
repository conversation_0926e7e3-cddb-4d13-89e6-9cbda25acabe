import {Box, Divider, Typography} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import Form from 'Components/Forms/Form';
import {FormikValues} from 'formik';
import {getAllCombinedPermissions} from 'Helpers/serverPermissionMappper';
import {StyleUtils} from 'Helpers/styleUtils';
import {enqueueSnackbar} from 'notistack';
import React, {useCallback, useMemo} from 'react';
import {useNavigate} from 'react-router';
import {useGetRoleByIdQuery} from 'redux/app/tenantManagementApiSlice';
import {useAddRoleMutation, useUpdateRoleMutation} from 'redux/auth/authApiSlice';
import {RouteNames} from 'Routes/routeNames';
import {useAddRoleLocationMeta} from './addRole.util';
import AddRoleActions from './AddRoleActions';
import AddRoleFormContent from './AddRoleFormContent';
import RolePermissions from './RolePermission';
import {useValidationSchema} from './validationSchema';

const PX = 2;

/**
 * `AddRole` is a React functional component for adding or editing a role within the Roles & Responsibilities section.
 *
 * - If an initial role is provided via location meta, the component operates in "edit" mode; otherwise, it creates a new role.
 * - Fetches role data when editing, and displays loading or error states as appropriate.
 * - Handles form submission for both adding and updating roles, including validation and permission management.
 * - Displays breadcrumbs, role details form, permissions selection, and action buttons.
 * - Navigates back to the Roles & Responsibilities page upon successful submission.
 *
 * @component
 * @returns {JSX.Element} The rendered AddRole page.
 */
const AddRole: React.FC = () => {
  const {role: initialRole} = useAddRoleLocationMeta();
  const isEditing = Boolean(initialRole);
  const {
    data: roles = [],
    isLoading: loadingRole,
    isError: errorLoadingRole,
    isFetching: fetchingRole,
  } = useGetRoleByIdQuery(
    {roleID: initialRole?.roleId ?? ''},
    {
      skip: !initialRole?.roleId,
      refetchOnMountOrArgChange: true,
    },
  );
  const role = roles.length > 0 ? roles[0] : null;
  const initialValues = {
    roleName: role?.name ?? '',
  };
  const [permissionArr, setPermissionArr] = React.useState<string[]>([]);
  const [isSameAsInitial, setIsSameAsInitial] = React.useState<boolean>(false);

  const {addRoleValidationSchema} = useValidationSchema({
    initialRoleName: initialRole?.roleName,
  });

  const [addRole] = useAddRoleMutation();
  const [updateRole] = useUpdateRoleMutation();
  const navigate = useNavigate();

  const breadcrumbsData = useMemo(
    () => [
      {label: 'Roles & Responsibilities', url: RouteNames.ROLES_RESPONSIBILITIES},
      {
        label: isEditing ? 'Edit Role' : 'Add Role',
        url: isEditing ? RouteNames.EDIT_ROLE : RouteNames.ADD_ROLE,
      },
    ],
    [isEditing],
  );

  /**
   * Handles changes to the permissions array and updates the state accordingly.
   *
   * @param permissions - The updated array of permission strings.
   * @param isSame - A boolean indicating whether the permissions are the same as the initial state.
   */
  const handlePermissionOnChange = useCallback(
    (permissions: string[], isSame: boolean) => {
      setPermissionArr(permissions);
      setIsSameAsInitial(isSame);
    },
    [setPermissionArr, setIsSameAsInitial],
  );

  const submit = useCallback(
    async (values: FormikValues) => {
      const roleName = values['roleName'];
      const allPermissions = getAllCombinedPermissions(permissionArr);
      try {
        if (isEditing) {
          await updateRole({id: initialRole!.roleId, name: roleName, permissions: allPermissions}).unwrap();
          enqueueSnackbar('Role updated successfully', {variant: 'success'});
        } else {
          await addRole({name: roleName, permissions: allPermissions}).unwrap();
          enqueueSnackbar('Role created successfully', {variant: 'success', subMessage: roleName});
        }
        navigate(RouteNames.ROLES_RESPONSIBILITIES);
      } finally {
        // sonar suppress
      }
    },
    [permissionArr],
  );

  const canSubmit = permissionArr.length > 0;

  if ((initialRole && loadingRole) || fetchingRole) {
    return <BackdropLoader />;
  }

  if (initialRole && errorLoadingRole) {
    return <h1>Something went wrong, please try again later</h1>;
  }

  return (
    <Box>
      <Box sx={{mb: 1}}>
        <Breadcrumb items={breadcrumbsData} separator="|" showHeader />
      </Box>
      <Box
        sx={{
          py: 1.5,
          border: '1px solid',
          borderColor: 'body.200',
          gap: 1.5,
          borderRadius: '0.375rem',
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        <Box>
          <Box px={2}>
            <Typography sx={{fontSize: '1rem', fontWeight: 700, color: 'body.900'}} mb={2}>
              Role details
            </Typography>

            <Typography sx={StyleUtils.lalelStyles}>Role name*</Typography>
          </Box>
          <Form
            initialValues={initialValues}
            validationSchema={addRoleValidationSchema}
            validateOnChange={true}
            enableReinitialize
            onSubmit={submit}
          >
            <Box sx={{mb: 2, minHeight: '73vh', display: 'flex', flexDirection: 'column'}}>
              <Box sx={{px: PX}}>
                <AddRoleFormContent />
                <RolePermissions
                  sx={{mt: 2}}
                  initialChecked={role?.permissions}
                  onChangePermission={handlePermissionOnChange}
                />
              </Box>

              <Divider sx={{py: PX, mt: 'auto'}} />
              <AddRoleActions sx={{px: PX, mt: PX}} canSubmit={canSubmit && !isSameAsInitial} isEditing={isEditing} />
            </Box>
          </Form>
        </Box>
      </Box>
    </Box>
  );
};

export default AddRole;
