import {render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import AddRoleActions from './AddRoleActions';

// Mocks
vi.mock('react-router', () => ({
  useNavigate: () => vi.fn(),
}));
vi.mock('formik', () => ({
  useFormikContext: () => ({
    handleSubmit: vi.fn(),
    dirty: true,
    isValid: true,
    isSubmitting: false,
  }),
}));

describe('AddRoleActions', () => {
  it('renders Add and Cancel buttons', () => {
    render(<AddRoleActions canSubmit={true} isEditing={false} />);
    expect(screen.getByText('Add Role')).toBeInTheDocument();
    expect(screen.getByText('Cancel')).toBeInTheDocument();
  });

  it('renders Update button when editing', () => {
    render(<AddRoleActions canSubmit={true} isEditing={true} />);
    expect(screen.getByText('Update')).toBeInTheDocument();
  });

  it('disables Add button when canSubmitForm returns false', () => {
    vi.spyOn(require('formik'), 'useFormikContext').mockReturnValue({
      handleSubmit: vi.fn(),
      dirty: false,
      isValid: false,
      isSubmitting: false,
    });
    render(<AddRoleActions canSubmit={false} isEditing={false} />);
    expect(screen.getByText('Add Role')).toBeDisabled();
  });
});
