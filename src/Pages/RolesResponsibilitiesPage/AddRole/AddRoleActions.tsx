import {Box, BoxProps} from '@mui/material';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import {useFormikContext} from 'formik';
import {FC} from 'react';
import {useNavigate} from 'react-router';

interface AddRoleActionProps extends BoxProps {
  canSubmit: boolean;
  isEditing: boolean;
}

/**
 * Renders action buttons for the Add Role form, including "Cancel" and "Add Role"/"Update".
 * Handles form submission and navigation logic based on editing state and form validity.
 *
 * @param sx - Optional style overrides for the container Box.
 * @param canSubmit - Indicates if the form can be submitted (additional custom logic).
 * @param isEditing - Determines if the form is in editing mode (shows "Update" instead of "Add Role").
 * @param props - Additional props passed to the Box container.
 *
 * @remarks
 * - Uses Formik context for form state and submission.
 * - Disables the submit button based on form validity, dirty state, and submission status.
 * - Navigates back on "Cancel" click.
 */
const AddRoleActions: FC<AddRoleActionProps> = ({sx, canSubmit, isEditing, ...props}) => {
  const {handleSubmit, dirty, isValid, isSubmitting} = useFormikContext();
  const navigate = useNavigate();
  const canSubmitForm = () => {
    if (isEditing) {
      return (dirty || canSubmit) && isValid;
    }
    return dirty && isValid && canSubmit && !isSubmitting;
  };
  return (
    <Box sx={{display: 'flex', justifyContent: 'flex-end', ...sx}} {...props}>
      <BorderButton onClick={() => navigate(-1)}>Cancel</BorderButton>
      <BlueButton
        variant="contained"
        color="primary"
        type="submit"
        loading={isSubmitting}
        onClick={() => handleSubmit()}
        sx={{ml: 1}}
        disabled={!canSubmitForm()}
      >
        {isEditing ? 'Update' : 'Add Role'}
      </BlueButton>
    </Box>
  );
};

export default AddRoleActions;
