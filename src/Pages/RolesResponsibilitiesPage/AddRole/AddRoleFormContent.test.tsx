// src/Pages/RolesResponsibilitiesPage/AddRole/AddRoleFormContent.test.tsx
import {render, screen} from '@testing-library/react';
import {Formik} from 'formik';
import AddRoleFormContent from './AddRoleFormContent';

describe('AddRoleFormContent', () => {
  it('renders the role name input with correct placeholder', () => {
    render(
      <Formik initialValues={{roleName: ''}} onSubmit={() => {}}>
        <AddRoleFormContent />
      </Formik>,
    );
    const input = screen.getByPlaceholderText('Enter Role name');
    expect(input).toBeInTheDocument();
    expect(input).toHaveAttribute('name', 'roleName');
    expect(input).toHaveAttribute('id', 'roleName');
  });

  it('shows error message when touched and error present', () => {
    render(
      <Formik
        initialValues={{roleName: ''}}
        initialTouched={{roleName: true}}
        initialErrors={{roleName: 'Role name required'}}
        onSubmit={() => {}}
      >
        <AddRoleFormContent />
      </Formik>,
    );
    expect(screen.getByText('Role name required')).toBeInTheDocument();
  });
});
