import FormInput from 'Components/Forms/FormInput';
import {getIn, useFormikContext} from 'formik';
import {StyleUtils} from 'Helpers/styleUtils';

/**
 * Renders the form input field for adding a new role name within the Add Role form.
 *
 * @param initialValue - The initial value for the form input, typically used to pre-populate the field.
 * @returns A React element containing the role name input field with validation and styling.
 *
 * @remarks
 * Utilizes Formik context to display validation errors and touched state.
 * The input field is styled responsively and marked as required.
 */
const AddRoleFormContent = () => {
  const {touched, errors} = useFormikContext();

  return (
    <FormInput
      id={`roleName`}
      name={`roleName`}
      fullWidth
      required
      sx={{...StyleUtils.inputBoxStyles, px: 2, width: {xs: '100%', sm: '60%', md: '40%'}}}
      readOnly={false}
      errorMessage={getIn(touched, `roleName`) && getIn(errors, `roleName`) ? getIn(errors, `roleName`) : ''}
      placeholder="Enter Role name"
    />
  );
};

export default AddRoleFormContent;
