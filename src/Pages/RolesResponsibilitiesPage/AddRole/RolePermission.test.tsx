import {render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {setupCommonMocks} from 'TestHelper/TestHelper';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import RolePermissions from './RolePermission';
setupCommonMocks();
// Mock the categorizedPermissions to have predictable test data
vi.mock('Helpers/categorizedPermissions', () => {
  const mockPermissions = {
    Tenant: [
      {
        label: 'Create Tenant',
        key: 'tenant:create',
        permission: 'CreateTenant',
        dependent: ['ViewTenant'],
      },
      {
        label: 'View Tenant',
        key: 'tenant:view',
        permission: 'ViewTenant',
        dependent: [],
      },
    ],
    Plans: [
      {
        label: 'Create Plan',
        key: 'plan:create',
        permission: 'CreatePlan',
        dependent: ['ViewPlan'],
      },
      {
        label: 'View Plan',
        key: 'plan:view',
        permission: 'ViewPlan',
        dependent: [],
      },
    ],
  };

  return {
    default: mockPermissions,
    PermissionCategory: {
      Tenant: 'Tenant',
      Plans: 'Plans',
    },
  };
});

describe('RolePermissions', () => {
  const mockOnChangePermission = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing', () => {
    render(<RolePermissions />);
    expect(screen.getByText('Enable all')).toBeInTheDocument();
  });

  it('renders all permission categories', () => {
    render(<RolePermissions />);

    expect(screen.getByText('Tenant')).toBeInTheDocument();
    expect(screen.getByText('Plans')).toBeInTheDocument();
  });

  it('renders individual permission items within categories', () => {
    render(<RolePermissions />);

    // Check tenant permissions
    expect(screen.getByText('Create Tenant')).toBeInTheDocument();
    expect(screen.getByText('View Tenant')).toBeInTheDocument();
    expect(screen.getByText('Create Plan')).toBeInTheDocument();
    expect(screen.getByText('View Plan')).toBeInTheDocument();
  });

  it('calls onChangePermission when permissions change', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    const createCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    await user.click(createCheckbox);

    await waitFor(() => {
      expect(mockOnChangePermission).toHaveBeenCalled();
    });
  });

  it('handles initial checked permissions correctly', () => {
    const initialChecked = ['CreateTenant'];
    render(<RolePermissions initialChecked={initialChecked} onChangePermission={mockOnChangePermission} />);

    // The tenant:create checkbox should be checked due to dependency resolution
    const createCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    expect(createCheckbox).toBeChecked();
  });

  it('enables dependencies when a permission is selected', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    // Click on tenant create (which depends on ViewTenant)
    const createCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    await user.click(createCheckbox);

    // Both create and view should be checked due to dependency
    expect(createCheckbox).toBeChecked();
    const viewCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
    expect(viewCheckbox).toBeChecked();
  });

  it('handles enable all functionality', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    const enableAllCheckbox = screen.getByRole('checkbox', {name: /enable all/i});
    await user.click(enableAllCheckbox);

    // All individual checkboxes should be checked
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      if (checkbox !== enableAllCheckbox) {
        expect(checkbox).toBeChecked();
      }
    });
  });

  it('handles disable all functionality', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    // First enable all
    const enableAllCheckbox = screen.getByRole('checkbox', {name: /enable all/i});
    await user.click(enableAllCheckbox);

    // Then disable all
    await user.click(enableAllCheckbox);

    // All individual checkboxes should be unchecked
    const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
    const createPlanCheckbox = screen.getByRole('checkbox', {name: /create plan/i});
    const viewPlanCheckbox = screen.getByRole('checkbox', {name: /view plan/i});

    expect(createTenantCheckbox).not.toBeChecked();
    expect(viewTenantCheckbox).not.toBeChecked();
    expect(createPlanCheckbox).not.toBeChecked();
    expect(viewPlanCheckbox).not.toBeChecked();
  });

  it('shows indeterminate state for enable all when some permissions are selected', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    // Select only one permission
    const createCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    await user.click(createCheckbox);

    const enableAllCheckbox = screen.getByRole('checkbox', {name: /enable all/i});
    expect(enableAllCheckbox).toHaveAttribute('data-indeterminate', 'true');
  });

  it('handles group toggle functionality', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    // Find the tenant group checkbox (first accordion)
    const tenantGroupCheckbox = screen.getAllByRole('checkbox')[1]; // Skip the "Enable all" checkbox
    await user.click(tenantGroupCheckbox);

    // All tenant permissions should be checked
    const createCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    const viewCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});

    expect(createCheckbox).toBeChecked();
    expect(viewCheckbox).toBeChecked();
  });

  it('prevents disabling required dependencies', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    // First enable create (which depends on view)
    const createCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    await user.click(createCheckbox);

    // Now try to disable view - it should remain checked because create depends on it
    const viewCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
    await user.click(viewCheckbox);

    // View should still be checked due to dependency
    expect(viewCheckbox).toBeChecked();
    expect(createCheckbox).toBeChecked();
  });

  it('allows disabling dependencies when no longer required', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    // Enable create (which depends on view)
    const createCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    await user.click(createCheckbox);

    // Disable create first
    await user.click(createCheckbox);

    // Now view should be able to be disabled
    const viewCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
    await user.click(viewCheckbox);

    expect(viewCheckbox).not.toBeChecked();
    expect(createCheckbox).not.toBeChecked();
  });

  it('reports correct permissions to onChangePermission callback', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    const createCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
    await user.click(createCheckbox);

    await waitFor(() => {
      expect(mockOnChangePermission).toHaveBeenCalledWith(
        expect.arrayContaining(['CreateTenant', 'ViewTenant']),
        false,
      );
    });
  });

  it('reports isSame as true when permissions match initial state', async () => {
    const initialChecked = ['CreateTenant'];
    render(<RolePermissions initialChecked={initialChecked} onChangePermission={mockOnChangePermission} />);

    // The component should report isSame as true initially
    await waitFor(() => {
      expect(mockOnChangePermission).toHaveBeenCalledWith(expect.any(Array), true);
    });
  });

  it('handles accordion expand/collapse without affecting permissions', async () => {
    const user = userEvent.setup();
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    // Check initial state - no permissions should be checked
    const createPlanCheckbox = screen.getByRole('checkbox', {name: /create plan/i});
    expect(createPlanCheckbox).not.toBeChecked();

    // Click the expand icon to collapse/expand accordion
    const expandIcon = screen.getAllByTestId('ExpandMoreIcon')[0];
    await user.click(expandIcon);

    // Permissions should still not be checked after accordion interaction
    expect(createPlanCheckbox).not.toBeChecked();
  });

  it('handles empty initial permissions', () => {
    render(<RolePermissions initialChecked={[]} onChangePermission={mockOnChangePermission} />);

    // All checkboxes should be unchecked
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      expect(checkbox).not.toBeChecked();
    });
  });

  it('handles undefined initial permissions', () => {
    render(<RolePermissions onChangePermission={mockOnChangePermission} />);

    // All checkboxes should be unchecked
    const checkboxes = screen.getAllByRole('checkbox');
    checkboxes.forEach(checkbox => {
      expect(checkbox).not.toBeChecked();
    });
  });

  it('applies custom sx styles', () => {
    const customSx = {backgroundColor: 'rgb(255, 0, 0)'};
    render(<RolePermissions sx={customSx} />);

    // The root Box should have the custom styles applied
    const rootBox = screen.getByText('Enable all').closest('div');
    expect(rootBox).toHaveStyle('background-color: rgb(255, 0, 0)');
  });

  it('passes through additional props to root Box', () => {
    render(<RolePermissions data-testid="role-permissions" />);

    expect(screen.getByTestId('role-permissions')).toBeInTheDocument();
  });

  describe('Complex Dependency Scenarios', () => {
    it('handles multiple permissions with overlapping dependencies', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Enable both create permissions which both depend on view permissions
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
      const createPlanCheckbox = screen.getByRole('checkbox', {name: /create plan/i});

      await user.click(createTenantCheckbox);
      await user.click(createPlanCheckbox);

      // Both view permissions should be enabled
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
      const viewPlanCheckbox = screen.getByRole('checkbox', {name: /view plan/i});

      expect(viewTenantCheckbox).toBeChecked();
      expect(viewPlanCheckbox).toBeChecked();
      expect(createTenantCheckbox).toBeChecked();
      expect(createPlanCheckbox).toBeChecked();
    });

    it('prevents disabling dependency when multiple permissions depend on it', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Enable both create permissions
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
      const createPlanCheckbox = screen.getByRole('checkbox', {name: /create plan/i});

      await user.click(createTenantCheckbox);
      await user.click(createPlanCheckbox);

      // Try to disable view tenant - should remain checked because create tenant depends on it
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
      await user.click(viewTenantCheckbox);

      expect(viewTenantCheckbox).toBeChecked();
      expect(createTenantCheckbox).toBeChecked();
    });

    it('allows disabling dependency when only one dependent is removed', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Enable both create permissions
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
      const createPlanCheckbox = screen.getByRole('checkbox', {name: /create plan/i});

      await user.click(createTenantCheckbox);
      await user.click(createPlanCheckbox);

      // Disable create tenant first
      await user.click(createTenantCheckbox);

      // Now view tenant should be able to be disabled
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
      await user.click(viewTenantCheckbox);

      expect(viewTenantCheckbox).not.toBeChecked();
      expect(createTenantCheckbox).not.toBeChecked();
      expect(createPlanCheckbox).toBeChecked(); // Should still be checked
    });
  });

  describe('Group Toggle Advanced Scenarios', () => {
    it('handles group toggle functionality correctly', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Enable tenant group
      const tenantGroupCheckbox = screen.getAllByRole('checkbox')[1]; // Skip "Enable all"
      await user.click(tenantGroupCheckbox);

      // All tenant permissions should be checked
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});

      expect(createTenantCheckbox).toBeChecked();
      expect(viewTenantCheckbox).toBeChecked();

      // Disable tenant group
      await user.click(tenantGroupCheckbox);

      // All tenant permissions should be unchecked
      expect(createTenantCheckbox).not.toBeChecked();
      expect(viewTenantCheckbox).not.toBeChecked();
    });

    it('handles partial group selection correctly', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Enable only one permission in tenant group
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
      await user.click(createTenantCheckbox);

      // Both create and view should be checked due to dependency
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
      expect(createTenantCheckbox).toBeChecked();
      expect(viewTenantCheckbox).toBeChecked();

      // Group checkbox should reflect the state
      const tenantGroupCheckbox = screen.getAllByRole('checkbox')[1];
      expect(tenantGroupCheckbox).toBeChecked(); // All items in group are now checked
    });
  });

  describe('State Management Edge Cases', () => {
    it('handles rapid successive permission changes', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});

      // Rapidly toggle permissions
      await user.click(createTenantCheckbox);
      await user.click(createTenantCheckbox);
      await user.click(createTenantCheckbox);

      // Final state should be consistent
      expect(createTenantCheckbox).toBeChecked();
      expect(viewTenantCheckbox).toBeChecked();
    });

    it('maintains state consistency when toggling enable all multiple times', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      const enableAllCheckbox = screen.getByRole('checkbox', {name: /enable all/i});

      // Toggle enable all multiple times
      await user.click(enableAllCheckbox); // Enable all
      await user.click(enableAllCheckbox); // Disable all
      await user.click(enableAllCheckbox); // Enable all again

      // All checkboxes should be checked
      const allCheckboxes = screen.getAllByRole('checkbox');
      allCheckboxes.forEach(checkbox => {
        if (checkbox !== enableAllCheckbox) {
          expect(checkbox).toBeChecked();
        }
      });
    });

    it('handles initial state with complex dependencies', () => {
      const initialChecked = ['CreateTenant', 'CreatePlan'];
      render(<RolePermissions initialChecked={initialChecked} onChangePermission={mockOnChangePermission} />);

      // Both create permissions and their dependencies should be checked
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
      const createPlanCheckbox = screen.getByRole('checkbox', {name: /create plan/i});
      const viewPlanCheckbox = screen.getByRole('checkbox', {name: /view plan/i});

      expect(createTenantCheckbox).toBeChecked();
      expect(viewTenantCheckbox).toBeChecked();
      expect(createPlanCheckbox).toBeChecked();
      expect(viewPlanCheckbox).toBeChecked();
    });
  });

  describe('UI Interaction Edge Cases', () => {
    it('handles group checkbox clicks correctly', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Click on the group checkbox
      const tenantGroupCheckbox = screen.getAllByRole('checkbox')[1];
      await user.click(tenantGroupCheckbox);

      // Permissions should be enabled
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});

      expect(createTenantCheckbox).toBeChecked();
      expect(viewTenantCheckbox).toBeChecked();
    });

    it('maintains proper keyboard navigation', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Tab through elements
      await user.tab(); // Focus on enable all
      const enableAllCheckbox = screen.getByRole('checkbox', {name: /enable all/i});
      expect(enableAllCheckbox).toHaveFocus();

      await user.tab(); // Focus on first accordion
      const firstAccordion = screen.getByRole('button', {name: /tenant/i});
      expect(firstAccordion).toHaveFocus();
    });
  });

  describe('Callback and State Reporting', () => {
    it('reports correct permissions array order', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Enable permissions in specific order
      const viewTenantCheckbox = screen.getByRole('checkbox', {name: /view tenant/i});
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});

      await user.click(viewTenantCheckbox);
      await user.click(createTenantCheckbox);

      await waitFor(() => {
        expect(mockOnChangePermission).toHaveBeenCalledWith(
          expect.arrayContaining(['ViewTenant', 'CreateTenant']),
          false,
        );
      });
    });

    it('reports isSame correctly after returning to initial state', async () => {
      const initialChecked = ['ViewTenant'];
      const user = userEvent.setup();
      render(<RolePermissions initialChecked={initialChecked} onChangePermission={mockOnChangePermission} />);

      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});

      // Add a permission
      await user.click(createTenantCheckbox);

      await waitFor(() => {
        expect(mockOnChangePermission).toHaveBeenCalledWith(expect.any(Array), false);
      });

      // Remove the added permission to return to initial state
      await user.click(createTenantCheckbox);

      await waitFor(() => {
        expect(mockOnChangePermission).toHaveBeenCalledWith(['ViewTenant'], true);
      });
    });

    it('handles onChangePermission being undefined', async () => {
      const user = userEvent.setup();
      render(<RolePermissions />);

      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});

      // Should not throw error when callback is undefined
      expect(() => user.click(createTenantCheckbox)).not.toThrow();
    });
  });

  describe('Empty State and Error Handling', () => {
    it('handles component without crashing when no permissions provided', () => {
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Should render the basic structure
      expect(screen.getByRole('checkbox', {name: /enable all/i})).toBeInTheDocument();
    });

    it('handles undefined callback gracefully', () => {
      render(<RolePermissions />);

      // Should render without crashing
      expect(screen.getByRole('checkbox', {name: /enable all/i})).toBeInTheDocument();
    });
  });

  describe('Advanced Permission Logic', () => {
    it('handles initial state comparison with complex scenarios', () => {
      const initialChecked = ['CreateTenant', 'ViewPlan'];
      render(<RolePermissions initialChecked={initialChecked} onChangePermission={mockOnChangePermission} />);

      // Should report isSame as true initially since dependencies are resolved
      expect(mockOnChangePermission).toHaveBeenCalledWith(
        expect.arrayContaining(['CreateTenant', 'ViewTenant', 'ViewPlan']),
        true,
      );
    });

    it('handles cross-category dependencies correctly', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // Enable create plan (which depends on view plan)
      const createPlanCheckbox = screen.getByRole('checkbox', {name: /create plan/i});
      await user.click(createPlanCheckbox);

      // Plan permissions should be checked
      const viewPlanCheckbox = screen.getByRole('checkbox', {name: /view plan/i});
      expect(createPlanCheckbox).toBeChecked();
      expect(viewPlanCheckbox).toBeChecked();

      // Verify that the callback was called with the correct permissions
      await waitFor(() => {
        expect(mockOnChangePermission).toHaveBeenCalledWith(expect.arrayContaining(['CreatePlan', 'ViewPlan']), false);
      });
    });
  });

  describe('Performance and Optimization', () => {
    it('handles large number of rapid state changes efficiently', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      const enableAllCheckbox = screen.getByRole('checkbox', {name: /enable all/i});

      // Perform many rapid changes
      for (let i = 0; i < 5; i++) {
        await user.click(enableAllCheckbox);
      }

      // Final state should be consistent (odd number of clicks = enabled)
      const allCheckboxes = screen.getAllByRole('checkbox');
      allCheckboxes.forEach(checkbox => {
        if (checkbox !== enableAllCheckbox) {
          expect(checkbox).toBeChecked();
        }
      });
    });

    it('maintains referential stability of initial state', async () => {
      const initialChecked = ['ViewTenant'];
      const user = userEvent.setup();
      render(<RolePermissions initialChecked={initialChecked} onChangePermission={mockOnChangePermission} />);

      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});

      // Make changes and return to initial state multiple times
      await user.click(createTenantCheckbox); // Add permission
      await user.click(createTenantCheckbox); // Remove permission (back to initial)

      await user.click(createTenantCheckbox); // Add again
      await user.click(createTenantCheckbox); // Remove again (back to initial)

      // Should consistently report isSame as true when back to initial state
      await waitFor(() => {
        expect(mockOnChangePermission).toHaveBeenCalledWith(['ViewTenant'], true);
      });
    });
  });

  describe('Accessibility and User Experience', () => {
    it('maintains proper checkbox states for screen readers', async () => {
      const user = userEvent.setup();
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      const enableAllCheckbox = screen.getByRole('checkbox', {name: /enable all/i});
      const createTenantCheckbox = screen.getByRole('checkbox', {name: /create tenant/i});

      // Enable one permission - enable all should be indeterminate
      await user.click(createTenantCheckbox);

      expect(enableAllCheckbox).toHaveAttribute('data-indeterminate', 'true');
      expect(enableAllCheckbox).not.toBeChecked();

      // Enable all - should be checked and not indeterminate
      await user.click(enableAllCheckbox);

      expect(enableAllCheckbox).toBeChecked();
      expect(enableAllCheckbox).not.toHaveAttribute('data-indeterminate', 'true');
    });

    it('provides correct aria labels and roles', () => {
      render(<RolePermissions onChangePermission={mockOnChangePermission} />);

      // All checkboxes should have proper roles
      const checkboxes = screen.getAllByRole('checkbox');
      expect(checkboxes.length).toBeGreaterThan(0);

      checkboxes.forEach(checkbox => {
        expect(checkbox).toHaveAttribute('type', 'checkbox');
      });
    });
  });
});
