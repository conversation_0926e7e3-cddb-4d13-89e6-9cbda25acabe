import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  BoxProps,
  Checkbox,
  Divider,
  FormControlLabel,
  Typography,
} from '@mui/material';
import categorizedPermissions, {CategoryPermission, PermissionCategory} from 'Helpers/categorizedPermissions';
import React, {useEffect, useMemo, useState} from 'react';

interface RolePermissionProps extends BoxProps {
  initialChecked?: string[]; // pass initial keys (like ['plan:edit', 'tenant:view'])
  onChangePermission?: (permissions: string[], isSame: boolean) => void;
}

/**
 * RolePermissions component for managing and displaying role-based permissions with dependency logic.
 *
 * This component renders a categorized list of permissions, allowing users to enable or disable individual permissions,
 * entire categories, or all permissions at once. It automatically handles permission dependencies, ensuring that
 * enabling or disabling a permission respects its required dependencies and relationships.
 *
 * Props:
 * - initialChecked: string[]
 *   Array of backend permission identifiers that should be initially enabled.
 * - onChangePermission?: (selectedPermissions: string[], isSameAsInitial: boolean) => void
 *   Callback invoked when the selected permissions change. Provides the current list of enabled backend permissions
 *   and a boolean indicating if the selection matches the initial state.
 * - sx?: SxProps
 *   Optional style overrides for the root container.
 * - ...rest: any
 *   Additional props passed to the root Box component.
 *
 * Features:
 * - Displays permissions grouped by category, with expand/collapse UI.
 * - Supports enabling/disabling all permissions, or all permissions in a category.
 * - Handles permission dependencies: enabling a permission automatically enables its required dependencies.
 * - Prevents disabling permissions if they are required by other active permissions.
 * - Tracks and compares the current selection to the initial selection.
 *
 * Usage:
 * ```tsx
 * <RolePermissions
 *   initialChecked={['perm.read', 'perm.write']}
 *   onChangePermission={(perms, isInitial) => { ... }}
 * />
 * ```
 */
const RolePermissions: React.FC<RolePermissionProps> = ({initialChecked, onChangePermission, sx, ...rest}) => {
  // ---- Flatten & maps for fast lookups ----
  const allItems = useMemo<CategoryPermission[]>(() => Object.values(categorizedPermissions).flat(), []);

  const permissionToItems = useMemo(() => {
    const m = new Map<string, CategoryPermission[]>();
    for (const it of allItems) {
      const arr = m.get(it.permission) ?? [];
      arr.push(it);
      m.set(it.permission, arr);
    }
    return m;
  }, [allItems]);

  const isPermissionActive = (perm: string, state = checked) => {
    const items = permissionToItems.get(perm) ?? [];
    return items.some(it => !!state[it.key]);
  };

  // enable all UI items that represent a backend permission, then recurse through its deps
  const enablePermissionAndDeps = (perm: string, draft: Record<string, boolean>) => {
    const items = permissionToItems.get(perm) ?? [];
    // mark every UI item for this permission as checked
    for (const it of items) draft[it.key] = true;

    // pick one representative (first) to follow the dependency chain
    const representative = items[0];
    if (representative?.dependent?.length) {
      for (const dep of representative.dependent) {
        if (!isPermissionActive(dep, draft)) {
          enablePermissionAndDeps(dep, draft);
        }
      }
    }
  };

  const refInitialState = React.useRef<Record<string, boolean> | null>(null);

  // ---- UI state keyed by *UI key* ----
  const [checked, setChecked] = useState<Record<string, boolean>>(() => {
    if (!initialChecked) return {};
    const draft: Record<string, boolean> = {};
    for (const perm of initialChecked) {
      enablePermissionAndDeps(perm, draft);
    }
    refInitialState.current = draft;
    return draft;
  }); // start all OFF

  const isKeyChecked = (key: string, state = checked) => !!state[key];

  // ---- Single item toggle (with dependency rules) ----
  // Helper to check if permission is still provided by other checked items
  const isPermissionStillProvided = (perm: string, itemKey: string, prev: Record<string, boolean>) =>
    (permissionToItems.get(perm) ?? []).some(it => it.key !== itemKey && prev[it.key]);

  // Helper to build set of required permissions from all other active items
  const getRequiredPerms = (itemKey: string, prev: Record<string, boolean>) => {
    const requiredPerms = new Set<string>();
    for (const other of allItems) {
      if (other.key !== itemKey && prev[other.key]) {
        for (const d of other.dependent ?? []) requiredPerms.add(d);
      }
    }
    return requiredPerms;
  };

  const handleToggleItem = (item: CategoryPermission, value: boolean, force = false) => {
    setChecked(prev => {
      const draft = {...prev};

      if (value) {
        draft[item.key] = true;
        for (const dep of item.dependent ?? []) {
          enablePermissionAndDeps(dep, draft);
        }
        return draft;
      }

      if (!force) {
        const perm = item.permission;
        const stillProvided = isPermissionStillProvided(perm, item.key, prev);

        if (!stillProvided) {
          const requiredPerms = getRequiredPerms(item.key, prev);
          if (requiredPerms.has(perm)) {
            return prev;
          }
        }
      }

      draft[item.key] = false;
      return draft;
    });
  };

  // ---- Group toggle ----
  const handleEnableGroup = (category: PermissionCategory) => {
    const items = categorizedPermissions[category];

    setChecked(prev => {
      let draft = {...prev};
      // turn ON every child and honor dependencies
      for (const it of items) {
        draft[it.key] = true;
        for (const dep of it.dependent ?? []) {
          enablePermissionAndDeps(dep, draft);
        }
      }
      return draft;
    });
  };

  const handleDisableGroup = (category: PermissionCategory) => {
    const items = categorizedPermissions[category];

    setChecked(prev => {
      let draft = {...prev};
      // attempt to turn OFF children, but use same rules as handleToggleItem
      for (const it of items) {
        // reuse handleToggleItem logic instead of force
        const tmp = {...draft};
        draft = handleToggleItemInternal(it, false, tmp);
      }
      return draft;
    });
  };

  // Helper to enable an item and its dependencies
  const enableItemWithDependencies = (item: CategoryPermission, draft: Record<string, boolean>) => {
    draft[item.key] = true;
    for (const dep of item.dependent ?? []) {
      enablePermissionAndDeps(dep, draft);
    }
  };

  // Helper to check if disabling an item would break dependencies
  const canDisableItem = (item: CategoryPermission, prev: Record<string, boolean>) => {
    const perm = item.permission;
    const stillProvided = (permissionToItems.get(perm) ?? []).some(it => it.key !== item.key && prev[it.key]);

    if (stillProvided) {
      return true; // Other items still provide this permission
    }

    // Check if this permission is required by other active items
    const requiredPerms = new Set<string>();
    for (const other of allItems) {
      if (other.key !== item.key && prev[other.key]) {
        for (const d of other.dependent ?? []) {
          requiredPerms.add(d);
        }
      }
    }

    return !requiredPerms.has(perm);
  };

  // Extracted logic so we can reuse in group
  const handleToggleItemInternal = (
    item: CategoryPermission,
    value: boolean,
    prev: Record<string, boolean>,
    force = false,
  ) => {
    const draft = {...prev};

    if (value) {
      enableItemWithDependencies(item, draft);
      return draft;
    }

    // For disabling: check if we can safely disable unless forced
    if (!force && !canDisableItem(item, prev)) {
      return prev; // don't disable, still needed
    }

    draft[item.key] = false;
    return draft;
  };

  // ---- Enable all / Disable all ----
  const handleEnableAll = () => {
    // everything ON (dependencies inherently satisfied because everything is on)
    const next: Record<string, boolean> = {};
    for (const it of allItems) next[it.key] = true;
    setChecked(next);
  };

  const handleDisableAll = () => {
    // everything OFF
    setChecked({});
  };

  // ---- Derived active backend permissions ----
  const selectedPermissions = useMemo(() => {
    const active = new Set<string>();
    for (const it of allItems) {
      if (checked[it.key]) {
        active.add(it.permission); // collect backend permission
      }
    }
    return active;
  }, [checked, allItems]);

  // ---- Compare with initialChecked ----
  const isSameAsInitial = useMemo(() => {
    const initial = refInitialState.current;
    if (!initial) return false;
    // Every key in A must exist in B and be true
    for (const key of Object.keys(initial)) {
      if (!checked[key]) {
        return false;
      }
    }

    // Every extra key in B must be false
    for (const key of Object.keys(checked)) {
      if (!(key in initial) && checked[key] === true) {
        return false;
      }
    }

    return true;
  }, [checked]);

  useEffect(() => {
    onChangePermission?.(Array.from(selectedPermissions), isSameAsInitial ?? false);
  }, [selectedPermissions, onChangePermission, isSameAsInitial]);

  // ---- UI derived states ----
  const allKeys = allItems.map(i => i.key);
  const allChecked = allKeys.length > 0 && allKeys.every(k => isKeyChecked(k));
  const someChecked = allKeys.some(k => isKeyChecked(k));

  return (
    <Box sx={{width: '100%', ...sx}} {...rest}>
      {/* Global enable all */}
      <FormControlLabel
        control={
          <Checkbox
            checked={allChecked}
            indeterminate={!allChecked && someChecked}
            onChange={e => (e.target.checked ? handleEnableAll() : handleDisableAll())}
          />
        }
        label="Enable all"
      />

      {Object.entries(categorizedPermissions).map(([category, items]) => {
        const children = items.map(it => isKeyChecked(it.key));
        const allChildrenChecked = children.length > 0 && children.every(Boolean);
        const someChildrenChecked = children.some(Boolean);

        return (
          <Accordion
            key={category}
            elevation={0}
            defaultExpanded
            sx={{
              border: '1px solid',
              borderColor: 'body.200',
              borderRadius: '0.375rem',
              overflow: 'hidden',
              mb: 1,
            }}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              sx={{
                backgroundColor: 'white.main',
                '& .MuiAccordionSummary-content': {my: 0},
              }}
            >
              <FormControlLabel
                onClick={e => e.stopPropagation()}
                onFocus={e => e.stopPropagation()}
                control={
                  <Checkbox
                    checked={allChildrenChecked}
                    indeterminate={!allChildrenChecked && someChildrenChecked}
                    onChange={e =>
                      e.target.checked
                        ? handleEnableGroup(category as PermissionCategory)
                        : handleDisableGroup(category as PermissionCategory)
                    }
                  />
                }
                label={<Typography>{category}</Typography>}
              />
            </AccordionSummary>
            <Divider />
            <AccordionDetails>
              <Box sx={{display: 'flex', gap: 2, flexWrap: 'wrap'}}>
                {items.length > 0 ? (
                  items.map(item => (
                    <FormControlLabel
                      key={item.key + item.label}
                      control={
                        <Checkbox
                          checked={isKeyChecked(item.key)}
                          onChange={e => handleToggleItem(item, e.target.checked)}
                        />
                      }
                      label={item.label}
                    />
                  ))
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    No specific permissions
                  </Typography>
                )}
              </Box>
            </AccordionDetails>
          </Accordion>
        );
      })}
    </Box>
  );
};

export default RolePermissions;
