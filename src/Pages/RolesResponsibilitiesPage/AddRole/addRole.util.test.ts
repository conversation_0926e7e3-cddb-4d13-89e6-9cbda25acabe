import {useLocation} from 'react-router';
import {describe, expect, it, vi} from 'vitest';
import {useAddRoleLocationMeta} from './addRole.util';

vi.mock('react-router', () => ({
  useLocation: vi.fn(),
}));

describe('useAddRoleLocationMeta', () => {
  it('returns role from location state', () => {
    const mockRole = {id: '1', name: 'TestRole'};
    (useLocation as any).mockReturnValue({state: {role: mockRole}});
    const result = useAddRoleLocationMeta();
    expect(result.role).toEqual(mockRole);
  });

  it('returns undefined if no role in state', () => {
    (useLocation as any).mockReturnValue({state: {}});
    const result = useAddRoleLocationMeta();
    expect(result.role).toBeUndefined();
  });

  it('returns undefined if no state', () => {
    (useLocation as any).mockReturnValue({});
    const result = useAddRoleLocationMeta();
    expect(result.role).toBeUndefined();
  });
});
