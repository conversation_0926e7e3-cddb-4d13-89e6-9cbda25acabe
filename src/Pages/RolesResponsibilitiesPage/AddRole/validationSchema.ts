import {Integers} from 'Helpers/integers';
import {debounce} from 'lodash';
import {useLazyGetRoleByNameQuery} from 'redux/app/tenantManagementApiSlice';
import * as yup from 'yup';

const MIN_ROLE_NAME_LENGTH = 3;
const MAX_ROLE_NAME_LENGTH = 50;
//Role Name should only contain letters
const ROLE_NAME_REGEX = /^[A-Za-z ]+$/;

interface ValidationProps {
  initialRoleName?: string;
}

/**
 * Custom hook that returns a Yup validation schema for adding a new role.
 *
 * The schema validates the `roleName` field with the following rules:
 * - Required and trimmed.
 * - Minimum and maximum length constraints.
 * - Must match a specific regex pattern (letters only).
 * - Checks for uniqueness against existing roles using a debounced API call.
 * - Allows the initial role name to bypass uniqueness check.
 *
 * @param initialRoleName - The initial role name to compare for uniqueness.
 * @returns An object containing the `addRoleValidationSchema` Yup schema.
 */
export const useValidationSchema = ({initialRoleName}: ValidationProps) => {
  const [getRoleByName] = useLazyGetRoleByNameQuery();

  const checkRoleNameUnique = debounce(async (roleName: string, resolve: (v: boolean) => void) => {
    try {
      const {data: roles = []} = await getRoleByName({name: roleName, ignoreCase: true});
      resolve(roles.length == 0);
    } catch {
      resolve(false);
    }
  }, Integers.FiveHundred);

  const addRoleValidationSchema = yup.object({
    roleName: yup
      .string()
      .trim()
      .required('Role name is required')
      .min(MIN_ROLE_NAME_LENGTH, `Role name should have at least ${MIN_ROLE_NAME_LENGTH} characters`)
      .max(MAX_ROLE_NAME_LENGTH, `Role name should have at most ${MAX_ROLE_NAME_LENGTH} characters`)
      .matches(ROLE_NAME_REGEX, 'Role name should only contain letters.')
      .test('check-unique-email', 'Role name already exists', value => {
        if (!value) return false;
        if (value === initialRoleName) return true;
        return new Promise(resolve => {
          checkRoleNameUnique(value, resolve);
        });
      }),
  });
  return {
    addRoleValidationSchema,
  };
};
