// @vitest-environment jsdom
import {render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import NoRoleView from './NoRoleView';

vi.mock('Assets/NoRoleIcon', () => ({
  __esModule: true,
  default: (props: any) => <svg data-testid="no-role-icon" {...props} />,
}));

vi.mock('react-router', () => ({
  useNavigate: vi.fn(),
}));

describe('NoRoleView', () => {
  it('renders the icon, heading, and subtext', () => {
    render(<NoRoleView />);
    expect(screen.getByTestId('no-role-icon')).toBeInTheDocument();
    expect(screen.getByText('You haven’t created any roles yet')).toBeInTheDocument();
    expect(screen.getByText(/Create a role to define permissions and manage/i)).toBeInTheDocument();
    expect(screen.getByText(/user access effectively\./i)).toBeInTheDocument();
  });
});
