// @vitest-environment jsdom
import {createTheme, ThemeProvider} from '@mui/material/styles';
import '@testing-library/jest-dom';
import {render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {beforeEach, vi} from 'vitest';
import {AddRoleButton} from './AddRoleButton';

const mockNavigate = vi.fn();

vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const theme = createTheme();

const renderWithCustomTheme = (component: React.ReactElement) => {
  return render(<ThemeProvider theme={theme}>{component}</ThemeProvider>);
};

describe('AddRoleButton', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render the button with the correct text and icon', () => {
    renderWithCustomTheme(<AddRoleButton />);
    expect(screen.getByText('Add Role')).toBeInTheDocument();
    expect(screen.getByTestId('AddIcon')).toBeInTheDocument();
  });

  it('should call navigate with the correct route when clicked', async () => {
    const user = userEvent.setup();
    renderWithTheme(<AddRoleButton />);

    const button = screen.getByTestId('add-role-button');

    // Verify the button is clickable
    expect(button).toBeEnabled();

    await user.click(button);

    // Check if navigate was called at all first
    waitFor(() => expect(mockNavigate).toHaveBeenCalled());
    waitFor(() => expect(mockNavigate).toHaveBeenCalledWith('/add-role', {}));
  });

  it('should apply custom sx props', () => {
    renderWithCustomTheme(<AddRoleButton sx={{color: 'red'}} />);
    const button = screen.getByTestId('add-role-button');
    expect(button).toHaveStyle('color: rgb(255, 0, 0)');
  });
});
