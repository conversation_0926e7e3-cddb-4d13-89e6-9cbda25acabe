import AddIcon from '@mui/icons-material/Add';
import BlueButton from 'Components/BlueButton/BlueButton';
import {BtnProps} from 'Components/Button/Button';
import {FC} from 'react';
import {useNavigate} from 'react-router';
import {RouteNames} from 'Routes/routeNames';

/**
 * Renders a button that navigates to the "Add Role" page when clicked.
 *
 * @param sx - Optional style overrides for the button.
 * @param props - Additional props passed to the underlying BlueButton component.
 *
 * @returns A BlueButton component with an Add icon and "Add Role" label.
 *
 * @example
 * <AddRoleButton sx={{ marginTop: 2 }} />
 */
export const AddRoleButton: FC<BtnProps> = ({sx, ...props}) => {
  const navigate = useNavigate();
  return (
    <BlueButton
      data-testid="add-role-button"
      onClick={() => {
        navigate(RouteNames.ADD_ROLE, {});
      }}
      sx={{height: '2.6rem', ...sx}}
      {...props}
    >
      <AddIcon sx={{height: '1rem', width: '1rem'}} /> Add Role
    </BlueButton>
  );
};
