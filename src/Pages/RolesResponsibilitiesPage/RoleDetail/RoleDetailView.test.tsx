import {fireEvent, screen, waitFor} from '@testing-library/react';
import {memoryRenderWithTheme} from 'TestHelper/TestHelper';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import RoleDetailView from './RoleDetailView';

const mockNavigate = vi.fn();
const mockUseRoleDetailState = vi.fn();
const mockUseGetRoleDetailLocationMetaData = vi.fn();
const mockHasPermission = vi.fn();

vi.mock('react-router', async () => {
  const actual = await vi.importActual('react-router');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

vi.mock('./hook/useRoleDetailState', () => ({
  __esModule: true,
  default: () => mockUseRoleDetailState(),
}));

vi.mock('./roleDetail.util', () => ({
  useGetRoleDetailLocationMetaData: () => mockUseGetRoleDetailLocationMetaData(),
}));

vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', () => ({
  usePermissions: () => ({
    hasPermission: mockHasPermission,
  }),
}));

vi.mock('Components/PermissionView/PermissionView', () => ({
  __esModule: true,
  default: ({permissions}: {permissions: string[]}) => <div data-testid="permissions-ui">{permissions.join(',')}</div>,
}));

vi.mock('Components/RoleStatusChip/RoleStatusChip', () => ({
  __esModule: true,
  default: ({role}: {role: any}) => <span data-testid="role-status-chip">{role?.status}</span>,
}));

vi.mock('Components/Breadcrumb/Breadcrumb', () => ({
  __esModule: true,
  default: ({items}: {items: any[]}) => <nav data-testid="breadcrumb">{items.map(i => i.label).join(' > ')}</nav>,
}));

vi.mock('Components/BackdropLoader', () => ({
  __esModule: true,
  default: () => <div data-testid="backdrop-loader" />,
}));

vi.mock('Components/TableCellBox', () => ({
  TableCellBoxWithToolTip: ({children}: any) => <span data-testid="created-by">{children}</span>,
}));

describe('RoleDetailView', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockUseGetRoleDetailLocationMetaData.mockReturnValue({
      roleView: {
        roleId: '1',
        name: 'Admin',
        userCount: 10,
        createdOn: '2025-01-01',
        modifiedOn: '2025-08-01',
        status: 'Active',
        permissions: ['VIEW_ROLE', 'EDIT_ROLE'],
        deleted: false,
      },
    });
    mockUseRoleDetailState.mockReturnValue({
      role: {
        name: 'Admin',
        permissions: ['VIEW_ROLE', 'EDIT_ROLE'],
      },
      loadingRole: false,
      roleError: false,
      createdByUser: {firstName: 'John', lastName: 'Doe'},
    });
    mockHasPermission.mockReturnValue(true);
  });

  it('renders loader when loadingRole is true', () => {
    mockUseRoleDetailState.mockReturnValue({
      role: null,
      loadingRole: true,
      roleError: false,
      createdByUser: null,
    });
    memoryRenderWithTheme(<RoleDetailView />);
    expect(screen.getByTestId('backdrop-loader')).toBeInTheDocument();
  });

  it('renders error when role is null', () => {
    mockUseGetRoleDetailLocationMetaData.mockReturnValue({
      roleView: undefined,
    });
    mockUseRoleDetailState.mockReturnValue({
      role: null,
      loadingRole: false,
      roleError: false,
      createdByUser: null,
    });
    memoryRenderWithTheme(<RoleDetailView />);
    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });

  it('renders role details and breadcrumbs', () => {
    memoryRenderWithTheme(<RoleDetailView />);
    expect(screen.getByText('Admin')).toBeInTheDocument();
    expect(screen.getByTestId('breadcrumb')).toHaveTextContent('Role & Permissions > Admin');
    expect(screen.getByTestId('role-status-chip')).toHaveTextContent('Active');
    expect(screen.getByText('Role information')).toBeInTheDocument();
    expect(screen.getByText('No. of users')).toBeInTheDocument();
    expect(screen.getByText('10')).toBeInTheDocument();
    expect(screen.getByText('Created on')).toBeInTheDocument();
    expect(screen.getByText('Modified on')).toBeInTheDocument();
  });

  it('renders permissions section', () => {
    memoryRenderWithTheme(<RoleDetailView />);
    expect(screen.getByTestId('permissions-ui')).toHaveTextContent('VIEW_ROLE,EDIT_ROLE');
  });

  it('renders created by section if permission exists', () => {
    memoryRenderWithTheme(<RoleDetailView />);
    expect(screen.getByTestId('created-by')).toHaveTextContent('John Doe');
  });

  it('calls navigate(-1) when Close button is clicked', () => {
    memoryRenderWithTheme(<RoleDetailView />);
    const closeBtn = screen.getByRole('button', {name: 'Close'});
    fireEvent.click(closeBtn);
    waitFor(() => expect(mockNavigate).toHaveBeenCalledWith(-1));
  });
});
