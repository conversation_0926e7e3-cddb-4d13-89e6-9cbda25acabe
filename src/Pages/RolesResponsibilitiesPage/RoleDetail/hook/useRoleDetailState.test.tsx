// @vitest-environment jsdom
import {renderHook, waitFor} from '@testing-library/react';
import {useGetUserByIdQuery, useLazyGetRoleByIdQuery} from 'redux/app/tenantManagementApiSlice';
import {IRole} from 'redux/app/types';
import {vi} from 'vitest';
import useRoleDetailState from './useRoleDetailState';

vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useLazyGetRoleByIdQuery: vi.fn(),
  useGetUserByIdQuery: vi.fn(),
}));

const mockGetRole = vi.fn();

describe('useRoleDetailState', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useLazyGetRoleByIdQuery as jest.Mock).mockReturnValue([mockGetRole, {isLoading: false, error: null}]);
    (useGetUserByIdQuery as jest.Mock).mockReturnValue({
      data: null,
      isLoading: false,
      error: null,
    });
  });

  it('should return initial state correctly', () => {
    const {result} = renderHook(() => useRoleDetailState());

    expect(result.current.role).toBeNull();
    expect(result.current.createdByUser).toBeNull();
    expect(result.current.loadingRole).toBe(false);
    expect(result.current.roleError).toBeNull();
    expect(result.current.isCreatedByLoading).toBe(false);
    expect(result.current.createdByError).toBeNull();
  });

  it('should not fetch role if roleId is not provided', () => {
    renderHook(() => useRoleDetailState());
    expect(mockGetRole).not.toHaveBeenCalled();
  });

  it('should fetch role and createdBy user when roleId is provided', async () => {
    const mockRole: IRole = {
      id: 'role1',
      name: 'Admin',
      permissions: [],
      status: 1,
      createdBy: 'user1',
      createdOn: '2023-01-01',
      deleted: false,
      deletedOn: '',
      deletedBy: '',
      modifiedOn: '2023-01-01',
      modifiedBy: 'user1',
      description: 'Admin role',
      tenantId: 'tenant1',
      allowedClients: [],
      roleType: 1,
    };
    const mockUser = {id: 'user1', name: 'Test User'};
    mockGetRole.mockReturnValue({unwrap: () => Promise.resolve([mockRole])});
    (useGetUserByIdQuery as jest.Mock).mockReturnValue({
      data: [mockUser],
      isLoading: false,
      error: null,
    });

    const {result, rerender} = renderHook(({roleId}) => useRoleDetailState(roleId), {
      initialProps: {roleId: 'role1'},
    });

    await waitFor(() => expect(result.current.role).toEqual(mockRole));
    rerender({roleId: 'role1'});
    await waitFor(() => expect(result.current.createdByUser).toEqual(mockUser));
  });

  it('should handle role not found', async () => {
    mockGetRole.mockReturnValue({unwrap: () => Promise.resolve([])});
    const {result} = renderHook(() => useRoleDetailState('role1'));

    await waitFor(() => {
      expect(result.current.role).toBeNull();
    });
  });

  it('should set role to null if api returns empty array', async () => {
    mockGetRole.mockReturnValue({unwrap: () => Promise.resolve([])});

    const {result} = renderHook(() => useRoleDetailState('role1'));

    await waitFor(() => {
      expect(result.current.role).toBeNull();
    });
  });

  it('should pass includeAll: false when activated is true', async () => {
    mockGetRole.mockReturnValue({unwrap: () => Promise.resolve([])});
    renderHook(() => useRoleDetailState('role1'));

    await waitFor(() => {
      expect(mockGetRole).toHaveBeenCalledWith({roleID: 'role1', includeAll: true});
    });
  });

  it('should return loading states correctly', () => {
    (useLazyGetRoleByIdQuery as jest.Mock).mockReturnValue([mockGetRole, {isLoading: true, error: null}]);
    const {result} = renderHook(() => useRoleDetailState('role1'));
    expect(result.current.loadingRole).toBe(true);
  });

  it('should return error states correctly', () => {
    const roleError = new Error('Failed to fetch role');
    (useLazyGetRoleByIdQuery as jest.Mock).mockReturnValue([mockGetRole, {isLoading: false, error: roleError}]);
    const {result} = renderHook(() => useRoleDetailState('role1'));
    expect(result.current.roleError).toBe(roleError);
  });
});
