import {useEffect, useState} from 'react';
import {useGetUserByIdQuery, useLazyGetRoleByIdQuery} from 'redux/app/tenantManagementApiSlice';
import {IRole} from 'redux/app/types';

/**
 * Custom hook to manage the state and data fetching for a role's details.
 *
 * @param roleId - The unique identifier of the role to fetch details for.
 * @param activated - Optional flag to determine whether to include all roles or only activated ones.
 * @returns An object containing:
 * - `role`: The fetched role details or `null` if not found.
 * - `createdByUser`: The user who created the role, or `null` if not available.
 * - `loadingRole`: <PERSON><PERSON>an indicating if the role data is currently loading.
 * - `roleError`: Any error encountered while fetching the role data.
 * - `isCreatedByLoading`: Boolean indicating if the creator user data is currently loading.
 * - `createdByError`: Any error encountered while fetching the creator user data.
 */
const useRoleDetailState = (roleId?: string) => {
  const [getUserRole, {isLoading: loadingRole, error: roleError}] = useLazyGetRoleByIdQuery();
  const [role, setRole] = useState<IRole | null>(null);
  const {
    data: createdByUsers,
    isLoading: isCreatedByLoading,
    error: createdByError,
  } = useGetUserByIdQuery(role?.createdBy ?? '', {skip: !role, refetchOnMountOrArgChange: true});

  const createdByUser = createdByUsers && createdByUsers.length > 0 ? createdByUsers[0] : null;

  useEffect(() => {
    if (roleId && roleId.length > 0) {
      getUserRole({roleID: roleId, includeAll: true})
        .unwrap()
        .then(detail => {
          if (detail.length === 0) {
            setRole(null);
            return;
          }
          setRole(detail[0]);
        });
    }
  }, [roleId, getUserRole]);

  return {
    role,
    createdByUser,
    loadingRole,
    roleError,
    isCreatedByLoading,
    createdByError,
  };
};

export default useRoleDetailState;
