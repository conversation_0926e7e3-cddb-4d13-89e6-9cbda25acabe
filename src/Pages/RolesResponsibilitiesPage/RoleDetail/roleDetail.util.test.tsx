import {renderHook} from '@testing-library/react';
import {MemoryRouter} from 'react-router';
import {useGetRoleDetailLocationMetaData} from './roleDetail.util';

const mockRole = {id: 'r1', name: 'Admin'};

function Wrapper({children}: {children: React.ReactNode}) {
  return <MemoryRouter initialEntries={[{pathname: '/role-detail', state: {role: mockRole}}]}>{children}</MemoryRouter>;
}

describe('useGetRoleDetailLocationMetaData', () => {
  it('should return roleView from location state', () => {
    const {result} = renderHook(() => useGetRoleDetailLocationMetaData(), {wrapper: Wrapper});
    expect(result.current.roleView).toEqual(mockRole);
  });

  it('should return undefined if role is not in state', () => {
    function EmptyWrapper({children}: {children: React.ReactNode}) {
      return <MemoryRouter initialEntries={[{pathname: '/role-detail'}]}>{children}</MemoryRouter>;
    }
    const {result} = renderHook(() => useGetRoleDetailLocationMetaData(), {wrapper: EmptyWrapper});
    expect(result.current.roleView).toBeUndefined();
  });
});
