import {useLocation} from 'react-router';
import {IRoleView} from 'redux/auth/authApiSlice';

interface RoleDetailLocationMetadata {
  roleView: IRoleView;
}

/**
 * Get the location metadata for the Role Detail page
 * @returns Location metadata
 */
export const useGetRoleDetailLocationMetaData = (): RoleDetailLocationMetadata => {
  const location = useLocation();
  return {
    roleView: location.state?.role as IRoleView,
  };
};
