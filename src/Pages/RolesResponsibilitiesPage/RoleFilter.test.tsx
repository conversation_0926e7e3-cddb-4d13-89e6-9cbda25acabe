import {LocalizationProvider} from '@mui/x-date-pickers';
import {AdapterDateFns} from '@mui/x-date-pickers/AdapterDateFns';
import {fireEvent, render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import RoleFilter, {IRoleFilter} from './RoleFilter';
import {RoleStatus} from './roles.util';

describe('RoleFilter', () => {
  const anchor = document.createElement('div');
  document.body.appendChild(anchor);

  const defaultProps = {
    open: true,
    onClose: vi.fn(),
    anchorEl: anchor,
    onFilterChange: vi.fn(),
    value: {
      status: new Set(),
      createdOnDateRange: undefined,
      modifiedDateRange: undefined,
    } as IRoleFilter,
  };

  it('renders filter popover when open', () => {
    render(<RoleFilter {...defaultProps} />);
    expect(screen.getByText('Filter')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Apply'})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: 'Close'})).toBeInTheDocument();
  });

  it('calls onClose when Close button is clicked', () => {
    render(<RoleFilter {...defaultProps} />);
    fireEvent.click(screen.getByRole('button', {name: 'Close'}));
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('calls onFilterChange and onClose when Apply is clicked with valid data', () => {
    render(<RoleFilter {...defaultProps} />);
    fireEvent.click(screen.getByRole('button', {name: 'Apply'}));
    expect(defaultProps.onFilterChange).toHaveBeenCalled();
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  it('closes popover when open is false', () => {
    render(<RoleFilter {...defaultProps} open={false} />);
    expect(screen.queryByText('Filter')).not.toBeInTheDocument();
  });

  it('renders and triggers Clear all when status is set', () => {
    const onClose = vi.fn();
    const onFilterChange = vi.fn();
    const anchor = document.createElement('div');
    document.body.appendChild(anchor);
    const props = {
      open: true,
      onClose,
      anchorEl: anchor,
      onFilterChange,
      value: {
        status: new Set([RoleStatus.ACTIVE]),
        createdOnDateRange: undefined,
        modifiedDateRange: undefined,
      },
    };
    render(
      <LocalizationProvider dateAdapter={AdapterDateFns}>
        <RoleFilter {...props} />
      </LocalizationProvider>,
    );
    const clearBtn = screen.getByText('Clear all');
    expect(clearBtn).toBeInTheDocument();
    fireEvent.click(clearBtn);
    // No error should occur, refs are cleared
  });
});
