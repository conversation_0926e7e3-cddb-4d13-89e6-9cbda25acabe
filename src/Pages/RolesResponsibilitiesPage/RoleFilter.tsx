import {Box, Divider, IconButton, Typography} from '@mui/material';
import {PopoverWithArrow} from 'Components/AppBar/PopoverWithArrow';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import DefaultCloseIcon from 'Components/DefaultDialog/DefaultCloseIcon';
import {useCallback, useEffect, useRef, useState} from 'react';
import DateRangeFilter, {
  DateRangeFilterSelectedDate,
  DateRangeOptions,
} from '../../Components/DateRangeFilter/DateRangeFilter';
import {IClearFilters} from '../../Components/FilterUtil/IClearFilters';
import FilterStatusChips, {FilterStatusChipsRef} from './StatusFilterChip/StatusFilterChip';

export interface IRoleFilter {
  status: Set<number>;
  createdOnDateRange?: DateRangeFilterSelectedDate;
  modifiedDateRange?: DateRangeFilterSelectedDate;
}

const VALID_DATE_RANGE_ERROR = 'Please select a valid date range.';

export interface RoleFilterProps<Filter> {
  open: boolean;
  onClose: () => void;
  value?: Filter;
  anchorEl?: HTMLElement | null;
  onFilterChange?: (filter: Filter) => void;
  filterView?: (
    clearRef: React.RefObject<IClearFilters | null>,
    onFilterChange?: (filter: Set<string>) => void,
    initialFilter?: Set<string>,
  ) => React.ReactNode;
}

const paddingDefault = 2; // Default padding for the popover
const buttonHeight = '2.3125rem'; // Height for the buttons

/**
 * TenantFilter component that displays a filter popover for tenants.
 * @param props The props for the component.
 * @returns {JSX.Element} The rendered TenantFilter component.
 */
const RoleFilter = <T extends RoleFilterProps<IRoleFilter>>(props: T) => {
  const {open, value: filter, anchorEl, onFilterChange, onClose} = props;
  const [selectedStatus, setSelectedStatus] = useState<Set<string>>(new Set());
  const [createOnErrorMessage, setCreateOnErrorMessage] = useState<string | undefined>();
  const [modifiedOnErrorMessage, setModifiedOnErrorMessage] = useState<string | undefined>();
  const [selectedCreatedOnRange, setSelectedCreatedOnRange] = useState<DateRangeFilterSelectedDate | undefined>(
    filter?.createdOnDateRange ?? undefined,
  );

  const [selectedModifiedOnRange, setSelectedModifiedOnRange] = useState<DateRangeFilterSelectedDate | undefined>(
    filter?.modifiedDateRange ?? undefined,
  );
  const filterStatusChipsRef = useRef<FilterStatusChipsRef>(null);
  const filterDateRangeRef = useRef<IClearFilters>(null);
  const filterModDateRangeRef = useRef<IClearFilters>(null);

  useEffect(() => {
    if (open) {
      // Reset selections when the filter is opened
      const stringFilter = filter?.status ? new Set(Array.from(filter?.status).map(String)) : new Set<string>();

      setSelectedStatus(stringFilter);
      setSelectedCreatedOnRange(filter?.createdOnDateRange);
      setSelectedModifiedOnRange(filter?.modifiedDateRange);
    }
  }, [open]);

  const applyHandle = () => {
    let hasError = false;
    if (selectedCreatedOnRange && [selectedCreatedOnRange?.startDate, selectedCreatedOnRange?.endDate].some(d => !d)) {
      setCreateOnErrorMessage(VALID_DATE_RANGE_ERROR);
      hasError = true;
    }
    if (
      selectedModifiedOnRange &&
      [selectedModifiedOnRange?.startDate, selectedModifiedOnRange?.endDate].some(d => !d)
    ) {
      setModifiedOnErrorMessage(VALID_DATE_RANGE_ERROR);
      hasError = true;
    }
    if (hasError) return;
    setCreateOnErrorMessage('');
    setModifiedOnErrorMessage('');
    onFilterChange?.({
      status: new Set(Array.from(selectedStatus).map(Number)),
      createdOnDateRange: selectedCreatedOnRange,
      modifiedDateRange: selectedModifiedOnRange,
    });
    onClose();
  };

  useEffect(() => {
    if (selectedCreatedOnRange?.dateRangeOption !== DateRangeOptions.CUSTOM) {
      setCreateOnErrorMessage('');
    }

    if (selectedModifiedOnRange?.dateRangeOption !== DateRangeOptions.CUSTOM) {
      setModifiedOnErrorMessage('');
    }
  }, [selectedCreatedOnRange, selectedModifiedOnRange]);

  const handleClearSelection = useCallback(() => {
    filterStatusChipsRef.current?.clearSelection();
    filterDateRangeRef.current?.clearSelection();
    filterModDateRangeRef.current?.clearSelection();
  }, []);

  const buildButtonSection = () => {
    return (
      <Box sx={{display: 'flex', justifyContent: 'flex-end', p: paddingDefault, pt: 1, gap: 1}}>
        <BorderButton sx={{height: buttonHeight}} onClick={onClose}>
          Close
        </BorderButton>
        <BlueButton onClick={applyHandle} sx={{height: buttonHeight}}>
          Apply
        </BlueButton>
      </Box>
    );
  };
  const buildHeaderSection = () => {
    return (
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: paddingDefault,
          pb: 0,
        }}
      >
        <Typography sx={{fontWeight: 700, fontSize: '1rem', color: 'body.dark'}}>Filter</Typography>

        {showClearButton && (
          <Box
            onClick={handleClearSelection}
            sx={{
              ml: 'auto',
              p: 0,
              backgroundColor: 'transparent',
              color: 'secondary.main',
              fontWeight: 600,
              fontSize: '0.75rem',
              cursor: 'pointer',
              textDecoration: ' underline',
            }}
          >
            Clear all
          </Box>
        )}

        <IconButton onClick={onClose} size="small" sx={{padding: 0}}>
          <DefaultCloseIcon sx={{color: 'body.300'}} />
        </IconButton>
      </Box>
    );
  };

  const showClearButton = selectedStatus.size > 0 || selectedCreatedOnRange || selectedModifiedOnRange;

  return (
    <>
      {/* Popper sits above backdrop */}
      <PopoverWithArrow
        id="tenant-filter-popper"
        disablePortal={false}
        elevation={2}
        open={open}
        anchorEl={anchorEl}
        transformHOrigin={{
          horizontal: 'right',
        }}
        onClose={onClose}
      >
        <Box sx={{display: 'flex', flexDirection: 'column', gap: 1, minWidth: 390, maxWidth: 350}}>
          {buildHeaderSection()}
          <Divider />
          <Box sx={{padding: paddingDefault, pt: 0, display: 'flex', flexDirection: 'column', gap: 2}}>
            {props.filterView ? (
              props.filterView(filterStatusChipsRef, setSelectedStatus, selectedStatus)
            ) : (
              <FilterStatusChips ref={filterStatusChipsRef} onSelect={setSelectedStatus} value={selectedStatus} />
            )}
            <DateRangeFilter
              title="Created date"
              ref={filterDateRangeRef}
              onSelect={setSelectedCreatedOnRange}
              value={selectedCreatedOnRange}
              error={createOnErrorMessage}
            />
            <DateRangeFilter
              title="Modified date"
              ref={filterModDateRangeRef}
              onSelect={setSelectedModifiedOnRange}
              value={selectedModifiedOnRange}
              error={modifiedOnErrorMessage}
            />
          </Box>
          <Divider />
          {buildButtonSection()}
        </Box>
      </PopoverWithArrow>
    </>
  );
};

export default RoleFilter;
