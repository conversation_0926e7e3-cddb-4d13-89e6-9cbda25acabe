import {screen, waitFor} from '@testing-library/react';
import {PermissionProvider} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import * as authApiSlice from 'redux/auth/authApiSlice';
import {renderWithStore} from 'Tests/utils/renderWithStore';
import {vi} from 'vitest';
import RolesResponsibilitiesPage from './RolesResponsibilitiesPage';

// Mock dependencies
vi.mock('Components/BackdropLoader', () => ({
  default: () => <div data-testid="backdrop-loader" />,
}));
vi.mock('Components/Table/Table', () => ({
  Table: (props: any) => <div data-testid="role-table">{props.data.length === 0 ? 'No Data' : 'Table Data'}</div>,
}));
vi.mock('./NoRoleView', () => ({
  __esModule: true,
  default: () => <div data-testid="no-role-view">No Role Found Add Role </div>,
}));
vi.mock('react-router', () => ({
  useNavigate: vi.fn(),
}));

const mockRoles = [
  {id: 1, name: 'Admin', createdOn: '2024-01-01'},
  {id: 2, name: 'User', createdOn: '2024-01-02'},
];

const mockRoleCount = {count: 2};

vi.mock('react-router', () => ({
  useNavigate: vi.fn(),
}));

describe('RolesResponsibilitiesPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('shows error message when roleCountError is true', () => {
    vi.spyOn(authApiSlice, 'useGetRolesByGroupQuery').mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: false,
    } as any);
    vi.spyOn(authApiSlice, 'useGetCountOfRolesByGroupQuery').mockReturnValue({
      data: undefined,
      error: true,
      isLoading: false,
    } as any);

    renderWithStore(<RolesResponsibilitiesPage />);
    expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
  });

  it('shows loader when data is loading', () => {
    vi.spyOn(authApiSlice, 'useGetRolesByGroupQuery').mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
    } as any);
    vi.spyOn(authApiSlice, 'useGetCountOfRolesByGroupQuery').mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: true,
    } as any);

    renderWithStore(<RolesResponsibilitiesPage />);
    expect(screen.getByTestId('backdrop-loader')).toBeInTheDocument();
  });

  it('shows error message when there is an error', () => {
    vi.spyOn(authApiSlice, 'useGetRolesByGroupQuery').mockReturnValue({
      data: undefined,
      error: true,
      isLoading: false,
    } as any);
    vi.spyOn(authApiSlice, 'useGetCountOfRolesByGroupQuery').mockReturnValue({
      data: undefined,
      error: undefined,
      isLoading: false,
    } as any);

    renderWithStore(<RolesResponsibilitiesPage />);
    expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
  });

  it('renders table with roles data', async () => {
    vi.spyOn(authApiSlice, 'useGetRolesByGroupQuery').mockReturnValue({
      data: mockRoles,
      error: undefined,
      isLoading: false,
    } as any);
    vi.spyOn(authApiSlice, 'useGetCountOfRolesByGroupQuery').mockReturnValue({
      data: mockRoleCount,
      error: undefined,
      isLoading: false,
    } as any);

    renderWithStore(
      <PermissionProvider>
        <RolesResponsibilitiesPage />
      </PermissionProvider>,
    );
    expect(screen.getByText(/roles & permission/i)).toBeInTheDocument();
    await waitFor(() => {
      expect(screen.getByTestId('role-table')).toHaveTextContent('Table Data');
    });
  });

  it('renders table with no data', async () => {
    vi.spyOn(authApiSlice, 'useGetRolesByGroupQuery').mockReturnValue({
      data: [],
      error: undefined,
      isLoading: false,
    } as any);
    vi.spyOn(authApiSlice, 'useGetCountOfRolesByGroupQuery').mockReturnValue({
      data: {count: 0},
      error: undefined,
      isLoading: false,
    } as any);

    renderWithStore(
      <PermissionProvider>
        <RolesResponsibilitiesPage />
      </PermissionProvider>,
    );
    expect(screen.getByText('No Data')).toBeInTheDocument();
  });
});
