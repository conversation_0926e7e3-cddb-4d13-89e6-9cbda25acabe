import {Box, Typography} from '@mui/material';
import SearchIcon from 'Assets/search-icon.svg';
import BackdropLoader from 'Components/BackdropLoader';
import CenterLoaderContainer from 'Components/CenterLoaderContainer';
import FilterWithCountButton from 'Components/FilterWithCountButton';
import PermissionWrapper from 'Components/PermissionWrapper';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import {DebouncedInput} from 'Components/Table';
import {useTableState} from 'Components/Table/hook/TableStateHook';
import {DefaultToolTip} from 'Components/ToolTipTypography/ToolTipTypography';
import PermissionKey from 'Constants/enums/permissions';
import {Integers} from 'Helpers/integers';
import {isNil} from 'lodash';
import React, {useCallback, useMemo, useState} from 'react';
import {IRoleFilterModel} from 'redux/app/types/rolesDto.types';
import {
  IRoleView,
  useActivateDeactiveRoleMutation,
  useGetCountOfRolesByGroupQuery,
  useGetRolesByGroupQuery,
} from 'redux/auth/authApiSlice';
import {ICommonApiFilterDTO} from 'redux/auth/commonApi.type';
import {commonHeaderBoxStyle} from 'styles/pages/Common.styles';
import {
  bodyCellProps,
  coloumnCellProps,
  leftHeaderStyle,
  tableContainerProps,
  tableHeadProps,
} from 'styles/pages/TenantPage.styles';
import {Table} from '../../Components/Table/Table';
import {ActionType, ActivateDeactivateDialog} from './ActivateDeactiveDialog/ActivateDeactivateDialog';
import NoRoleView from './NoRoleView';
import {AddRoleButton} from './RoleDetail/AddRoleButton';
import RoleFilter, {IRoleFilter} from './RoleFilter';
import {RoleStatus, useRoleTableColumns} from './roles.util';

/**
 * Roles & Responsibilities Page
 * This page displays roles and responsibilities for the user.
 * May have features like add search and sorting of some columns
 */
const RolesResponsibilitiesPage = () => {
  const {limit, setLimit, offset, setOffset, handlePageChange, handleRowsPerPageChange} = useTableState();
  const [sortBy, setSortBy] = useState<string>('createdOn DESC');
  const initialSortState = [{id: 'createdOn', desc: true}];
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<IRoleFilter>();
  const filterButtonRef = React.useRef<HTMLElement>(null);

  const commonFilter = useMemo(() => {
    return {
      status: roleFilter?.status && Array.from(roleFilter.status).map(Number),
      createdOnDateRange: roleFilter?.createdOnDateRange,
      modifiedDateRange: roleFilter?.modifiedDateRange,
      searchValue: searchTerm.trim(),
    };
  }, [roleFilter, searchTerm]);
  const [showActivateDeactivate, setShowActivateDeactivate] = useState<IRoleView | undefined>();
  const {columns} = useRoleTableColumns({
    clickOnEditButton: role => {
      setShowActivateDeactivate(role);
    },
  });

  // Build the filter object for the API call
  const filterParams: IRoleFilterModel = useMemo(() => {
    return {
      limit,
      offset,
      order: sortBy,
      ...commonFilter,
    };
  }, [limit, offset, sortBy, commonFilter]);

  // Build count filter (without limit/offset)
  const countFilterParams: ICommonApiFilterDTO = useMemo(() => {
    return {
      order: sortBy,
      ...commonFilter,
    };
  }, [sortBy, commonFilter]);

  const {
    data: roles = [],
    error,
    isLoading,
    isFetching,
    refetch: refetchRoles,
  } = useGetRolesByGroupQuery(filterParams, {
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
  });

  const {
    data: roleCount,
    error: roleCountError,
    isLoading: isRoleCountLoading,
    refetch: refetchRoleCount,
  } = useGetCountOfRolesByGroupQuery(countFilterParams, {
    refetchOnMountOrArgChange: true,
    refetchOnFocus: true,
  });
  const [activateDeactivateRole, {isLoading: performingActivatingDeactivating}] = useActivateDeactiveRoleMutation();

  const handleSortChange = (columnId: string, sort: boolean) => {
    const sortParam = `${columnId} ${sort ? 'DESC' : 'ASC'}`;
    setOffset(0);
    setSortBy(sortParam);
  };

  React.useEffect(() => {
    setOffset(0);
  }, [searchTerm]);

  const [openFilter, setOpenFilter] = React.useState(false);

  const filterSize = React.useMemo(() => {
    let size = 0;
    size += roleFilter?.status?.size ?? 0;
    size += !isNil(roleFilter?.createdOnDateRange) ? 1 : 0;
    size += !isNil(roleFilter?.modifiedDateRange) ? 1 : 0;
    return size;
  }, [roleFilter]);

  const activateDeactivateRoleAction = useCallback(async () => {
    try {
      if (!showActivateDeactivate) return;
      const markActive = showActivateDeactivate.status === RoleStatus.INACTIVE;
      await activateDeactivateRole({id: showActivateDeactivate.roleId, isActive: markActive}).unwrap();
      setShowActivateDeactivate(undefined);
      refetchRoleCount();
      refetchRoles();
    } catch {
      // Error handled at slice level
    }
  }, [activateDeactivateRole, showActivateDeactivate, refetchRoleCount, refetchRoles]);

  if (isLoading || isRoleCountLoading) {
    return <BackdropLoader />;
  }

  if (error || roleCountError) {
    return (
      <Box component={'h2'} sx={{width: '100%', textAlign: 'center'}}>
        Something went wrong. Please try again later.
      </Box>
    );
  }

  const buildFilterButton = () => {
    return (
      <FilterWithCountButton
        ref={filterButtonRef}
        filterSize={filterSize}
        onClick={() => {
          setOpenFilter(prev => !prev);
        }}
      />
    );
  };

  const buildTopRightSection = () => {
    return (
      <Box sx={{display: 'flex', gap: 1, flexDirection: 'row', alignItems: 'center', ml: 'auto'}}>
        {/* Search Input */}
        <DebouncedInput
          placeholder="Search Role"
          data-testid="search-role"
          sx={{
            fontSize: '0.675rem',
            pl: 0,
          }}
          debounceTime={500}
          leftAdornment={<SVGImageFromPath path={SearchIcon} sx={{width: '1rem', height: '1rem', mr: 1}} />}
          inputSx={{
            fontSize: '1rem',
            fontWeight: 400,
            color: 'black.main',
          }}
          value={searchTerm}
          onChange={value => {
            setSearchTerm('' + value);
          }}
        />
        {/* // Filter Button */}
        {buildFilterButton()}
        <PermissionWrapper permission={PermissionKey.AddRole}>{buildAddButton()}</PermissionWrapper>
      </Box>
    );
  };

  const drawTable = () => {
    return (
      <CenterLoaderContainer isLoading={isFetching}>
        <Table
          data={roles ?? []}
          columns={columns}
          enableSorting
          initialSortingState={initialSortState}
          tablePropsObject={{
            tableHeadProps: {sx: tableHeadProps},
            columnCellProps: {sx: coloumnCellProps},
            tableContainerProps: {sx: tableContainerProps},
            bodyCellProps: {sx: bodyCellProps},
          }}
          limit={limit}
          setLimit={setLimit}
          offset={offset}
          setOffset={setOffset}
          count={roleCount?.count ?? 0}
          manualPagination={true}
          onSortChange={handleSortChange}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          data-testid="role-table"
          excludeSortColumns={['status', 'actions']}
        />
      </CenterLoaderContainer>
    );
  };

  const buildAddButton = () => {
    return (
      <DefaultToolTip title="Add Role">
        <AddRoleButton />
      </DefaultToolTip>
    );
  };

  const buildHeader = () => {
    return (
      <Box sx={{minHeight: '3.125rem', ...commonHeaderBoxStyle}}>
        <Box
          sx={{
            flexDirection: 'row',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            width: '100%',
          }}
        >
          <Typography variant="h6" sx={leftHeaderStyle}>
            Roles & Permissions
          </Typography>
          {buildTopRightSection()}
        </Box>
      </Box>
    );
  };

  return (
    <Box>
      {buildHeader()}
      {roles.length === 0 && filterSize !== 0 && !!searchTerm ? (
        <Box sx={{display: 'flex', justifyContent: 'center', alignItems: 'center', height: '70vh'}}>
          <NoRoleView />
        </Box>
      ) : (
        drawTable()
      )}
      <RoleFilter
        open={openFilter}
        anchorEl={filterButtonRef.current}
        onClose={() => setOpenFilter(false)}
        value={roleFilter}
        onFilterChange={filter => {
          setOffset(0);
          setRoleFilter(filter);
        }}
      />
      <ActivateDeactivateDialog
        actionType={(() => {
          switch (showActivateDeactivate?.status) {
            case RoleStatus.ACTIVE:
              if (showActivateDeactivate.userCount > Integers.Zero) {
                return ActionType.DeactivateNotAllowed;
              }
              return ActionType.Deactivate;
            case RoleStatus.INACTIVE:
              return ActionType.Activate;
          }
          return ActionType.Activate;
        })()}
        onClose={() => {
          if (performingActivatingDeactivating) return;
          setShowActivateDeactivate(undefined);
        }}
        onConfirm={activateDeactivateRoleAction}
        isLoading={performingActivatingDeactivating}
        open={!!showActivateDeactivate}
        title={showActivateDeactivate?.roleName ?? ''}
      />
    </Box>
  );
};

export default RolesResponsibilitiesPage;
