import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import FilterStatusChips, {FilterStatusChipsRef, RoleFilterStates} from './StatusFilterChip';

function renderWithRef(props = {}) {
  const ref = React.createRef<FilterStatusChipsRef>();
  render(<FilterStatusChips ref={ref} {...props} />);
  return ref;
}

describe('FilterStatusChips', () => {
  let onSelectMock: any;

  beforeEach(() => {
    onSelectMock = vi.fn();
  });

  it('renders all status chips', () => {
    renderWithRef({onSelect: onSelectMock});
    Object.values(RoleFilterStates).forEach(label => {
      expect(screen.getByText(label)).toBeInTheDocument();
    });
  });

  it('clears selection via ref', () => {
    const ref = renderWithRef({value: new Set(['ACTIVE', 'INACTIVE']), onSelect: onSelectMock});
    ref.current?.clearSelection();
    Object.values(RoleFilterStates).forEach(label => {
      expect(screen.getByText(label)).not.toHaveClass('Mui-selected');
    });
  });

  it('renders without props and does not crash', () => {
    expect(() => render(<FilterStatusChips />)).not.toThrow();
  });

  it('does not call onSelect if not provided', () => {
    render(<FilterStatusChips />);
    const activeChip = screen.getByText('Active');
    fireEvent.click(activeChip);
    // No error should occur
  });

  it('handles rapid select/deselect', () => {
    const onSelect = vi.fn();
    render(<FilterStatusChips onSelect={onSelect} />);
    const activeChip = screen.getByText('Active');
    fireEvent.click(activeChip);
    expect(onSelect).toHaveBeenCalled();
  });

  it('ref usage without assignment does not crash', () => {
    const ref = React.createRef<FilterStatusChipsRef>();
    render(<FilterStatusChips ref={ref} />);
    expect(() => ref.current?.clearSelection()).not.toThrow();
  });

  it('selects and deselects Inactive chip', () => {
    const onSelect = vi.fn();
    render(<FilterStatusChips onSelect={onSelect} />);
    const inactiveChip = screen.getByText('Inactive');
    fireEvent.click(inactiveChip);
    expect(onSelect).toHaveBeenCalled();
  });

  it('clearSelection resets both chips', () => {
    const ref = React.createRef<FilterStatusChipsRef>();
    render(<FilterStatusChips ref={ref} value={new Set(['ACTIVE', 'INACTIVE'])} />);
    ref.current?.clearSelection();
    expect(screen.getByText('Active')).not.toHaveClass('Mui-selected');
    expect(screen.getByText('Inactive')).not.toHaveClass('Mui-selected');
  });
});
