import {ColumnDef} from '@tanstack/react-table';
import RoleStatusChip from 'Components/RoleStatusChip/RoleStatusChip';
import {TableCellBox, TableCellBoxWithToolTip} from 'Components/TableCellBox';
import {defaultTableDateFormatting} from 'Constants/enums';
import {useMemo} from 'react';
import {IRoleView} from 'redux/auth/authApiSlice';
import {ActionButtons} from './ActionButtons';

/**
 * Enum for role status.
 */
export enum RoleStatus {
  ACTIVE = 0,
  INACTIVE = 1,
  UNKNOWN = -1,
}

/**
 * Returns the corresponding `RoleStatus` enum value based on the provided numeric or string input.
 *
 * - If `num` is `0` or `'0'`, returns `RoleStatus.ACTIVE`.
 * - If `num` is `1` or `'1'`, returns `RoleStatus.INACTIVE`.
 * - For any other value, returns `RoleStatus.UNKNOWN`.
 *
 * @param num - The input value representing the role status, as a number or string.
 * @returns The mapped `RoleStatus` value.
 */
export const roleStatusFrom = (num: number | string): RoleStatus => {
  switch (num) {
    case 0:
    case '0':
      return RoleStatus.ACTIVE;
    case 1:
    case '1':
      return RoleStatus.INACTIVE;
    default:
      return RoleStatus.UNKNOWN;
  }
};

/**
 * Defines the columns for the roles table.
 */

interface UseRoleTableColProps {
  clickOnEditButton: (role: IRoleView) => void;
}

export const useRoleTableColumns = ({clickOnEditButton}: UseRoleTableColProps) => {
  const columns: ColumnDef<IRoleView>[] = useMemo(
    () => [
      {
        header: 'Role Name',
        accessorKey: 'roleName',
        cell: ({row}) => <TableCellBoxWithToolTip>{row.original.roleName}</TableCellBoxWithToolTip>,
      },
      {
        header: 'No. of users',
        accessorKey: 'userCount',
        cell: ({row}) => <TableCellBox>{row.original.userCount}</TableCellBox>,
      },
      {
        header: 'Status',
        accessorKey: 'status',
        cell: ({row}) => <RoleStatusChip role={row.original} />,
      },
      {
        header: 'Created On',
        accessorKey: 'createdOn',
        cell: ({row}) => {
          const date = defaultTableDateFormatting(row.original.createdOn);
          return <TableCellBoxWithToolTip>{date}</TableCellBoxWithToolTip>;
        },
      },

      {
        header: 'Modified On',
        accessorKey: 'modifiedOn',
        cell: ({row}) => {
          const date = defaultTableDateFormatting(row.original.modifiedOn);
          return <TableCellBoxWithToolTip>{date}</TableCellBoxWithToolTip>;
        },
      },
      {
        header: 'Actions',
        accessorKey: 'actions',
        cell: ({row}) => <ActionButtons role={row.original} onClickEditButton={clickOnEditButton} />,
      },
    ],
    [],
  );

  return {
    columns,
  };
};
