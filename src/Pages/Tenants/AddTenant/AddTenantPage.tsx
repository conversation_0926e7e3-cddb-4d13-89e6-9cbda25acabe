import {Box, Grid, Stack} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader';
import Breadcrumb from 'Components/Breadcrumb/Breadcrumb';
import Form from 'Components/Forms/Form';
import StepperTab from 'Components/StepperTab/StepperTab';
import {Integers} from 'Helpers/integers';
import {useCallback} from 'react';
import {useLocation, useNavigate} from 'react-router-dom';
import {addTenantValidationSchema, FormAddTenant, steps} from './addTenantsUtils';
import {useAddTenantForm} from './hooks/useAddTenantForm';
import {useAddTenantState} from './hooks/useAddTenantState';
import {useAddTenantSteps} from './hooks/useAddTenantSteps';
import {InitialChoosePlanValues} from './PlanSection/ChoosePlan';
import SuccessDialog from './PlanSection/SuccessDialog';
import RenderButton from './RenderButton';
const breadcrumbItems = [
  {label: 'Tenants', url: '/tenants'},
  {label: 'Add Tenant', url: '/add-tenant'},
];
const editBreadcrumbItems = [
  {label: 'Tenants', url: '/tenants'},
  {label: 'Edit Tenant', url: '/edit-tenant'},
];
function getBreadCrumbItems(isEdit: boolean) {
  if (isEdit) {
    return editBreadcrumbItems;
  }
  return breadcrumbItems;
}
type Props = {
  isEdit?: boolean;
  initialValues?: FormAddTenant; // <- NEW
  onSubmit?: (values: FormAddTenant) => Promise<void>; // <-- NEW
  choosePlanInitialValues?: InitialChoosePlanValues | null; // <-- NEW
};
/**
 * `AddTenantPage` is a multi-step form page to onboard a new tenant.
 * It consists of company info, contact details, plan selection,
 * file uploads, and a summary step before final submission.
 *
 * Features:
 * - Formik-based form with validation
 * - Multi-step navigation via Stepper
 * - File upload and plan selection
 * - Final success dialog and navigation
 */
const AddTenantPage: React.FC<Props> = ({isEdit = false, initialValues: injectedValues, choosePlanInitialValues}) => {
  const navigate = useNavigate();

  const leadInfo = useLocation().state?.leadInfo;
  /**
   * Custom hook to manage state of the Add Tenant form wizard.
   * Handles step navigation, dialog state, files, and plan selections.
   */
  const {
    activeStep,
    handleNext,
    handleBack,
    handleNextButton,
    files,
    overAllPlan,
    userCount,
    setUserCount,
    selectedDevice,
    setSelectedDevice,
    selectedTenure,
    setSelectedTenure,
    selectedInfraConfig,
    setSelectedInfraConfig,
    isDialogOpen,
    setFiles,
    setOverAllPlan,
    onNavigateToTenant,
    setIsDialogOpen,
    setActiveStep,
    nextButtonState,
    createdTenant,
    setCreatedTenant,
    existingFiles,
    setExistingFiles,
  } = useAddTenantState(injectedValues ?? undefined);

  /**
   * Custom hook to initialize form values and handle submission.
   * Integrates file uploads and success dialog logic.
   */
  const {initialValues, formSubmit, isLoading} = useAddTenantForm({
    files,
    setDialogOpen: setIsDialogOpen,
    setActiveStep,
    setCreatedTenant,
    isEdit,
    existingFiles,
    tenantId: injectedValues?.id,
    injectedValues,
    leadInfo,
  });

  /**
   * Custom hook to return step-wise UI rendering for the form wizard.
   * Also passes down file and plan handlers.
   */
  const {renderStepContent} = useAddTenantSteps({
    handleNextButton,
    setOverAllPlan,
    files,
    onFileUpload: (uploadedFiles: File[]) => setFiles([...files, ...uploadedFiles]),
    onRemoveFile: setFiles,
    overAllPlan: overAllPlan,
    userCount,
    setUserCount,
    selectedDevice,
    setSelectedDevice,
    selectedTenure,
    setSelectedTenure,
    selectedInfraConfig,
    setSelectedInfraConfig,
    isEdit,
    choosePlanInitialValues,
    initialValues: injectedValues,
    existingFiles,
    onRemoveExistingFile: setExistingFiles,
  });

  /**
   * Callback to handle cancellation of form wizard, navigates to /tenants
   */
  const handleCancel = useCallback(() => {
    navigate('/tenants');
  }, [navigate]);

  return (
    <Box data-testid="AddTenantPage">
      <Grid container>
        <Grid size={12}>
          <Stack spacing={2}>
            <Breadcrumb items={getBreadCrumbItems(isEdit)} separator="|" showHeader />
          </Stack>
        </Grid>
        <Grid
          container
          size={12}
          sx={{
            borderRadius: '0.375rem',
            borderWidth: '0.0625rem',
            borderStyle: 'solid',
            borderColor: 'body.200',
            overflow: 'hidden',
          }}
        >
          <Grid size={{xs: 12}}>
            <StepperTab activeStep={activeStep} steps={steps} />
          </Grid>
          <Grid
            container
            size={{xs: 12}}
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              opacity: 1,
            }}
          >
            <Form<FormAddTenant>
              initialValues={{...initialValues, ...injectedValues}}
              enableReinitialize
              validationSchema={addTenantValidationSchema}
              onSubmit={formSubmit}
              validateOnChange={true}
            >
              {isLoading ? <BackdropLoader /> : null}
              <Grid size={12}>
                <Box sx={{flex: 1, height: '100%'}}>{renderStepContent(activeStep)}</Box>
              </Grid>

              <Grid
                size={12}
                sx={{
                  borderTop: theme => `0.0625rem solid ${theme.palette.body[Integers.OneHundred]}`, // You can change color as needed
                  mt: '2.25rem',
                }}
              >
                <RenderButton
                  handleBack={handleBack}
                  handleNext={handleNext}
                  handleCancel={handleCancel}
                  activeStep={activeStep}
                  nextButtonState={nextButtonState}
                  isFileUploaded={files.length > 0}
                  isEdit={isEdit}
                  choosePlanInitialValues={choosePlanInitialValues ?? undefined}
                />
              </Grid>

              <SuccessDialog
                isDialogOpen={isDialogOpen}
                onNavigateToTenant={onNavigateToTenant}
                title={isEdit ? 'Tenant updated successfully!' : 'Tenant created successfully!'}
                content={createdTenant}
                isEdit={isEdit}
              />
            </Form>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AddTenantPage;
