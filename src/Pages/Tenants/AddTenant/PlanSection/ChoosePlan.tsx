import {Box, Container, Stack, styled, ToggleButton, ToggleButtonGroup, Typography, useTheme} from '@mui/material';
import BackdropLoader from 'Components/BackdropLoader/BackdropLoader';
import FormSelect from 'Components/Forms/FormSelect/FormSelect';
import {FormikValues, useFormikContext} from 'formik';
import {Integers} from 'Helpers/integers';
import {capitalize, groupedFeatureOptions, PlanStatus} from 'Pages/PlansPage/plans.utils';
import React, {useEffect, useRef} from 'react';
import {useLazyGetDevicesQuery, useLazyGetPlansQuery, useLazyGetTenuresQuery} from 'redux/app/planManagementAPiSlice';
import {IBillingCycle, IConfigureDevice, PlanResponse} from 'redux/app/types/plan.type';
import {
  andAll,
  areAllOptionsPresent,
  areAllSelectionsEmpty,
  calculateTotalCost,
  getPlanSummaryProps,
  getSelectedValue,
  isAnySelectionChanged,
  isPlanSelected,
  orValue,
  planCardScrollSx,
  shouldShowBackdrop,
  shouldShowError,
  shouldShowLoader,
  syncFormikWithSelectionsHelper,
  userCostLabelHelper,
} from './choosePlanHelpers';
import PlanCard from './PlanCard';
import PlanSummary from './PlanSummary';

export const renderTier = (tier: string) => {
  const group = groupedFeatureOptions.find(g => g.value === tier);
  if (group) {
    return (
      <>
        <strong>{group.label}</strong> ({group.features.join(', ')})
      </>
    );
  }
  return '-';
};

interface PlanSelectionState {
  device?: string;
  tenure?: string;
  infra?: string;
}

/**
 * Determines whether the selected plan should be reset based on changes in the plan selection state.
 *
 * @param prevState - The previous state of the plan selection.
 * @param currState - The current state of the plan selection.
 * @param planId - The ID of the plan being evaluated.
 * @param overAllPlan - The overall plan response object, or null/undefined if not available.
 * @param isStepperNavigation - Indicates if the navigation was triggered by a stepper action.
 * @returns `true` if the plan should be reset due to changes in device, tenure, or infra selections and not due to stepper navigation; otherwise, `false`.
 */
function shouldResetPlan(
  prevState: PlanSelectionState,
  currState: PlanSelectionState,
  planId: string,
  overAllPlan: PlanResponse | null | undefined,
  isStepperNavigation: boolean,
) {
  const deviceChanged = !!prevState.device && prevState.device !== currState.device;
  const tenureChanged = !!prevState.tenure && prevState.tenure !== currState.tenure;
  const infraChanged = !!prevState.infra && prevState.infra !== currState.infra;
  return (
    isPlanSelected(planId, overAllPlan) &&
    !isStepperNavigation &&
    isAnySelectionChanged(deviceChanged, tenureChanged, infraChanged)
  );
}

/**
 * Determines whether default selections should be set based on the provided options and current selections.
 *
 * @param tenureOptions - An array of tenure option objects, each with a `label` and `value`.
 * @param deviceOptions - An array of device option objects, each with a `label` and `value`.
 * @param groupedFeatureOptions - An array of grouped feature option objects, each with a `label` and `value`.
 * @param selectedTenure - The currently selected tenure value, or `undefined` if none is selected.
 * @param selectedDevice - The currently selected device value, or `undefined` if none is selected.
 * @param selectedInfraConfig - The currently selected infrastructure configuration value, or `undefined` if none is selected.
 * @returns `true` if all option arrays are present and all selections are empty; otherwise, `false`.
 */
function shouldSetDefaults(
  tenureOptions: Array<{label: string; value: string}>,
  deviceOptions: Array<{label: string; value: string}>,
  groupedFeatureOptions: Array<{label: string; value: string}>,
  selectedTenure: string | undefined,
  selectedDevice: string | undefined,
  selectedInfraConfig: string | undefined,
) {
  return (
    areAllOptionsPresent(tenureOptions, deviceOptions, groupedFeatureOptions) &&
    areAllSelectionsEmpty(selectedTenure, selectedDevice, selectedInfraConfig)
  );
}
export interface InitialChoosePlanValues {
  billingCycle?: string; // id for billingCycle
  noOfDevices?: string; // id for device config
  infraConfig?: string; // infra/tier value
  planId?: string; // selected plan id
  tenurePeriod?: string; // optional label like 'Monthly' (if not provided we compute)
  userCount?: number;
  overAllPlan?: PlanResponse | null; // optional full plan object
}

interface ChoosePlanProps {
  handleNextButton?: (state: boolean) => void;
  setOverAllPlan?: (plan: PlanResponse | null) => void;
  overAllPlan?: PlanResponse | null;
  userCount: number;
  setUserCount: (count: number) => void;
  selectedDevice?: string;
  setSelectedDevice: (val: string) => void;
  selectedTenure?: string;
  setSelectedTenure: (val: string) => void;
  selectedInfraConfig?: string;
  setSelectedInfraConfig: (val: string) => void;
  isStepperNavigation?: boolean;
  initialValues?: InitialChoosePlanValues;
  isEdit?: boolean;
}

const renderError = (props: {devicesError?: unknown; tenuresError?: unknown; plansError?: unknown}) => (
  <ChoosePlanError {...props} />
);

/**
 * Displays an error message when fetching required data for the Choose Plan section fails.
 *
 * @param props - The component props.
 * @param props.devicesError - Optional error object for device fetching failure.
 * @param props.tenuresError - Optional error object for tenure fetching failure.
 * @param props.plansError - Optional error object for plan fetching failure.
 *
 * @returns A React element displaying error messages for the failed data fetches.
 */
const ChoosePlanError: React.FC<{devicesError?: unknown; tenuresError?: unknown; plansError?: unknown}> = ({
  devicesError,
  tenuresError,
  plansError,
}) => (
  <Box data-testid="ChoosePlanError" sx={{p: 4, textAlign: 'center'}}>
    <Typography variant="h6" color="error" gutterBottom>
      Failed to fetch required data.
    </Typography>
    <Typography variant="body2" color="body.500">
      {devicesError ? 'Error getting devices. ' : ''}
      {tenuresError ? 'Error getting tenures. ' : ''}
      {plansError ? 'Error getting plans.' : ''}
    </Typography>
  </Box>
);

/**
 * ChoosePlan component allows users to select a subscription plan by choosing a billing cycle (tenure),
 * number of devices, and infrastructure configuration. It fetches available plans based on the selected
 * options and displays them as selectable cards. The component manages local and Formik form state,
 * handles default selections, and synchronizes selections with Formik context.
 *
 * @component
 * @param {Object} props - Component props.
 * @param {(state: boolean) => void} props.handleNextButton - Callback to handle the next button state.
 * @param {(plan: PlanResponse | null) => void} props.setOverAllPlan - Setter for the selected overall plan.
 * @param {PlanResponse | null} props.overAllPlan - Currently selected overall plan.
 * @param {number} props.userCount - Number of users for the plan.
 * @param {(count: number) => void} props.setUserCount - Setter for the user count.
 * @param {string} props.selectedDevice - Currently selected device configuration ID.
 * @param {(val: string) => void} props.setSelectedDevice - Setter for the selected device configuration.
 * @param {string} props.selectedTenure - Currently selected billing cycle ID.
 * @param {(val: string) => void} props.setSelectedTenure - Setter for the selected billing cycle.
 * @param {string} props.selectedInfraConfig - Currently selected infrastructure configuration ID.
 * @param {(val: string) => void} props.setSelectedInfraConfig - Setter for the selected infrastructure configuration.
 * @param {boolean} [props.isStepperNavigation=false] - Indicates if the component is used within a stepper navigation.
 *
 * @returns {JSX.Element} The rendered ChoosePlan component.
 *
 * @remarks
 * - Uses Formik context for form state management.
 * - Fetches devices, tenures, and plans using lazy queries.
 * - Handles loading and error states for data fetching.
 * - Provides default selection logic and synchronizes with Formik.
 * - Displays plan cards and a plan summary.
 */
/**
 * `ChoosePlan` is a React functional component that provides a UI for selecting a subscription plan,
 * including tenure (billing cycle), number of devices, and infrastructure configuration.
 * It integrates with Formik for form state management and supports both creation and editing flows.
 *
 * ## Props
 * - `handleNextButton`: Callback to handle the "Next" button state.
 * - `setOverAllPlan`: Setter for the selected overall plan.
 * - `overAllPlan`: The currently selected plan object.
 * - `userCount`: Number of users for the selected plan.
 * - `setUserCount`: Setter for the user count.
 * - `selectedDevice`: The currently selected device configuration.
 * - `setSelectedDevice`: Setter for the selected device.
 * - `selectedTenure`: The currently selected billing cycle.
 * - `setSelectedTenure`: Setter for the selected tenure.
 * - `selectedInfraConfig`: The currently selected infrastructure configuration.
 * - `setSelectedInfraConfig`: Setter for the infrastructure configuration.
 * - `isStepperNavigation`: Indicates if navigation is controlled by a stepper (default: false).
 * - `isEdit`: Indicates if the component is in edit mode.
 * - `initialValues`: Initial values for editing an existing plan.
 *
 * ## Features
 * - Fetches available devices, tenures, and plans from APIs.
 * - Applies initial values when editing.
 * - Resets plan selection when relevant fields change.
 * - Synchronizes Formik form state with local selections.
 * - Displays plan cards, selection controls, and a summary.
 * - Handles loading and error states.
 *
 * @component
 * @param {ChoosePlanProps} props - The props for the ChoosePlan component.
 * @returns {JSX.Element} The rendered ChoosePlan UI.
 */
const ChoosePlan: React.FC<ChoosePlanProps> = ({
  handleNextButton,
  setOverAllPlan,
  overAllPlan,
  userCount,
  setUserCount,
  selectedDevice,
  setSelectedDevice,
  selectedTenure,
  setSelectedTenure,
  selectedInfraConfig,
  setSelectedInfraConfig,
  isStepperNavigation = false,
  isEdit,
  initialValues,
}) => {
  // Formik context

  const {values, setFieldValue, setValues} = useFormikContext<FormikValues>();
  // Group local state using useReducer for lower complexity
  type LocalState = {
    planId: string;
    tenurePeriod: string;
  };
  type LocalAction = {type: 'setPlanId'; planId: string} | {type: 'setTenurePeriod'; tenurePeriod: string};

  function localStateReducer(state: LocalState, action: LocalAction): LocalState {
    switch (action.type) {
      case 'setPlanId':
        return {...state, planId: action.planId};
      case 'setTenurePeriod':
        return {...state, tenurePeriod: action.tenurePeriod};
      default:
        return state;
    }
  }
  const theme = useTheme();
  const [localState, dispatchLocal] = React.useReducer(localStateReducer, {
    planId: orValue(overAllPlan?.id, ''),
    tenurePeriod: '',
  });
  const {planId, tenurePeriod} = localState;
  const setPlanId = (id: string) => dispatchLocal({type: 'setPlanId', planId: id});
  const setTenurePeriod = (tp: string) => dispatchLocal({type: 'setTenurePeriod', tenurePeriod: tp});

  const plansLength = 30;

  const [triggerDevices, {data: devicesData, isLoading: devicesLoading, error: devicesError}] =
    useLazyGetDevicesQuery();
  const [triggerTenures, {data: tenuresData, isLoading: tenuresLoading, error: tenuresError}] =
    useLazyGetTenuresQuery();
  const [getPlans, {data: plansData, isLoading: plansLoading, error: plansError, isFetching: plansFetching}] =
    useLazyGetPlansQuery({});

  // Reset plan and related values when device, tenure, or infra config changes
  const didMount = useRef(false);
  // Track previous values to avoid resetting on initial mount and plan selection
  const prevDevice = useRef<string | undefined>(undefined);
  const prevTenure = useRef<string | undefined>(undefined);
  const prevInfra = useRef<string | undefined>(undefined);
  const initialApplied = useRef(false);

  const resetPlanState = () => {
    if (initialApplied.current) return;
    setOverAllPlan?.(null);
    setPlanId('');
    setValues((prev: FormikValues) => ({
      ...prev,
      overAllPlan: null,
      totalCost: 0,
      userCount: 0,
    }));
  };

  useEffect(() => {
    if (didMount.current) {
      if (
        shouldResetPlan(
          {
            device: prevDevice.current,
            tenure: prevTenure.current,
            infra: prevInfra.current,
          },
          {
            device: selectedDevice,
            tenure: selectedTenure,
            infra: selectedInfraConfig,
          },
          planId,
          overAllPlan,
          isStepperNavigation,
        )
      ) {
        resetPlanState();
      }
    } else {
      didMount.current = true;
    }
    prevDevice.current = selectedDevice;
    prevTenure.current = selectedTenure;
    prevInfra.current = selectedInfraConfig;
  }, [selectedDevice, selectedTenure, selectedInfraConfig, isStepperNavigation, planId, overAllPlan]);

  // ---- NEW: apply initial values when editing ----
  useEffect(() => {
    if (!isEdit || initialApplied.current || overAllPlan) return;

    const init = initialValues as
      | {
          billingCycle?: string;
          noOfDevices?: string;
          infraConfig?: string;
          planId?: string;
          tenurePeriod?: string;
          userCount?: number;
          overAllPlan?: PlanResponse | null;
        }
      | undefined;

    if (!init) return;

    applySharedSelections(init);
    applyLocalReducerState(init);
    syncFormikFields(init);

    // Prevent the resetPlanState from clearing these values on first renders
    initialApplied.current = true;
  }, [isEdit]);

  // ----------------- helpers -----------------

  function applySharedSelections(init: InitialChoosePlanValues) {
    if (init.noOfDevices) setSelectedDevice(init.noOfDevices);
    if (init.billingCycle) setSelectedTenure(init.billingCycle);
    if (init.infraConfig) setSelectedInfraConfig(init.infraConfig);
  }

  function applyLocalReducerState(init: InitialChoosePlanValues) {
    const pid = init.planId ?? init.overAllPlan?.id;
    if (pid) setPlanId(pid);

    if (init.tenurePeriod) {
      setTenurePeriod(init.tenurePeriod);
    } else if (init.billingCycle) {
      const tOpt = tenureOptions.find((t: {value: string | undefined}) => t.value === init.billingCycle);
      if (tOpt) setTenurePeriod(tOpt.label);
    } else {
      //DO NOTHING
    }
  }

  function syncFormikFields(init: InitialChoosePlanValues) {
    setFieldValue('billingCycle', init.billingCycle ?? values.billingCycle);
    setFieldValue('noOfDevices', init.noOfDevices ?? values.noOfDevices);
    setFieldValue('infraConfig', init.infraConfig ?? values.infraConfig);

    if (init.overAllPlan) {
      setOverAllPlan?.(init.overAllPlan);
      setValues((prev: FormikValues) => ({
        ...prev,
        overAllPlan: init.overAllPlan,
        userCount: init.userCount ?? prev.userCount,
      }));
    } else if (init.planId) {
      setValues((prev: FormikValues) => ({
        ...prev,
        userCount: init.userCount ?? prev.userCount,
      }));
    } else {
      //DO NOTHING
    }

    if (typeof init.userCount !== 'undefined') {
      setUserCount(init.userCount);
      setFieldValue('userCount', init.userCount);
    }
  }

  // Fetch devices and tenures only once
  useEffect(() => {
    if (!devicesData) triggerDevices({order: 'min asc'});
    if (!tenuresData) triggerTenures({});
  }, [devicesData, tenuresData, triggerDevices, triggerTenures]);

  const deviceOptions =
    devicesData?.map((device: IConfigureDevice) => ({
      value: device.id,
      label: `${device.min} - ${device.max}`,
    })) ?? [];

  const tenureOrder = ['Monthly', 'Quarterly', 'Annually'];
  const tenureOptions = (
    tenuresData?.map((tenures: IBillingCycle) => ({
      value: tenures.id,
      label: capitalize(tenures.cycleName),
    })) ?? []
  ).sort(
    (a: {label: string; value: string}, b: {label: string; value: string}) =>
      tenureOrder.indexOf(a.label) - tenureOrder.indexOf(b.label),
  );

  // Extracted: set all defaults
  function setAllDefaultsHelper(
    options: {
      tenureOptions: Array<{label: string; value: string}>;
      deviceOptions: Array<{label: string; value: string}>;
      groupedFeatureOptions: Array<{label: string; value: string}>;
    },
    setters: {
      setSelectedTenure: (val: string) => void;
      setFieldValue: (field: string, value: unknown) => void;
      setTenurePeriod: (label: string) => void;
      setSelectedDevice: (val: string) => void;
      setSelectedInfraConfig: (val: string) => void;
    },
    getPlans: (params: {
      billingCycleId: string[];
      configureDeviceId: string;
      tier: string;
      limit: number;
      status: number[];
      order: string;
    }) => void,
  ) {
    const {tenureOptions, deviceOptions, groupedFeatureOptions} = options;
    const {setSelectedTenure, setFieldValue, setTenurePeriod, setSelectedDevice, setSelectedInfraConfig} = setters;

    setSelectedTenure(tenureOptions[0].value);
    setFieldValue('billingCycle', tenureOptions[0].value);
    setTenurePeriod(tenureOptions[0].label);

    setSelectedDevice(deviceOptions[0].value);
    setFieldValue('noOfDevices', deviceOptions[0].value);

    setSelectedInfraConfig(groupedFeatureOptions[0].value);
    setFieldValue('infraConfig', groupedFeatureOptions[0].value);

    getPlans({
      billingCycleId: [tenureOptions[0].value],
      configureDeviceId: deviceOptions[0].value,
      tier: groupedFeatureOptions[0].value,
      limit: plansLength,
      status: [PlanStatus.ACTIVE],
      order: 'createdOn DESC',
    });
  }

  // Extracted: sync Formik with selections

  useEffect(() => {
    if (
      shouldSetDefaults(
        tenureOptions,
        deviceOptions,
        groupedFeatureOptions,
        selectedTenure,
        selectedDevice,
        selectedInfraConfig,
      )
    ) {
      setAllDefaultsHelper(
        {
          tenureOptions,
          deviceOptions,
          groupedFeatureOptions,
        },
        {
          setSelectedTenure,
          setFieldValue,
          setTenurePeriod,
          setSelectedDevice,
          setSelectedInfraConfig,
        },
        getPlans,
      );
      return;
    }
    if (selectedTenure && selectedDevice && selectedInfraConfig) {
      syncFormikWithSelectionsHelper({
        tenureOptions,
        selectedTenure: selectedTenure as string,
        selectedDevice: selectedDevice as string,
        selectedInfraConfig: selectedInfraConfig as string,
        tenurePeriod,
        setTenurePeriod,
        setFieldValue,
        values,
        getPlans,
      });
    }
  }, [
    tenureOptions,
    deviceOptions,
    groupedFeatureOptions,
    selectedTenure,
    selectedDevice,
    selectedInfraConfig,
    getPlans,
  ]);

  // Set default value for No. of devices
  useEffect(() => {
    if (deviceOptions.length && !values.noOfDevices) {
      setFieldValue('noOfDevices', deviceOptions[0].value);
    }
  }, [deviceOptions, values.noOfDevices, setFieldValue]);

  // Set default value for Infra configuration
  useEffect(() => {
    if (andAll(groupedFeatureOptions.length > 0, !values.infraConfig)) {
      setFieldValue('infraConfig', groupedFeatureOptions[0].value);
    }
  }, [groupedFeatureOptions, values.infraConfig, setFieldValue]);

  // Keep formik in sync with shared state
  useEffect(() => {
    if (selectedDevice) setFieldValue('noOfDevices', selectedDevice);
  }, [selectedDevice, setFieldValue]);

  useEffect(() => {
    if (selectedTenure) {
      setFieldValue('billingCycle', selectedTenure);
    }
  }, [selectedTenure, setFieldValue]);

  useEffect(() => {
    if (selectedInfraConfig) setFieldValue('infraConfig', selectedInfraConfig);
  }, [selectedInfraConfig, setFieldValue]);

  // Initialize planId from overAllPlan if present
  useEffect(() => {
    if (overAllPlan?.id && !planId) {
      setPlanId(overAllPlan.id);
    }
  }, [overAllPlan, planId]);

  const fmt = (n: number) => `$${n.toLocaleString()}`;
  const planSummaryProps = getPlanSummaryProps(
    plansData,
    planId,
    userCount,
    tenurePeriod,
    (count: number) => {
      setUserCount(count);
      if (!planId || !plansData) return;
      const selected = plansData.find((p: PlanResponse) => p.id === planId);
      if (!selected) return;
      const total = calculateTotalCost(selected, count);
      setFieldValue('totalCost', total);
      setFieldValue('userCount', selected.allowedUnlimitedUsers ? undefined : count);
    },
    fmt,
  );
  // Call getPlans when all initial values are set
  useEffect(() => {
    if (values.billingCycle && values.noOfDevices && values.infraConfig) {
      getPlans({
        billingCycleId: [values.billingCycle],
        configureDeviceId: values.noOfDevices,
        tier: values.infraConfig,
        limit: plansLength,
        status: [PlanStatus.ACTIVE],
        order: 'createdOn DESC',
      });
    }
  }, [values.billingCycle, values.noOfDevices, values.infraConfig]);

  const handleDeviceChange = (val: string) => {
    setSelectedDevice(val);
    setFieldValue('noOfDevices', val);
  };

  const getGradientColor = (color1: string, color2: string) =>
    `linear-gradient(145deg, ${color1} 1.31%, ${color2} 97.7%)`;

  // Use the planGradient and planSelectedGradient from theme colors
  const gradient = getGradientColor(theme.palette.plan.normal[Integers.One], theme.palette.plan.normal[Integers.Two]);
  const selectedGradient = getGradientColor(
    theme.palette.plan.selected[Integers.One],
    theme.palette.plan.selected[Integers.Two],
  );

  const handleTenureChange = async (label: string) => {
    setTenurePeriod(label);
    const tenure = tenureOptions.find((t: {label: string; value: string}) => t.label === label);
    if (tenure) {
      setSelectedTenure(tenure.value);
      setFieldValue('billingCycle', tenure.value);
    }
  };

  const handleInfraChange = async (val: string) => {
    setSelectedInfraConfig(val);
    setFieldValue('infraConfig', val);
  };

  const handlePlanSelect = (planId: string) => {
    setPlanId(planId);
    selectPlan(planId, plansData, setOverAllPlan, handleNextButton, userCount, setValues);
  };

  // Extracted plan selection logic
  function selectPlan(
    planId: string,
    plansData: PlanResponse[] | undefined,
    setOverAllPlan: ((plan: PlanResponse | null) => void) | undefined,
    handleNextButton: ((state: boolean) => void) | undefined,
    userCount: number,
    setValues: (fn: (prevValues: FormikValues) => FormikValues) => void,
  ) {
    const selected = plansData?.find((p: PlanResponse) => p.id === planId);
    if (selected) {
      setOverAllPlan?.(selected);
    }
    handleNextButton?.(false);

    // Calculate totalCost at the time of selection
    const total = calculateTotalCost(selected, userCount);
    setValues((prevValues: FormikValues) => ({
      ...prevValues,
      overAllPlan: selected,
      totalCost: total,
      userCount: selected?.allowedUnlimitedUsers ? undefined : userCount,
    }));
  }

  const PillToggleGroup = styled(ToggleButtonGroup)(({theme}) => ({
    borderRadius: 9999,
    backgroundColor: theme.palette.secondary[Integers.Fifty],
    border: `0.0625rem solid ${theme.palette.secondary[Integers.OneHundred]}`,
    padding: 4,
    gap: 6,
    // Ensure each pill keeps full rounding (MUI resets it for grouped buttons)
    '& .MuiToggleButtonGroup-grouped': {
      border: 0,
      borderRadius: 9999,
      margin: 0,
    },
    '& .MuiToggleButtonGroup-grouped:not(:first-of-type)': {
      border: 0,
      margin: 0,
    },
  }));

  const PillToggle = styled(ToggleButton)(({theme}) => ({
    border: 0,
    borderRadius: 9999,
    padding: '0.375rem 0.875rem',
    color: theme.palette.secondary.main,
    fontWeight: 700,
    '&.Mui-selected': {
      backgroundColor: theme.palette.secondary.main,
      color: theme.palette.white.main,
      '&:hover': {backgroundColor: theme.palette.secondary.main},
    },
    '&:hover': {backgroundColor: theme.palette.action.hover},
  }));

  // Show loader or error using helpers
  if (shouldShowLoader(devicesLoading, tenuresLoading)) {
    return <BackdropLoader />;
  }
  if (shouldShowError(devicesError, tenuresError, plansError)) {
    return renderError({devicesError, tenuresError, plansError});
  }

  return (
    <Container sx={{minWidth: '100%', position: 'relative'}}>
      <Box sx={{p: 2}}>
        <Stack direction="row" justifyContent="space-between" alignItems="center" spacing={2} mb={3} mt={1.5}>
          <PillToggleGroup
            value={tenurePeriod}
            exclusive
            data-testid="tenure-toggle-group"
            onChange={(_, v) => v && handleTenureChange(v)}
            aria-label="Billing cycle"
          >
            {tenureOptions.map((option: {label: string; value: string}) => (
              <PillToggle key={option.label} value={option.label} data-testid={`tenure-${option.label}`}>
                {option.label}
              </PillToggle>
            ))}
          </PillToggleGroup>
          <Stack direction="row" spacing={2} alignItems="center">
            <Stack direction="row" spacing={2} alignItems="center">
              <Typography
                sx={{
                  fontSize: '0.9375rem',
                  fontWeight: 700,
                  color: 'body.dark',
                  marginBottom: 0,
                  display: 'block',
                  textAlign: 'left',
                  whiteSpace: 'nowrap',
                }}
              >
                No. of devices
              </Typography>
              <FormSelect
                id="noOfDevices"
                options={deviceOptions}
                placeholder="Select number of devices"
                onChange={handleDeviceChange}
                value={getSelectedValue(selectedDevice, values.noOfDevices)}
                sx={{width: 150, height: 50, borderRadius: '0.25rem'}}
                data-testid="device-select"
              />
            </Stack>
            <Stack direction="row" spacing={2} alignItems="center" sx={{paddingLeft: 3}}>
              <Typography
                sx={{
                  fontSize: '0.9375rem',
                  fontWeight: 700,
                  color: 'body.dark',
                  marginBottom: 0,
                  display: 'block',
                  textAlign: 'left',
                  whiteSpace: 'nowrap',
                }}
              >
                Infra configuration
              </Typography>
              <FormSelect
                id="infraConfig"
                options={groupedFeatureOptions}
                placeholder="Select infra configuration"
                onChange={handleInfraChange}
                value={getSelectedValue(selectedInfraConfig, values.infraConfig)}
                sx={{width: 150, height: 50, borderRadius: '0.25rem'}}
                data-testid="infra-select"
              />
            </Stack>
          </Stack>
        </Stack>

        {shouldShowBackdrop(plansFetching, plansLoading) && <BackdropLoader />}
        {/* Pricing cards */}
        <Box component="main">
          <Stack
            direction="row"
            spacing={2}
            sx={planCardScrollSx(plansData?.length ?? 0)}
            aria-label="Pricing plans horizontal scroller"
            aria-labelledby="pricing-plans-heading"
          >
            {plansData?.length === 0 ? (
              <Box
                sx={{
                  width: '100%',
                  textAlign: 'center',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="h6" color="body.500">
                  No plans found
                </Typography>
              </Box>
            ) : (
              (plansData ?? []).map((p: PlanResponse) => (
                <PlanCard
                  key={p.id}
                  plan={p}
                  isSelected={planId === p.id}
                  onSelect={handlePlanSelect}
                  tenurePeriod={tenurePeriod}
                  selectedGradient={selectedGradient}
                  gradient={gradient}
                  userCostLabel={userCostLabelHelper}
                />
              ))
            )}
          </Stack>
        </Box>
        {planSummaryProps && <PlanSummary {...planSummaryProps} />}
      </Box>
    </Container>
  );
};

export default ChoosePlan;
