// @vitest-environment jsdom
import {ThemeProvider, createTheme} from '@mui/material/styles';
import {fireEvent, render, screen} from '@testing-library/react';
import {beforeEach, describe, expect, it, vi} from 'vitest';

// Mock Integers enum
vi.mock('Helpers/integers', () => ({
  Integers: {NineHundred: 900},
}));

// Deep theme mock with body.900
const themeMock = createTheme({
  palette: {
    white: {main: '#fff'},
    primary: {main: '#1976d2'},
    error: {main: '#d32f2f'},
    warning: {main: '#ed6c02'},
    info: {main: '#0288d1'},
    success: {main: '#2e7d32'},
    grey: {500: '#9e9e9e'},
    body: {900: '#222'},
    common: {black: '#000', white: '#fff'},
    background: {default: '#fafafa', paper: '#fff'},
    text: {primary: '#000', secondary: '#666'},
  },
});

vi.mock('@mui/material/styles', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('@mui/material', async importOriginal => ({
  ...(await importOriginal()),
  useTheme: () => themeMock,
}));
vi.mock('src/config/theme', () => ({
  default: themeMock,
}));
vi.mock('src/Providers/theme/default', () => ({
  default: themeMock,
}));

import type {PlanResponse} from 'redux/app/types/plan.type';
import PlanCard from './PlanCard';

function renderWithTheme(ui: React.ReactElement) {
  return render(<ThemeProvider theme={themeMock}>{ui}</ThemeProvider>);
}

const basePlan = {
  id: 'plan1',
  name: 'Basic',
  price: '100',
  costPerUser: '10',
  allowedUnlimitedUsers: false,
  tier: 'infra1',
  currency: {
    id: 'cur1',
    symbol: '$',
    currencyCode: 'USD',
    currencyName: 'US Dollar',
    country: 'US',
  },
  configureDevice: {
    id: 'dev1',
    min: 1,
    max: 10,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '',
    createdBy: '',
    modifiedOn: '',
    modifiedBy: '',
    version: '1',
    computeSize: '',
    dbSize: '',
  },
  deleted: false,
  deletedOn: null,
  deletedBy: null,
  createdOn: '',
  createdBy: '',
  modifiedOn: '',
  modifiedBy: '',
  description: '',
  size: '0',
  status: '1',
  version: '1',
  billingCycleId: '',
  currencyId: '',
  configureDeviceId: '',
  planSizeId: '',
};

// Required props for PlanCard
const requiredProps = {
  tenurePeriod: 'Monthly',
  selectedGradient: '#fff',
  gradient: '#eee',
  userCostLabel: (p: any) => 'User Cost',
};

describe('PlanCard Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders plan information correctly', () => {
    renderWithTheme(<PlanCard plan={basePlan} isSelected={false} onSelect={vi.fn()} {...requiredProps} />);
    expect(screen.getByText('Basic')).toBeInTheDocument();
    expect(screen.getByText('$100')).toBeInTheDocument();
  });

  it('renders select button when not selected', () => {
    renderWithTheme(<PlanCard plan={basePlan} isSelected={false} onSelect={vi.fn()} {...requiredProps} />);
    expect(screen.getByText(/Select/i)).toBeInTheDocument();
  });

  it('renders selected state when plan is selected', () => {
    renderWithTheme(<PlanCard plan={basePlan} isSelected={true} onSelect={vi.fn()} {...requiredProps} />);
    expect(screen.getByText(/Selected/i)).toBeInTheDocument();
  });

  it('calls onSelect when select button is clicked', () => {
    const onSelect = vi.fn();
    renderWithTheme(<PlanCard plan={basePlan} isSelected={false} onSelect={onSelect} {...requiredProps} />);
    fireEvent.click(screen.getByText(/Select/i));
    expect(onSelect).toHaveBeenCalled();
  });

  it('handles missing currency symbol', () => {
    const plan = {...basePlan, currency: undefined};
    renderWithTheme(<PlanCard plan={plan} isSelected={false} onSelect={vi.fn()} {...requiredProps} />);
    expect(screen.getByText('100')).toBeInTheDocument();
  });

  it('handles missing configureDevice', () => {
    const plan = {...basePlan, configureDevice: undefined};
    renderWithTheme(<PlanCard plan={plan} isSelected={false} onSelect={vi.fn()} {...requiredProps} />);
    expect(screen.getByText('Basic')).toBeInTheDocument();
  });

  it('handles unlimited users', () => {
    const plan = {...basePlan, allowedUnlimitedUsers: true};
    const unlimitedUserCostLabel = (p: PlanResponse) => (p.allowedUnlimitedUsers ? 'Unlimited' : 'User Cost');
    renderWithTheme(
      <PlanCard
        plan={plan}
        isSelected={false}
        onSelect={vi.fn()}
        {...requiredProps}
        userCostLabel={unlimitedUserCostLabel}
      />,
    );
    expect(screen.getByText(/Unlimited/i)).toBeInTheDocument();
  });

  it('handles quarterly tenure period', () => {
    renderWithTheme(
      <PlanCard plan={basePlan} isSelected={false} onSelect={vi.fn()} {...requiredProps} tenurePeriod="Quarterly" />,
    );
    expect(screen.getByText('Basic')).toBeInTheDocument();
  });

  it('handles annual tenure period', () => {
    renderWithTheme(
      <PlanCard plan={basePlan} isSelected={false} onSelect={vi.fn()} {...requiredProps} tenurePeriod="Annually" />,
    );
    expect(screen.getByText('Basic')).toBeInTheDocument();
  });
});
