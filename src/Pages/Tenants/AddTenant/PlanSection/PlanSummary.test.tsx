// @vitest-environment jsdom
import {fireEvent, render, screen} from '@testing-library/react';
import {PlanResponse} from 'redux/app/types/plan.type';
import {describe, expect, it, vi} from 'vitest';
import PlanSummary from './PlanSummary';

const mockPlan: PlanResponse = {
  id: 'plan1',
  name: 'Basic',
  price: '100',
  costPerUser: '10',
  allowedUnlimitedUsers: false,
  tier: 'infra1',
  currency: {
    id: 'cur1',
    symbol: '$',
    currencyCode: 'USD',
    currencyName: 'US Dollar',
    country: 'US',
  },
  configureDevice: {
    id: 'dev1',
    min: 1,
    max: 10,
    deleted: false,
    deletedOn: null,
    deletedBy: null,
    createdOn: '',
    createdBy: '',
    modifiedOn: '',
    modifiedBy: '',
    computeSize: '',
    dbSize: '',
  },
  deleted: false,
  deletedOn: null,
  deletedBy: null,
  createdOn: '',
  version: '1',
  createdBy: '',
  modifiedOn: '',
  modifiedBy: '',
  description: '',
  size: '0',
  status: '1',
  billingCycleId: '',
  currencyId: '',
  configureDeviceId: '',
  planSizeId: '',
};

const mockProps = {
  selectedPlan: mockPlan,
  tenurePeriod: 'Monthly',
  userCount: 5,
  setUserCount: vi.fn(),
  usersCost: 50,
  totalCost: 150,
  fmt: (n: number) => `$${n.toLocaleString()}`,
};

describe('PlanSummary Component', () => {
  it('renders plan summary correctly', () => {
    render(<PlanSummary {...mockProps} />);

    expect(screen.getByText('Price summary')).toBeInTheDocument();
    expect(screen.getByText('Basic - Monthly')).toBeInTheDocument();
    expect(screen.getByText('Plan cost')).toBeInTheDocument();
    expect(screen.getByText('100')).toBeInTheDocument();
    expect(screen.getByText('Total cost for all users')).toBeInTheDocument();
    expect(screen.getByText('$50')).toBeInTheDocument();
    expect(screen.getByText('Total cost')).toBeInTheDocument();
    expect(screen.getByTestId('total-cost')).toHaveTextContent('$150');
    expect(screen.getByTestId('total-cost')).toHaveTextContent('/Month');
  });

  it('renders user count controls when not unlimited', () => {
    render(<PlanSummary {...mockProps} />);

    expect(screen.getByText('Number of Users')).toBeInTheDocument();
    expect(screen.getByTestId('decrease-user-count')).toBeInTheDocument();
    expect(screen.getByTestId('increase-user-count')).toBeInTheDocument();
    expect(screen.getByText('5')).toBeInTheDocument();
  });

  it('does not render user count controls for unlimited plans', () => {
    const unlimitedPlan = {...mockPlan, allowedUnlimitedUsers: true};
    render(<PlanSummary {...mockProps} selectedPlan={unlimitedPlan} />);

    expect(screen.queryByText('Number of Users')).not.toBeInTheDocument();
    expect(screen.queryByTestId('decrease-user-count')).not.toBeInTheDocument();
    expect(screen.queryByTestId('increase-user-count')).not.toBeInTheDocument();
  });

  it('decreases user count when decrease button is clicked', () => {
    const setUserCountMock = vi.fn();
    render(<PlanSummary {...mockProps} setUserCount={setUserCountMock} />);

    const decreaseButton = screen.getByTestId('decrease-user-count');
    fireEvent.click(decreaseButton);

    expect(setUserCountMock).toHaveBeenCalledWith(4);
  });

  it('increases user count when increase button is clicked', () => {
    const setUserCountMock = vi.fn();
    render(<PlanSummary {...mockProps} setUserCount={setUserCountMock} />);

    const increaseButton = screen.getByTestId('increase-user-count');
    fireEvent.click(increaseButton);

    expect(setUserCountMock).toHaveBeenCalledWith(6);
  });

  it('does not decrease user count below 1', () => {
    const setUserCountMock = vi.fn();
    render(<PlanSummary {...mockProps} userCount={1} setUserCount={setUserCountMock} />);

    expect(screen.getByTestId('disable-remove-icon')).toBeInTheDocument();
  });

  it('handles quarterly tenure', () => {
    render(<PlanSummary {...mockProps} tenurePeriod="Quarterly" />);

    expect(screen.getByText('Basic - Quarterly')).toBeInTheDocument();
    expect(screen.getByTestId('total-cost')).toHaveTextContent('$150');
    expect(screen.getByTestId('total-cost')).toHaveTextContent('/Quarter');
  });

  it('handles annual tenure', () => {
    render(<PlanSummary {...mockProps} tenurePeriod="Annually" />);

    expect(screen.getByText('Basic - Annually')).toBeInTheDocument();
    expect(screen.getByTestId('total-cost')).toHaveTextContent('$150');
    expect(screen.getByTestId('total-cost')).toHaveTextContent('/Annual');
  });

  it('handles zero users cost', () => {
    render(<PlanSummary {...mockProps} usersCost={0} totalCost={100} />);

    expect(screen.getByText('$0')).toBeInTheDocument();
    expect(screen.getByTestId('total-cost')).toHaveTextContent('$100');
    expect(screen.getByTestId('total-cost')).toHaveTextContent('/Month');
  });

  it('handles large numbers formatting', () => {
    render(<PlanSummary {...mockProps} usersCost={1000} totalCost={1100} />);

    expect(screen.getByText('$1,000')).toBeInTheDocument();
    expect(screen.getByTestId('total-cost')).toHaveTextContent('$1,100');
    expect(screen.getByTestId('total-cost')).toHaveTextContent('/Month');
  });
});
