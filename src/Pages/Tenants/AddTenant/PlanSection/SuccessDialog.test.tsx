import {fireEvent, render, screen} from '@testing-library/react';
import {Formik} from 'formik';
import {MemoryRouter} from 'react-router';
import {vi} from 'vitest';
import {FormAddTenant} from '../addTenantsUtils';
import SuccessDialog from './SuccessDialog';

// Mock navigate
const mockNavigate = vi.fn();

// ✅ Partial mock: keeps MemoryRouter real, mocks useNavigate
vi.mock('react-router', async () => {
  const actual = await vi.importActual<typeof import('react-router')>('react-router');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// Wrap component in both Formik and Router
const renderWithProviders = (ui: React.ReactElement, formValues: Partial<FormAddTenant> = {}) => {
  const defaultValues: FormAddTenant = {
    company: '',
    firstName: '',
    lastName: '',
    designation: '',
    email: '',
    countryCode: {code: '', label: ''}, // or a valid CountryCode default if not string
    mobileNumber: '',
    city: '',
    state: '',
    language: '',
    files: [],
    key: '',
    ...formValues,
  };

  return render(
    <MemoryRouter>
      <Formik initialValues={defaultValues} onSubmit={vi.fn()}>
        {ui}
      </Formik>
    </MemoryRouter>,
  );
};

describe('SuccessDialog', () => {
  const defaultProps = {
    isDialogOpen: true,
    onNavigateToTenant: vi.fn(),
  };

  const setup = (props = defaultProps, formValues: Partial<FormAddTenant> = {}) =>
    renderWithProviders(<SuccessDialog {...props} />, formValues);

  it('should render dialog when open', () => {
    setup();

    const dialog = screen.getByTestId('success-typography');
    expect(dialog).toBeInTheDocument();
  });

  it('should not render dialog when closed', () => {
    setup({...defaultProps, isDialogOpen: false});

    const dialog = screen.queryByTestId('success-typography');
    expect(dialog).not.toBeInTheDocument();
  });

  it('should display custom title and content when provided', () => {
    const customProps = {
      ...defaultProps,
      title: 'Tenant Created!',
      content: 'You have successfully created a tenant.',
    };

    setup(customProps);

    expect(screen.getByText('Tenant Created!')).toBeInTheDocument();
    expect(screen.getByText('You have successfully created a tenant.')).toBeInTheDocument();
  });

  it('should call onNavigateToTenant when "Add more tenants" is clicked', () => {
    setup();

    const addBtn = screen.getByRole('button', {name: /add more tenants/i});
    fireEvent.click(addBtn);

    expect(defaultProps.onNavigateToTenant).toHaveBeenCalled();
  });

  it('should navigate to "/tenants" when "Go to Tenants" is clicked', () => {
    setup();

    const goToTenantBtn = screen.getByRole('button', {name: /go to tenants/i});
    fireEvent.click(goToTenantBtn);

    expect(mockNavigate).toHaveBeenCalledWith('/tenants');
  });

  it('should render the success image with alt text', () => {
    setup();

    const img = screen.getByAltText('file');
    expect(img).toBeInTheDocument();
    expect(img.tagName).toBe('IMG');
  });
});
