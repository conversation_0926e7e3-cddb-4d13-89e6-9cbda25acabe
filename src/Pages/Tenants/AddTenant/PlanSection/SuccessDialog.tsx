import {Box, Dialog, DialogContent, Stack, Typography} from '@mui/material';
import Button from 'Components/Button';
import {useNavigate} from 'react-router';
import success from '../../../../Assets/success.svg';
import {SuccessDialogStyle} from './SuccessDialogStyle';

/**
 * Props for SuccessDialog component.
 */
interface SuccessDialogProps {
  /** Controls visibility of the dialog */
  isDialogOpen: boolean;
  /** Callback when user wants to add another tenant */
  onNavigateToTenant: () => void;
  /** Title of the success dialog */
  title?: string;
  /** Content/message of the dialog */
  content?: string;
  isEdit?: boolean;
}

/**
 * SuccessDialog component shown after successful tenant creation.
 *
 * @component
 * @param {SuccessDialogProps} props
 */
const SuccessDialog = ({isDialogOpen, onNavigateToTenant, title, content, isEdit}: SuccessDialogProps) => {
  const navigate = useNavigate();

  /**
   * Redirects to the given path.
   * @param nav - Navigation path
   */
  const handleNavigate = (nav: string) => {
    navigate(nav);
  };

  return (
    <Dialog open={isDialogOpen} maxWidth="xs" fullWidth sx={SuccessDialogStyle.dialog} data-testid="success-typography">
      <DialogContent sx={SuccessDialogStyle.dialogContent}>
        <Box
          component="img"
          src={success}
          alt="file"
          sx={{
            width: '5rem',
            height: '5rem',
            objectFit: 'contain',
            marginBottom: 2,
            marginTop: 1,
          }}
        />

        <Typography variant="h6" sx={SuccessDialogStyle.outerTypography}>
          {title ?? 'Success'}
        </Typography>
        {content && (
          <Box sx={SuccessDialogStyle.box}>
            <Typography sx={SuccessDialogStyle.typography}>
              {content ?? 'The process to add tenant has been initiated successfully.'}
            </Typography>
          </Box>
        )}
        <Stack spacing={2.5} sx={SuccessDialogStyle.stack}>
          {!isEdit && (
            <Button onClick={onNavigateToTenant} variant="contained" sx={SuccessDialogStyle.createTenantButton}>
              Add more tenants
            </Button>
          )}
          <Button variant="outlined" onClick={() => handleNavigate('/tenants')} sx={SuccessDialogStyle.tenantButton}>
            Go to Tenants
          </Button>
        </Stack>
      </DialogContent>
    </Dialog>
  );
};

export default SuccessDialog;
