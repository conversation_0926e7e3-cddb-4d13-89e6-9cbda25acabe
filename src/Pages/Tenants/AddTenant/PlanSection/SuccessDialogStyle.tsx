const dialog = {
  '& .MuiDialog-paper': {
    borderRadius: '0.2rem',
  },
  my: 2,
};

const dialogContent = {textAlign: 'center', p: 2, whiteSpace: 'normal', wordBreak: 'break-word'};

const circleIcon = {fontSize: '4.375rem', mb: 1, color: 'primary.stepperCompletedIcon'};

const outerTypography = {color: 'black.main', fontWeight: '700', fontSize: '1.375rem'};

const typography = {
  mt: 1,
  whiteSpace: 'normal',
  maxWidth: '80%',
  wordBreak: 'break-word',
  color: 'body.500',
  fontSize: '1rem',
};

const box = {
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  width: '100%',
  textAlign: 'center',
};

const stack = {mt: 5, alignItems: 'center', width: '100%'};

const createTenantButton = {
  width: '100%',
  fontSize: '1rem',
  fontWeight: 700,
  height: '3.75rem',
  fill: 'secondary.700',
  backgroundColor: 'secondary.500',
  color: 'white.main',
  '&:hover': {backgroundColor: 'secondary.main'},
};

const tenantButton = {
  width: '100%',
  fontSize: '1rem',
  color: 'black.main',
  fontWeight: 700,
  height: '3.75rem',
  borderColor: 'body.100',
};

export const SuccessDialogStyle = {
  dialog,
  dialogContent,
  circleIcon,
  outerTypography,
  box,
  typography,
  stack,
  createTenantButton,
  tenantButton,
};
