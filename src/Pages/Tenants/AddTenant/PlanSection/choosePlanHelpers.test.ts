// @vitest-environment jsdom
import {describe, expect, it, vi} from 'vitest';
import * as helpers from './choosePlanHelpers';

describe('choosePlanHelpers', () => {
  it('areAllOptionsPresent returns true only if all arrays are non-empty', () => {
    expect(
      helpers.areAllOptionsPresent([{label: 'a', value: '1'}], [{label: 'b', value: '2'}], [{label: 'c', value: '3'}]),
    ).toBe(true);
    expect(helpers.areAllOptionsPresent([], [{label: 'b', value: '2'}], [{label: 'c', value: '3'}])).toBe(false);
    expect(helpers.areAllOptionsPresent([{label: 'a', value: '1'}], [], [{label: 'c', value: '3'}])).toBe(false);
    expect(helpers.areAllOptionsPresent([{label: 'a', value: '1'}], [{label: 'b', value: '2'}], [])).toBe(false);
  });

  it('areAllSelectionsEmpty returns true only if all are empty/undefined', () => {
    expect(helpers.areAllSelectionsEmpty()).toBe(true);
    expect(helpers.areAllSelectionsEmpty('', '', '')).toBe(true);
    expect(helpers.areAllSelectionsEmpty('a', '', '')).toBe(false);
    expect(helpers.areAllSelectionsEmpty('', 'b', '')).toBe(false);
    expect(helpers.areAllSelectionsEmpty('', '', 'c')).toBe(false);
  });

  it('isPlanSelected returns true if planId or overAllPlan is truthy', () => {
    expect(helpers.isPlanSelected('id', null)).toBe(true);
    expect(helpers.isPlanSelected('', {id: 'id'} as any)).toBe(true);
    expect(helpers.isPlanSelected('', null)).toBe(false);
  });

  it('isAnySelectionChanged returns true if any arg is true', () => {
    expect(helpers.isAnySelectionChanged(false, false, false)).toBe(false);
    expect(helpers.isAnySelectionChanged(true, false, false)).toBe(true);
    expect(helpers.isAnySelectionChanged(false, true, false)).toBe(true);
    expect(helpers.isAnySelectionChanged(false, false, true)).toBe(true);
  });

  it('syncFormikWithSelectionsHelper updates fields and calls getPlans if needed', () => {
    const setFieldValue = vi.fn();
    const setTenurePeriod = vi.fn();
    const getPlans = vi.fn();
    const params = {
      tenureOptions: [{label: 'Monthly', value: 'm'}],
      selectedTenure: 'm',
      selectedDevice: '2',
      selectedInfraConfig: 'tier1',
      tenurePeriod: '',
      setTenurePeriod,
      setFieldValue,
      values: {billingCycle: '', noOfDevices: '', infraConfig: ''},
      getPlans,
    };
    helpers.syncFormikWithSelectionsHelper(params);
    expect(setTenurePeriod).toHaveBeenCalledWith('Monthly');
    expect(setFieldValue).toHaveBeenCalledWith('billingCycle', 'm');
    expect(setFieldValue).toHaveBeenCalledWith('noOfDevices', '2');
    expect(setFieldValue).toHaveBeenCalledWith('infraConfig', 'tier1');
    expect(getPlans).toHaveBeenCalledWith({billingCycleId: ['m'], configureDeviceId: '2', tier: 'tier1'});
  });

  it('shouldShowLoader returns true if any loading is true', () => {
    expect(helpers.shouldShowLoader(true, false)).toBe(true);
    expect(helpers.shouldShowLoader(false, true)).toBe(true);
    expect(helpers.shouldShowLoader(false, false)).toBe(false);
  });

  it('shouldShowError returns true if any error is truthy', () => {
    expect(helpers.shouldShowError(null, null, null)).toBe(false);
    expect(helpers.shouldShowError('err', null, null)).toBe(true);
    expect(helpers.shouldShowError(null, 'err', null)).toBe(true);
    expect(helpers.shouldShowError(null, null, 'err')).toBe(true);
  });

  it('getPlanSummaryProps returns summary or null', () => {
    const plan = {id: '1', costPerUser: 10, price: 100} as any;
    const result = helpers.getPlanSummaryProps([plan], '1', 2, 'Monthly', vi.fn(), n => `$${n}`);
    expect(result).toMatchObject({
      selectedPlan: plan,
      tenurePeriod: 'Monthly',
      userCount: 2,
      usersCost: 20,
      totalCost: 120,
    });
    expect(result?.fmt(5)).toBe('$5');
    expect(helpers.getPlanSummaryProps(undefined, '1', 2, 'Monthly', vi.fn(), n => `$${n}`)).toBeNull();
    expect(helpers.getPlanSummaryProps([plan], '', 2, 'Monthly', vi.fn(), n => `$${n}`)).toBeNull();
    expect(helpers.getPlanSummaryProps([], '1', 2, 'Monthly', vi.fn(), n => `$${n}`)).toBeNull();
  });

  it('calculateTotalCost returns correct total', () => {
    const plan = {costPerUser: 10, price: 100} as any;
    expect(helpers.calculateTotalCost(plan, 2)).toBe(120);
    expect(helpers.calculateTotalCost(undefined, 2)).toBe(0);
  });

  it('userCostLabelHelper returns correct label', () => {
    expect(helpers.userCostLabelHelper({allowedUnlimitedUsers: true} as any)).toBe('Unlimited');
    expect(helpers.userCostLabelHelper({costPerUser: 10} as any)).toBe('$10 per user');
    expect(helpers.userCostLabelHelper({} as any)).toBe('-');
  });

  it('orValue returns a if defined, else b', () => {
    expect(helpers.orValue('a', 'b')).toBe('a');
    expect(helpers.orValue(undefined, 'b')).toBe('b');
    expect(helpers.orValue(null, 'b')).toBe('b');
    expect(helpers.orValue('', 'b')).toBe('b');
  });

  it('andAll returns true only if all are true', () => {
    expect(helpers.andAll(true, true, true)).toBe(true);
    expect(helpers.andAll(true, false, true)).toBe(false);
  });

  it('shouldShowBackdrop returns true if any arg is true', () => {
    expect(helpers.shouldShowBackdrop(true, false)).toBe(true);
    expect(helpers.shouldShowBackdrop(false, true)).toBe(true);
    expect(helpers.shouldShowBackdrop(false, false)).toBe(false);
  });

  it('shouldShowLabel returns true if array is non-empty', () => {
    expect(helpers.shouldShowLabel([1])).toBe(true);
    expect(helpers.shouldShowLabel([])).toBe(false);
    expect(helpers.shouldShowLabel(undefined as any)).toBe(false);
  });

  it('planCardScrollSx returns correct style object', () => {
    const sx = helpers.planCardScrollSx(2);
    expect(sx.justifyContent).toBe('center');
    const sx2 = helpers.planCardScrollSx(5);
    expect(sx2.justifyContent).toBeUndefined();
    expect(sx2.width).toBe('100%');
  });

  it('getSelectedValue returns primary if valid, else fallback', () => {
    expect(helpers.getSelectedValue('a', 'b')).toBe('a');
    expect(helpers.getSelectedValue('', 'b')).toBe('b');
    expect(helpers.getSelectedValue(undefined, 'b')).toBe('b');
    expect(helpers.getSelectedValue(undefined, undefined)).toBeUndefined();
  });
});
