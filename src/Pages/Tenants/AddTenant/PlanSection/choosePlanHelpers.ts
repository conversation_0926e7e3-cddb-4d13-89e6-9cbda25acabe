import {FormikValues, useFormikContext} from 'formik';
import React from 'react';
import {PlanResponse} from 'redux/app/types/plan.type';

/**
 * Checks if all provided option arrays (tenure, device, and grouped feature options) contain at least one element.
 *
 * @param tenureOptions - An array of tenure option objects, each with a `label` and `value`.
 * @param deviceOptions - An array of device option objects, each with a `label` and `value`.
 * @param groupedFeatureOptions - An array of grouped feature option objects, each with a `label` and `value`.
 * @returns `true` if all option arrays are non-empty; otherwise, `false`.
 */
export function areAllOptionsPresent(
  tenureOptions: Array<{label: string; value: string}>,
  deviceOptions: Array<{label: string; value: string}>,
  groupedFeatureOptions: Array<{label: string; value: string}>,
): boolean {
  return tenureOptions.length > 0 && deviceOptions.length > 0 && groupedFeatureOptions.length > 0;
}

/**
 * Checks if all selection parameters are empty or undefined.
 *
 * @param selectedTenure - The selected tenure value, or undefined if not selected.
 * @param selectedDevice - The selected device value, or undefined if not selected.
 * @param selectedInfraConfig - The selected infrastructure configuration value, or undefined if not selected.
 * @returns `true` if all selections are empty or undefined; otherwise, `false`.
 */
export function areAllSelectionsEmpty(
  selectedTenure?: string,
  selectedDevice?: string,
  selectedInfraConfig?: string,
): boolean {
  return !selectedTenure && !selectedDevice && !selectedInfraConfig;
}

/**
 * Determines if a plan is selected based on the provided plan ID or overall plan object.
 *
 * @param planId - The ID of the plan to check.
 * @param overAllPlan - The overall plan object, which may be null or undefined.
 * @returns `true` if either the plan ID or the overall plan object is truthy; otherwise, `false`.
 */
export function isPlanSelected(planId: string, overAllPlan: PlanResponse | null | undefined): boolean {
  return Boolean(planId || overAllPlan);
}

/**
 * Determines if any of the selection states (device, tenure, or infrastructure) have changed.
 *
 * @param deviceChanged - Indicates whether the device selection has changed.
 * @param tenureChanged - Indicates whether the tenure selection has changed.
 * @param infraChanged - Indicates whether the infrastructure selection has changed.
 * @returns `true` if any of the selections have changed; otherwise, `false`.
 */
export function isAnySelectionChanged(deviceChanged: boolean, tenureChanged: boolean, infraChanged: boolean): boolean {
  return deviceChanged || tenureChanged || infraChanged;
}

export interface SyncFormikParams {
  tenureOptions: Array<{label: string; value: string}>;
  selectedTenure: string;
  selectedDevice: string;
  selectedInfraConfig: string;
  tenurePeriod: string;
  setTenurePeriod: (label: string) => void;
  setFieldValue: (field: string, value: unknown) => void;
  values: FormikValues;
  getPlans: (params: {billingCycleId: string[]; configureDeviceId: string; tier: string}) => void;
}

/**
 * Synchronizes Formik form values with the current user selections for tenure, device, and infrastructure configuration.
 *
 * This helper updates the Formik state if any of the selected values differ from the current form values.
 * If any updates are made, it triggers a plan retrieval based on the new selections.
 * It also ensures the tenure period label is kept in sync with the selected tenure.
 *
 * @param params - An object containing the following properties:
 *   - tenureOptions: Array of available tenure options.
 *   - selectedTenure: The currently selected tenure value.
 *   - selectedDevice: The currently selected device value.
 *   - selectedInfraConfig: The currently selected infrastructure configuration value.
 *   - tenurePeriod: The current tenure period label.
 *   - setTenurePeriod: Function to update the tenure period label.
 *   - setFieldValue: Function to update a Formik field value.
 *   - values: The current Formik form values.
 *   - getPlans: Function to fetch plans based on the current selections.
 */
export function syncFormikWithSelectionsHelper(params: SyncFormikParams): void {
  const {
    tenureOptions,
    selectedTenure,
    selectedDevice,
    selectedInfraConfig,
    tenurePeriod,
    setTenurePeriod,
    setFieldValue,
    values,
    getPlans,
  } = params;

  const tenureObj = tenureOptions.find(t => t.value === selectedTenure);
  if (tenureObj && tenureObj.label !== tenurePeriod) setTenurePeriod(tenureObj.label);

  let shouldUpdate = false;
  if (values.billingCycle !== selectedTenure) {
    setFieldValue('billingCycle', selectedTenure);
    shouldUpdate = true;
  }
  if (values.noOfDevices !== selectedDevice) {
    setFieldValue('noOfDevices', selectedDevice);
    shouldUpdate = true;
  }
  if (values.infraConfig !== selectedInfraConfig) {
    setFieldValue('infraConfig', selectedInfraConfig);
    shouldUpdate = true;
  }
  if (shouldUpdate) {
    getPlans({
      billingCycleId: [selectedTenure],
      configureDeviceId: selectedDevice,
      tier: selectedInfraConfig,
    });
  }
}

// New helpers for ChoosePlan complexity reduction

/**
 * Determines whether a loading indicator should be displayed based on the loading states of various resources.
 *
 * @param devicesLoading - Indicates if device data is currently loading.
 * @param tenuresLoading - Indicates if tenure data is currently loading.
 * @returns `true` if any of the loading flags are `true`, otherwise `false`.
 */
export function shouldShowLoader(devicesLoading: boolean, tenuresLoading: boolean): boolean {
  return devicesLoading || tenuresLoading;
}

/**
 * Determines whether an error should be shown based on the presence of errors in devices, tenures, or plans.
 *
 * @param devicesError - The error related to devices, if any.
 * @param tenuresError - The error related to tenures, if any.
 * @param plansError - The error related to plans, if any.
 * @returns `true` if any of the provided errors are truthy; otherwise, `false`.
 */
export function shouldShowError(devicesError: unknown, tenuresError: unknown, plansError: unknown): boolean {
  return Boolean(devicesError || tenuresError || plansError);
}

/**
 * Generates summary properties for a selected plan, including calculated costs and handlers.
 *
 * @param plansData - Array of available plan responses or undefined.
 * @param planId - The ID of the selected plan.
 * @param userCount - The number of users for the plan.
 * @param tenurePeriod - The selected tenure period for the plan.
 * @param setUserCount - Callback to update the user count.
 * @param fmt - Function to format numbers for display.
 * @returns An object containing the selected plan, tenure period, user count, cost calculations, and formatting function,
 *          or `null` if the plan is not found or planId is not provided.
 */
export function getPlanSummaryProps(
  plansData: PlanResponse[] | undefined,
  planId: string,
  userCount: number,
  tenurePeriod: string,
  setUserCount: (count: number) => void,
  fmt: (n: number) => string,
) {
  const selectedPlan = plansData?.find((p: PlanResponse) => p.id === planId);
  if (!planId || !selectedPlan) return null;
  const perUser = selectedPlan.costPerUser ?? 0;
  const planCost = Number(selectedPlan.price ?? 0);
  const usersCost = Number(perUser ?? 0) * userCount;
  const totalCost = planCost + usersCost;

  return {
    selectedPlan,
    tenurePeriod,
    userCount,
    setUserCount,
    usersCost,
    totalCost,
    fmt,
  };
}

/**
 * Calculates the total cost for a plan and user count.
 *
 * @param plan - The selected plan object.
 * @param userCount - The number of users.
 * @returns The total cost as a number.
 */
export function calculateTotalCost(plan: PlanResponse | undefined, userCount: number): number {
  if (!plan) return 0;
  const perUser = plan.costPerUser ?? 0;
  const planCost = Number(plan.price ?? 0);
  const usersCost = Number(perUser) * userCount;
  return planCost + usersCost;
}

/**
 * Custom hook to manage the state for choosing a plan in the Add Tenant flow.
 *
 * @param overAllPlan - The currently selected overall plan, or null if none is selected.
 * @returns An object containing Formik context values and setters, as well as local state and setters for plan selection.
 *
 * @property values - The current Formik form values.
 * @property setFieldValue - Function to set a specific field value in Formik.
 * @property setValues - Function to set all form values in Formik.
 * @property planId - The currently selected plan ID.
 * @property setPlanId - Setter for the selected plan ID.
 * @property tenurePeriod - The selected tenure period for the plan.
 * @property setTenurePeriod - Setter for the tenure period.
 */
export function useChoosePlanState(overAllPlan: PlanResponse | null) {
  const {values, setFieldValue, setValues} = useFormikContext<FormikValues>();
  const [planId, setPlanId] = React.useState(overAllPlan?.id || '');
  const [tenurePeriod, setTenurePeriod] = React.useState('');
  return {
    values,
    setFieldValue,
    setValues,
    planId,
    setPlanId,
    tenurePeriod,
    setTenurePeriod,
  };
}

/**
 * Returns a user cost label string based on the given plan.
 *
 * - If the plan allows unlimited users, returns 'Unlimited'.
 * - If the plan has a cost per user, returns the cost formatted as `'$<cost> per user'`.
 * - Otherwise, returns '-'.
 *
 * @param p - The plan response object containing user and cost information.
 * @returns A string representing the user cost label for the plan.
 */
export function userCostLabelHelper(p: PlanResponse): string {
  if (p.allowedUnlimitedUsers) return 'Unlimited';
  if (p.costPerUser) return `$${p.costPerUser} per user`;
  return '-';
}

export function orValue<T>(a: T | undefined, b: T): T {
  return a !== undefined && a !== null && a !== '' ? a : b;
}

export function andAll(...args: boolean[]): boolean {
  return args.every(Boolean);
}

export const scrollThumbHoverSx = {
  backgroundColor: 'text.disabled',
};

export function shouldShowBackdrop(isFetching: boolean, plansLoading: boolean): boolean {
  return isFetching || plansLoading;
}

export function shouldShowLabel(deviceOptions: unknown[]): boolean {
  return Array.isArray(deviceOptions) && deviceOptions.length > 0;
}

const minGridSize = 3;

/**
 * Generates a style object for horizontally scrollable plan cards with responsive and accessible enhancements.
 *
 * @param planCount - The number of plan cards to display.
 * @returns An object containing style properties for the plan card container, including responsive scroll snapping,
 *          gradient masking for scroll edges, custom scrollbar styling, and conditional centering based on plan count.
 *
 * @remarks
 * - Applies different scroll snap behaviors and gradient masks for small (`xs`) and medium (`md`) breakpoints.
 * - Centers the content if the number of plans is less than or equal to `minGridSize`.
 * - Enhances scrollbar appearance and usability across browsers.
 */
export const planCardScrollSx = (planCount: number) => ({
  width: '100%',
  maxWidth: '100%',
  overflowX: 'auto',
  overscrollBehaviorX: 'contain',
  WebkitOverflowScrolling: 'touch',
  scrollSnapType: {xs: 'x mandatory', md: 'x proximity'},
  px: 0.5,
  pb: 2,
  scrollbarGutter: 'stable both-edges',
  minHeight: 400,
  ...(planCount > 0 && planCount <= minGridSize ? {justifyContent: 'center'} : {}),
  '&::-webkit-scrollbar': {height: 6, width: 6},
  '&::-webkit-scrollbar-thumb': {
    backgroundColor: 'divider',
    borderRadius: 9999,
  },
  '&::-webkit-scrollbar-thumb:hover': {
    backgroundColor: 'text.disabled',
  },
});

/**
 * Returns the primary value if it is defined, non-null, and non-empty; otherwise, returns the fallback value.
 *
 * @param primary - The primary string value to check.
 * @param fallback - The fallback string value to use if the primary is undefined, null, or an empty string.
 * @returns The selected string value, or undefined if both are undefined, null, or empty.
 */
export function getSelectedValue(primary: string | undefined, fallback: string | undefined): string | undefined {
  return primary !== undefined && primary !== null && primary !== '' ? primary : fallback;
}
