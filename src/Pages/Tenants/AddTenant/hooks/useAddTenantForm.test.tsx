import {act, renderHook} from '@testing-library/react';
import {getErrorMessage} from 'Helpers/utils';
import {useCreateTenantMutation, useUpdateTenantByIdMutation} from 'redux/app/tenantManagementApiSlice';
import {vi} from 'vitest';
import {FormAddTenant, initialAddTenantValues} from '../addTenantsUtils';
import {useAddTenantForm} from './useAddTenantForm';

// Mocks
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useCreateTenantMutation: vi.fn(),
  useUpdateTenantByIdMutation: vi.fn(),
}));
vi.mock('Helpers/utils', () => ({
  getErrorMessage: vi.fn(),
}));
vi.mock('notistack', () => ({
  enqueueSnackbar: vi.fn(),
}));

describe('useAddTenantForm', () => {
  const mockUnwrap = vi.fn();
  const mockUpdateUnwrap = vi.fn();

  const setDialogOpen = vi.fn();
  const setActiveStep = vi.fn();
  const setCreatedTenant = vi.fn();

  const files: File[] = [new File(['dummy'], 'dummy.txt', {type: 'text/plain'})];

  const sampleValues: FormAddTenant = {
    ...initialAddTenantValues(),
    company: 'Test Company',
    language: 'en',
    key: 'test-company',

    overAllPlan: {
      id: 'p',
      name: '',
      price: '',
      costPerUser: '',
      allowedUnlimitedUsers: false,
      tier: '',
      currency: {
        id: 'cur1',
        symbol: '$',
        currencyCode: 'USD',
        currencyName: 'US Dollar',
        country: 'US',
      },
      configureDevice: {
        id: 'dev1',
        min: 1,
        max: 10,
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '',
        createdBy: '',
        modifiedOn: '',
        modifiedBy: '',
        computeSize: '',
        dbSize: '',
      },
      deleted: false,
      deletedOn: null,
      deletedBy: null,
      createdOn: '',
      createdBy: '',
      modifiedOn: '',
      modifiedBy: '',
      description: '',
      size: '0',
      status: '1',
      version: '1',
      billingCycleId: '',
      currencyId: '',
      configureDeviceId: '',
      planSizeId: '',
    },
    firstName: 'John',
    lastName: 'Doe',
    email: '<EMAIL>',
    mobileNumber: '1234567890',
    countryCode: {code: '+91', label: 'India'},
    designation: 'Manager',
  };

  const actions = {
    resetForm: vi.fn(),
    setErrors: vi.fn(),
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useCreateTenantMutation as unknown as ReturnType<() => any>).mockReturnValue([
      vi.fn(() => ({unwrap: mockUnwrap})),
      {isLoading: false},
    ]);
    (useUpdateTenantByIdMutation as unknown as ReturnType<() => any>).mockReturnValue([
      vi.fn(() => ({unwrap: mockUpdateUnwrap})),
      {isLoading: false},
    ]);
  });

  it('should return initial values and functions', () => {
    const {result} = renderHook(() => useAddTenantForm({files, setDialogOpen, setActiveStep, setCreatedTenant}));

    expect(result.current.initialValues).toEqual(initialAddTenantValues());
    expect(typeof result.current.formSubmit).toBe('function');
    expect(result.current.isLoading).toBe(false);
  });

  it('should handle successful form submission (create)', async () => {
    mockUnwrap.mockResolvedValue({success: true});

    const {result} = renderHook(() => useAddTenantForm({files, setDialogOpen, setActiveStep, setCreatedTenant}));

    await act(() => result.current.formSubmit(sampleValues, actions as any));

    expect(mockUnwrap).toHaveBeenCalled();
    expect(actions.resetForm).toHaveBeenCalledWith({values: initialAddTenantValues()});
    expect(setDialogOpen).toHaveBeenCalledWith(true);
  });

  it('should handle update tenant when isEdit is true', async () => {
    mockUpdateUnwrap.mockResolvedValue({success: true});

    const {result} = renderHook(() =>
      useAddTenantForm({
        files,
        setDialogOpen,
        setActiveStep,
        setCreatedTenant,
        isEdit: true,
        tenantId: 'tenant123',
        injectedValues: {...sampleValues, company: 'Old Company'}, // force change
      }),
    );

    await act(() => result.current.formSubmit(sampleValues, actions as any));

    expect(mockUpdateUnwrap).toHaveBeenCalled();
    expect(setCreatedTenant).toHaveBeenCalledWith('Test Company');
    expect(actions.resetForm).toHaveBeenCalled();
  });

  it('should handle subdomain error', async () => {
    const error = {data: {message: 'Subdomain already exists'}};
    (getErrorMessage as any).mockReturnValue('Subdomain already exists');
    mockUnwrap.mockRejectedValue(error);

    const {result} = renderHook(() => useAddTenantForm({files, setDialogOpen, setActiveStep, setCreatedTenant}));

    await act(() => result.current.formSubmit(sampleValues, actions as any));

    expect(getErrorMessage).toHaveBeenCalledWith(error);
    expect(setActiveStep).toHaveBeenCalledWith(0);
    expect(actions.setErrors).toHaveBeenCalledWith({key: 'Subdomain already exists'});
  });

  it('should handle email error', async () => {
    const error = {data: {message: 'email already exists'}};
    (getErrorMessage as any).mockReturnValue('email already exists');
    mockUnwrap.mockRejectedValue(error);

    const {result} = renderHook(() => useAddTenantForm({files, setDialogOpen, setActiveStep, setCreatedTenant}));

    await act(() => result.current.formSubmit(sampleValues, actions as any));

    expect(setActiveStep).toHaveBeenCalledWith(0);
    expect(actions.setErrors).toHaveBeenCalledWith({email: 'email already exists'});
  });

  it('should handle Duplicate feature error', async () => {
    const error = {data: {message: 'Duplicate feature'}};
    (getErrorMessage as any).mockReturnValue('Duplicate feature');
    mockUnwrap.mockRejectedValue(error);

    const {result} = renderHook(() => useAddTenantForm({files, setDialogOpen, setActiveStep, setCreatedTenant}));

    await act(() => result.current.formSubmit(sampleValues, actions as any));

    expect(setActiveStep).toHaveBeenCalledWith(1);
    expect(actions.setErrors).not.toHaveBeenCalled();
  });

  it('should do nothing for unknown error', async () => {
    const error = {data: {message: 'Some unknown error'}};
    (getErrorMessage as any).mockReturnValue('Some unknown error');
    mockUnwrap.mockRejectedValue(error);

    const {result} = renderHook(() => useAddTenantForm({files, setDialogOpen, setActiveStep, setCreatedTenant}));

    await act(() => result.current.formSubmit(sampleValues, actions as any));

    expect(setActiveStep).not.toHaveBeenCalled();
    expect(actions.setErrors).not.toHaveBeenCalled();
  });
});
