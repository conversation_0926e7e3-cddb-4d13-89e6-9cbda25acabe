import {act, renderHook} from '@testing-library/react';
import {vi} from 'vitest';
import {TenantCreationStepType} from '../addTenantsUtils';
import {useAddTenantState} from './useAddTenantState';

const mockNavigate = vi.fn();

vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

describe('useAddTenantState', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('should initialize with default state', () => {
    const {result} = renderHook(() => useAddTenantState());

    expect(result.current.activeStep).toBe(0);
    expect(result.current.nextButtonState).toBe(false);
    expect(result.current.isDialogOpen).toBe(false);
    expect(result.current.files).toEqual([]);
    expect(result.current.overAllPlan).toBeNull();
  });

  it('should handleNext correctly', () => {
    const {result} = renderHook(() => useAddTenantState());

    act(() => {
      result.current.handleNext(); // 0 -> 1
    });

    expect(result.current.activeStep).toBe(1);
    expect(result.current.nextButtonState).toBe(false);

    act(() => {
      result.current.handleNext(); // 1 -> 2
    });

    expect(result.current.activeStep).toBe(2);

    act(() => {
      result.current.handleNext(); // max 2
    });

    expect(result.current.activeStep).toBe(2); // shouldn't exceed 2
  });

  it('should handleBack correctly without overAllPlan', () => {
    const {result} = renderHook(() => useAddTenantState());

    act(() => {
      result.current.setActiveStep(2);
      result.current.handleBack(); // 2 -> 1
    });

    expect(result.current.activeStep).toBe(1);

    act(() => {
      result.current.handleBack(); // 1 -> 0
    });

    expect(result.current.activeStep).toBe(0);
  });

  it('should handleBack and reset nextButtonState if overAllPlan exists', () => {
    const {result} = renderHook(() => useAddTenantState());

    act(() => {
      result.current.setOverAllPlan({
        id: '123',
        name: 'Standard',
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '',
        createdBy: '',
        modifiedOn: '',
        modifiedBy: '',
        description: '',
        size: '0',
        price: '0',
        costPerUser: '0',
        allowedUnlimitedUsers: false,
        tier: '',
        currency: undefined,
        configureDevice: undefined,
        billingCycle: undefined,
        status: '1',
        version: '1',
        billingCycleId: '',
        currencyId: '',
        configureDeviceId: '',
        planSizeId: '',
      });
      result.current.setActiveStep(2);
      result.current.handleBack(); // 2 -> 1 with overAllPlan
    });

    expect(result.current.activeStep).toBe(1);
    expect(result.current.nextButtonState).toBe(false);
  });

  it('should set nextButtonState correctly using handleNextButton', () => {
    const {result} = renderHook(() => useAddTenantState());

    act(() => {
      result.current.handleNextButton(true);
    });

    expect(result.current.nextButtonState).toBe(true);

    act(() => {
      result.current.handleNextButton(false);
    });

    expect(result.current.nextButtonState).toBe(false);
  });

  it('should reset state and navigate on onNavigateToTenant', () => {
    const {result} = renderHook(() => useAddTenantState());

    act(() => {
      result.current.setActiveStep(2);
      result.current.setIsDialogOpen(true);
      result.current.setFiles([new File(['dummy'], 'dummy.txt')]);
      result.current.setOverAllPlan({
        id: 'plan1',
        name: 'Premium',
        deleted: false,
        deletedOn: null,
        deletedBy: null,
        createdOn: '',
        createdBy: '',
        modifiedOn: '',
        modifiedBy: '',
        description: '',
        size: '0',
        price: '0',
        costPerUser: '0',
        allowedUnlimitedUsers: false,
        tier: '',
        currency: undefined,
        configureDevice: undefined,
        billingCycle: undefined,
        status: '1',
        version: '1',
        billingCycleId: '',
        currencyId: '',
        configureDeviceId: '',
        planSizeId: '',
      });

      result.current.onNavigateToTenant();
    });

    expect(result.current.activeStep).toBe(TenantCreationStepType.TenantDetails);
    expect(result.current.files).toEqual([]);
    expect(result.current.overAllPlan).toBeNull();
    expect(result.current.isDialogOpen).toBe(false);
    expect(mockNavigate).toHaveBeenCalledWith('/add-tenant');
  });
});
