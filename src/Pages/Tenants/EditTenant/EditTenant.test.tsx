import {render, screen, waitFor} from '@testing-library/react';
import {useGetSubscriptionQuery, useGetTenantsQuery} from 'redux/app/tenantManagementApiSlice';
import {vi} from 'vitest';
import AddTenantPage from '../AddTenant/AddTenantPage';
import EditTenant from './EditTenant';

// Mock dependencies
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetTenantsQuery: vi.fn(),
  useGetSubscriptionQuery: vi.fn(),
}));

vi.mock('Components/BackdropLoader', () => ({
  __esModule: true,
  default: () => <div>Loading...</div>,
}));

vi.mock('../AddTenant/AddTenantPage', () => ({
  __esModule: true,
  default: vi.fn(() => <div>AddTenantPageMock</div>),
}));

vi.mock('Pages/utils', () => ({
  getStateOptions: vi.fn(() => Promise.resolve('NY')),
  getCityOptions: vi.fn(() => Promise.resolve('NY')),
}));

// Type-safe mocks
const mockUseGetTenantsQuery = useGetTenantsQuery as unknown as ReturnType<typeof vi.fn>;
const mockUseGetSubscriptionQuery = useGetSubscriptionQuery as unknown as ReturnType<typeof vi.fn>;

describe('EditTenant Page', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loader when tenant data is loading', () => {
    mockUseGetTenantsQuery.mockReturnValue({
      data: undefined,
      isLoading: true,
      error: null,
    });
    mockUseGetSubscriptionQuery.mockReturnValue({});

    render(<EditTenant />);

    expect(screen.getByText('Loading...')).toBeInTheDocument();
  });

  it('renders error message when tenant query fails', () => {
    mockUseGetTenantsQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: true,
    });
    mockUseGetSubscriptionQuery.mockReturnValue({});

    render(<EditTenant />);

    expect(screen.getByText(/Failed to load tenant details./i)).toBeInTheDocument();
  });

  it('renders error message when subscription query fails', () => {
    mockUseGetTenantsQuery.mockReturnValue({
      data: [{id: 'tenant-1', name: 'Test Tenant', contacts: [], address: {}, files: []}],
      isLoading: false,
      error: null,
    });
    mockUseGetSubscriptionQuery.mockReturnValue({
      data: undefined,
      isLoading: false,
      error: true,
    });

    render(<EditTenant />);

    expect(screen.getByText(/Failed to load subscription details./i)).toBeInTheDocument();
  });

  it('renders AddTenantPage with correct props when data loads', async () => {
    const tenantMock = {
      id: 'tenant-1',
      name: 'Test Tenant',
      key: 'abc.distek.com',
      lang: 'en',
      files: [{id: 'file-1'}],
      contacts: [
        {
          firstName: 'John',
          lastName: 'Doe',
          designation: 'Manager',
          email: '<EMAIL>',
          phoneNumber: '*********',
        },
      ],
      address: {city: 'NY', state: 'NY'},
    };

    const subscriptionMock = {
      id: 'sub-1',
      numberOfUsers: 10,
      totalCost: 200,
      planId: 'plan-123',
      plan: {
        billingCycleId: 'cycle-1',
        configureDeviceId: 'device-1',
        tier: 'gold',
        currency: {code: 'USD'},
        billingCycle: {id: 'cycle-1', name: 'Monthly'},
      },
    };

    mockUseGetTenantsQuery.mockReturnValue({
      data: [tenantMock],
      isLoading: false,
      error: null,
    });

    mockUseGetSubscriptionQuery.mockReturnValue({
      data: [subscriptionMock],
      isLoading: false,
      error: null,
    });

    render(<EditTenant />);

    await waitFor(() => {
      expect(AddTenantPage).toHaveBeenCalledWith(
        expect.objectContaining({
          isEdit: true,
          initialValues: expect.objectContaining({
            id: 'tenant-1',
            company: 'Test Tenant',
            firstName: 'John',
            lastName: 'Doe',
            designation: 'Manager',
            email: '<EMAIL>',
            mobileNumber: '*********',
            key: 'abc', // stripped `.distek.com`
            city: 'NY',
            state: 'NY',
            userCount: 10,
            totalCost: 200,
            overAllPlan: expect.objectContaining({
              billingCycleId: 'cycle-1',
              configureDeviceId: 'device-1',
              tier: 'gold',
            }),
          }),
          choosePlanInitialValues: expect.objectContaining({
            billingCycle: 'cycle-1',
            noOfDevices: 'device-1',
            infraConfig: 'gold',
            planId: 'plan-123',
            userCount: 10,
          }),
        }),
        undefined,
      );
    });

    expect(screen.getByText('AddTenantPageMock')).toBeInTheDocument();
  });
});
