// @vitest-environment jsdom
import '@testing-library/jest-dom';
import {fireEvent, render, screen} from '@testing-library/react';
import React from 'react';
import {vi} from 'vitest';
import FilterStatusChips from './StatusFilterChip';

const mockUseGetAllTenantStatusesQuery = vi.fn();

vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetAllTenantStatusesQuery: (...args: any[]) => mockUseGetAllTenantStatusesQuery(...args),
}));

vi.mock('../../../Components/FilterChip/FilterChip', () => ({
  __esModule: true,
  default: (props: any) => (
    <button
      data-testid={`chip-${props.label}`}
      onClick={props.onClick}
      aria-selected={props.selected}
      onDoubleClick={props.onDelete}
    >
      {props.label}
    </button>
  ),
}));

vi.mock('../../../Components/FilterChip/FilterSectionView', () => ({
  __esModule: true,
  default: (props: any) => (
    <div data-testid="section">
      {props.title}
      {props.children}
    </div>
  ),
}));

vi.mock('../TenantUtils', () => ({
  getTitleForTenantId: (id: string) => `Title-${id}`,
}));

describe('FilterStatusChips', () => {
  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state', () => {
    mockUseGetAllTenantStatusesQuery.mockReturnValue({isLoading: true});
    render(<FilterStatusChips />);
    expect(screen.getByText(/Please Wait/i)).toBeInTheDocument();
  });

  it('renders error state', () => {
    mockUseGetAllTenantStatusesQuery.mockReturnValue({isLoading: false, error: true});
    render(<FilterStatusChips />);
    expect(screen.getByText(/Something Went Wrong/i)).toBeInTheDocument();
  });

  it('renders empty state', () => {
    mockUseGetAllTenantStatusesQuery.mockReturnValue({isLoading: false, error: false, data: {statuses: {}}});
    render(<FilterStatusChips />);
    expect(screen.getByTestId('section')).toBeInTheDocument();
  });

  it('renders chips and handles selection and deletion', () => {
    mockUseGetAllTenantStatusesQuery.mockReturnValue({
      isLoading: false,
      error: false,
      data: {statuses: {active: 'Active', inactive: 'Inactive'}},
    });
    const onSelect = vi.fn();
    render(<FilterStatusChips onSelect={onSelect} />);
    // Select active
    const activeChip = screen.getByTestId('chip-Title-active');
    fireEvent.click(activeChip);
    expect(onSelect).toHaveBeenCalled();
    // Select inactive
    const inactiveChip = screen.getByTestId('chip-Title-inactive');
    fireEvent.click(inactiveChip);
    expect(onSelect).toHaveBeenCalled();
    // Delete active
    fireEvent.doubleClick(activeChip);
    expect(onSelect).toHaveBeenCalled();
  });

  it('clears selection via imperative handle', () => {
    mockUseGetAllTenantStatusesQuery.mockReturnValue({
      isLoading: false,
      error: false,
      data: {statuses: {active: 'Active'}},
    });
    const ref = React.createRef<any>();
    render(<FilterStatusChips ref={ref} value={new Set(['active'])} />);
    // Clear selection via ref
    ref.current?.clearSelection();
    // No assertion, just coverage for imperative handle
  });
});
