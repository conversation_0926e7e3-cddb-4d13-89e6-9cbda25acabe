import {Box, Typography} from '@mui/material';
import {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {useGetAllTenantStatusesQuery} from 'redux/app/tenantManagementApiSlice';
import FilterChip from '../../../Components/FilterChip/FilterChip';
import FilterSectionView from '../../../Components/FilterChip/FilterSectionView';
import {IClearFilters} from '../../../Components/FilterUtil/IClearFilters';
import {getTitleForTenantId} from '../TenantUtils';

export interface FilterStatusChipsProps {
  value?: Set<string>;
  onSelect?: (selected: Set<string>) => void;
}

export type FilterStatusChipsRef = IClearFilters;

/**
 * FilterStatusChips component that displays a set of status chips for filtering tenants.
 * It allows users to select multiple statuses and provides a way to clear the selection.
 * @param {FilterStatusChipsProps} props - The properties for the component.
 * @returns {JSX.Element} The rendered FilterStatusChips component.
 */
const FilterStatusChips = forwardRef<FilterStatusChipsRef, FilterStatusChipsProps>(({onSelect, value}, ref) => {
  const {data, isLoading, error} = useGetAllTenantStatusesQuery();
  const [selectedStatus, setSelectedStatus] = useState<Set<string>>(value ? new Set(value) : new Set());

  useEffect(() => {
    if (onSelect) {
      onSelect(new Set(selectedStatus));
    }
  }, [selectedStatus, onSelect]);

  useImperativeHandle(ref, () => ({
    clearSelection: () => {
      setSelectedStatus(new Set());
    },
  }));

  if (isLoading) {
    return <Typography>Please Wait...</Typography>;
  }
  if (error) {
    return <Typography>Something Went Wrong</Typography>;
  }
  return (
    <FilterSectionView title="Status">
      <Box sx={{display: 'flex', flexDirection: 'row', gap: 0.75, flexWrap: 'wrap', textTransform: 'capitalize'}}>
        {Object.entries(data?.statuses ?? {}).map(([key, value]) => (
          <FilterChip
            key={key}
            onClick={() => {
              if (!selectedStatus.has(key)) {
                selectedStatus.add(key);
              }
              setSelectedStatus(new Set(selectedStatus));
            }}
            label={getTitleForTenantId(key) ?? value}
            selected={selectedStatus.has(key)}
            onDelete={() => {
              selectedStatus.delete(key);
              setSelectedStatus(new Set(selectedStatus));
            }}
          />
        ))}
      </Box>
    </FilterSectionView>
  );
});
FilterStatusChips.displayName = 'FilterStatusChips';

export default FilterStatusChips;
