// @vitest-environment jsdom
import {configureStore} from '@reduxjs/toolkit';
import {fireEvent, render, screen} from '@testing-library/react';
import {Provider} from 'react-redux';
import {tenantApiSlice} from 'redux/app/tenantManagementApiSlice';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import TenantDetailsPage from './TenantDetailsPage';
import {mockSubscriptions, mockTenantBillings, mockTenantData} from './testConstants';

const createMockStore = () =>
  configureStore({
    reducer: {[tenantApiSlice.reducerPath]: tenantApiSlice.reducer},
    middleware: getDefaultMiddleware => getDefaultMiddleware().concat(tenantApiSlice.middleware),
  });

import {ThemeProvider, createTheme} from '@mui/material/styles';
import {MemoryRouter} from 'react-router-dom';
const testTheme = createTheme({
  palette: {
    white: {main: '#fff'},
    body: {'200': '#f5f5f5'},
  },
});
const renderWithProvider = (ui: React.ReactElement) =>
  render(
    <MemoryRouter>
      <ThemeProvider theme={testTheme}>
        <Provider store={createMockStore()}>{ui}</Provider>
      </ThemeProvider>
    </MemoryRouter>,
  );
// Mocks
const mockUseGetTenantByIdQuery = vi.fn();
const mockUseGetSubscriptionQuery = vi.fn();
const mockUseGetTenantBillingsQuery = vi.fn();

vi.mock('redux/app/tenantManagementApiSlice', async importOriginal => {
  const actual = (await importOriginal()) as Record<string, unknown>;
  return {
    ...(actual as object),
    tenantApiSlice: (actual as any).tenantApiSlice || {
      reducerPath: 'tenantApi',
      reducer: () => ({}),
      middleware: () => (next: any) => (action: any) => next(action),
      endpoints: {},
    },
    useGetTenantByIdQuery: (...args: unknown[]) => mockUseGetTenantByIdQuery(...args),
    useGetSubscriptionQuery: (...args: unknown[]) => mockUseGetSubscriptionQuery(...args),
  };
});

vi.mock('redux/app/invoiceManagementApiSlice', () => ({
  useGetTenantBillingsQuery: (...args: unknown[]) => mockUseGetTenantBillingsQuery(...args),
}));

vi.mock('./EllipsisText/EllipsisText', () => ({
  __esModule: true,
  default: ({text}: {text: string}) => <span>{text}</span>,
}));
vi.mock('Components/Breadcrumb/Breadcrumb', () => ({
  __esModule: true,
  default: () => <div data-testid="breadcrumb" />,
}));
vi.mock('Components/StatusChip/StatusChip', () => ({
  __esModule: true,
  default: ({label}: {label: string}) => <span>{label}</span>,
}));

describe('TenantDetailsPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders loading state', () => {
    mockUseGetTenantByIdQuery.mockReturnValue({isLoading: true});
    mockUseGetSubscriptionQuery.mockReturnValue({isLoading: false});
    mockUseGetTenantBillingsQuery.mockReturnValue({isLoading: false});

    renderWithProvider(<TenantDetailsPage />);
    expect(screen.getByText(/Loading tenant details/i)).toBeInTheDocument();
  });

  it('renders error state', () => {
    mockUseGetTenantByIdQuery.mockReturnValue({isLoading: false, error: true});
    mockUseGetSubscriptionQuery.mockReturnValue({isLoading: false});
    mockUseGetTenantBillingsQuery.mockReturnValue({isLoading: false});

    renderWithProvider(<TenantDetailsPage />);
    expect(screen.getByText(/Error loading tenant details/i)).toBeInTheDocument();
  });

  it('renders not found state', () => {
    mockUseGetTenantByIdQuery.mockReturnValue({isLoading: false, error: undefined, data: null});
    mockUseGetSubscriptionQuery.mockReturnValue({isLoading: false});
    mockUseGetTenantBillingsQuery.mockReturnValue({isLoading: false});

    renderWithProvider(<TenantDetailsPage />);
    expect(screen.getByText(/Tenant not found/i)).toBeInTheDocument();
  });

  it('renders tenant details with all sections', () => {
    mockUseGetTenantByIdQuery.mockReturnValue({isLoading: false, error: undefined, data: mockTenantData});
    mockUseGetSubscriptionQuery.mockReturnValue({isLoading: false, error: undefined, data: mockSubscriptions});
    mockUseGetTenantBillingsQuery.mockReturnValue({isLoading: false, error: undefined, data: mockTenantBillings});

    renderWithProvider(<TenantDetailsPage />);
    expect(screen.getByText('Test Tenant Company')).toBeInTheDocument();
    expect(screen.getByText('Tenant information')).toBeInTheDocument();
    expect(screen.getByText('Plan details')).toBeInTheDocument();
    expect(screen.getByText('Contact information')).toBeInTheDocument();
    expect(screen.getByText('Documents uploaded')).toBeInTheDocument();
    expect(screen.getByText('Pro Plan')).toBeInTheDocument();
    expect(
      screen.getAllByText(
        (content, node) => typeof node?.textContent === 'string' && node.textContent.includes('Contract Agreement.pdf'),
      ).length,
    ).toBeGreaterThan(0);
    expect(
      screen.getAllByText(
        (content, node) => typeof node?.textContent === 'string' && node.textContent.includes('Terms of Service.pdf'),
      ).length,
    ).toBeGreaterThan(0);
  });

  it('formats and displays contact info', () => {
    mockUseGetTenantByIdQuery.mockReturnValue({isLoading: false, error: undefined, data: mockTenantData});
    mockUseGetSubscriptionQuery.mockReturnValue({isLoading: false, error: undefined, data: mockSubscriptions});
    mockUseGetTenantBillingsQuery.mockReturnValue({isLoading: false, error: undefined, data: mockTenantBillings});

    renderWithProvider(<TenantDetailsPage />);
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('ceo')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText(/\(415\) 555-1234/)).toBeInTheDocument();
    expect(screen.getByText('San Francisco')).toBeInTheDocument();
    expect(screen.getByText('Ca')).toBeInTheDocument();
  });

  it('shows "No documents uploaded" when files are empty', () => {
    const noFiles = {...mockTenantData, files: []};
    mockUseGetTenantByIdQuery.mockReturnValue({isLoading: false, error: undefined, data: noFiles});
    mockUseGetSubscriptionQuery.mockReturnValue({isLoading: false, error: undefined, data: mockSubscriptions});
    mockUseGetTenantBillingsQuery.mockReturnValue({isLoading: false, error: undefined, data: mockTenantBillings});

    renderWithProvider(<TenantDetailsPage />);
    expect(screen.getByText('No documents uploaded')).toBeInTheDocument();
  });

  it('handles minimal tenant data gracefully', () => {
    const minimal = {
      tenantDetails: {id: 'id', name: '', status: 1, createdOn: '', key: '', address: {}},
      contact: {},
      files: [],
    };
    mockUseGetTenantByIdQuery.mockReturnValue({isLoading: false, error: undefined, data: minimal});
    mockUseGetSubscriptionQuery.mockReturnValue({isLoading: false, error: undefined, data: []});
    mockUseGetTenantBillingsQuery.mockReturnValue({isLoading: false, error: undefined, data: []});

    renderWithProvider(<TenantDetailsPage />);
    expect(screen.getByText('Unnamed Tenant')).toBeInTheDocument();
    expect(screen.getByText('No documents uploaded')).toBeInTheDocument();
  });

  it('handles document click and opens file', () => {
    mockUseGetTenantByIdQuery.mockReturnValue({isLoading: false, error: undefined, data: mockTenantData});
    mockUseGetSubscriptionQuery.mockReturnValue({isLoading: false, error: undefined, data: mockSubscriptions});
    mockUseGetTenantBillingsQuery.mockReturnValue({isLoading: false, error: undefined, data: mockTenantBillings});

    const openSpy = vi.spyOn(window, 'open').mockImplementation(() => null);

    renderWithProvider(<TenantDetailsPage />);
    // Find the document name node
    const docNode = screen.getByText('Contract Agreement.pdf');
    // Find the clickable document item container
    const clickable = docNode.closest('.MuiBox-root.css-1poh1gt');
    if (!clickable) {
      throw new Error('No clickable element found for Contract Agreement.pdf');
    }
    fireEvent.click(clickable);
    expect(openSpy).toHaveBeenCalledWith('https://example.com/contract.pdf', '_blank', 'noopener,noreferrer');
    openSpy.mockRestore();
  });
});
