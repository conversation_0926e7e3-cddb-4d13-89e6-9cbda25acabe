// Mocks for TenantDetailsPage.test.tsx

export const mockTenantData = {
  tenantDetails: {
    id: 'tenant-1',
    name: 'Test Tenant Company',
    status: 1,
    createdOn: '2023-01-01',
    key: 'tenant-key',
    address: {
      city: 'San Francisco',
      state: 'CA',
    },
  },
  contact: {
    firstName: '<PERSON>',
    lastName: 'Doe',
    designation: 'ceo',
    email: '<EMAIL>',
    phoneNumber: '4155551234',
    countryCode: '+1',
  },
  files: [
    {
      id: 'file-1',
      tenantId: 'tenant-1',
      fileKey: 'contract-agreement',
      originalName: 'Contract Agreement.pdf',
      source: 1,
      size: 123456,
      signedUrl: 'https://example.com/contract.pdf',
    },
    {
      id: 'file-2',
      tenantId: 'tenant-1',
      fileKey: 'terms-of-service',
      originalName: 'Terms of Service.pdf',
      source: 1,
      size: 654321,
      signedUrl: 'https://example.com/terms.pdf',
    },
  ],
};

export const mockSubscriptions = [
  {
    plan: {
      name: 'Pro Plan',
      billingCycle: {cycleName: 'Monthly'},
      allowedUnlimitedUsers: false,
      costPerUser: 100,
      price: 1000,
      status: '0',
    },
    numberOfUsers: 10,
    totalCost: 1000,
    startDate: '2023-01-01',
    endDate: '2024-01-01',
  },
];

export const mockTenantBillings = [
  {
    invoiceId: 'INV-001',
    amount: 1000,
    createdOn: '2023-02-01',
    status: 'paid',
  },
];
