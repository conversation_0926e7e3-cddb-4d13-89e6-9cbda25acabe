import {describe, expect, it} from 'vitest';
import {getTitleForTenantId} from './TenantUtils';

describe('getTitleForTenantId', () => {
  it('returns "Active" for tenantId "0"', () => {
    expect(getTitleForTenantId('0')).toBe('Active');
  });

  it('returns "Pending Provision" for tenantId "1"', () => {
    expect(getTitleForTenantId('1')).toBe('Pending Provision');
  });

  it('returns "Provisioning" for tenantId "2"', () => {
    expect(getTitleForTenantId('2')).toBe('Provisioning');
  });

  it('returns "Provision Failed" for tenantId "3"', () => {
    expect(getTitleForTenantId('3')).toBe('Provision Failed');
  });

  it('returns "Inactive" for tenantId "4"', () => {
    expect(getTitleForTenantId('4')).toBe('Inactive');
  });

  it('returns undefined for unknown tenantId', () => {
    expect(getTitleForTenantId('999')).toBeUndefined();
    expect(getTitleForTenantId('')).toBeUndefined();
    expect(getTitleForTenantId('abc')).toBeUndefined();
  });
});
