import {CellContext} from '@tanstack/react-table';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import StatusChip from 'Components/StatusChip/StatusChip';
import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import {TenantType} from 'redux/app/types';
import {ActionButtons} from './TenantPage';

export const DEFAULT_LIMIT = 5;
export const DEFAULT_OFFSET = 0;

const whiteMain = 'white.main';

export enum TenantStatus {
  ACTIVE, // Tenant is active and fully functional
  PENDINGPROVISION, // Tenant is awaiting provisioning
  PROVISIONING, // Tenant is currently being provisioned
  PROVISIONFAILED, // Provisioning process failed
  INACTIVE, // Tenant is inactive
  PENDINGONBOARDING, // Tenant is active but not onboarded due to missing information
}

export const tenantStatusMapToStatusChip: Record<TenantStatus, StatusChipState> = {
  [TenantStatus.ACTIVE]: StatusChipState.ACTIVE,
  [TenantStatus.PENDINGPROVISION]: StatusChipState.PENDINGPROVISION,
  [TenantStatus.PROVISIONING]: StatusChipState.PROVISIONING,
  [TenantStatus.PROVISIONFAILED]: StatusChipState.PROVISIONFAILED,
  [TenantStatus.INACTIVE]: StatusChipState.INACTIVE,
  [TenantStatus.PENDINGONBOARDING]: StatusChipState.PENDINGONBOARDING,
};

export const getStatusLabel = (status: TenantStatus | number): string => {
  const statusLabelMap: Record<TenantStatus | number, string> = {
    [TenantStatus.ACTIVE]: 'Active',
    [TenantStatus.PENDINGPROVISION]: 'Pending Provision',
    [TenantStatus.INACTIVE]: 'Inactive',
    [TenantStatus.PROVISIONFAILED]: 'Failed Provision',
    [TenantStatus.PROVISIONING]: 'Provisioning',
    [TenantStatus.PENDINGONBOARDING]: 'Pending Onboarding',
  };
  return statusLabelMap[status] || '';
};

interface TenantTableRow extends TenantType {}

interface TenantTableColumn {
  header: string;
  accessorKey?: keyof TenantTableRow;
  id?: string;
  cell?: (context: CellContext<TenantTableRow, unknown>) => React.ReactNode;
}

const columnNameMap: Record<string, string> = {
  tenantName: 'name',
  status: 'status',
  createdDate: 'createdOn',
  planName: 'name',
};

export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

export const tenantTableColumns: TenantTableColumn[] = [
  {
    header: 'Tenant name',
    accessorKey: 'name',
    id: 'tenantName',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => (
      <EllipsisText text={row.original.name && row.original.name.length > 0 ? row.original.name : '-'} />
    ),
  },
  {
    header: 'Status',
    accessorKey: 'status',
    id: 'status',
    cell: (context: CellContext<TenantTableRow, unknown>) => {
      const status = context.getValue() as TenantStatus;
      const label = getStatusLabel(status);
      return <StatusChip label={label} status={tenantStatusMapToStatusChip[status]} />;
    },
  },
  {
    header: 'Created date',
    accessorKey: 'createdOn',
    id: 'createdDate',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => {
      const date = new Date(row.original.createdOn);
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
      });
    },
  },
  {
    header: 'Plan name',
    accessorKey: 'planName',
    id: 'planName',
    cell: ({row}: CellContext<TenantTableRow, unknown>) => {
      const planName = row.original.planName;
      return <EllipsisText text={planName && planName.length > 0 ? planName : '-'} />;
    },
  },
  {
    header: 'Actions',
    cell: (cellContext: CellContext<TenantTableRow, unknown>) => (
      <ActionButtons row={cellContext as CellContext<unknown, unknown>} />
    ),
  },
];
