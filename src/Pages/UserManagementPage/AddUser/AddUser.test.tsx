import {fireEvent, render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {emailRegEx} from 'Constants/enums';
import {renderWithTheme} from 'TestHelper/TestHelper';
import * as ReactRouter from 'react-router';
import {MemoryRouter} from 'react-router';
import {useAddUserMutation, useGetRolesQuery, useUpdateUserMutation} from 'redux/app/tenantManagementApiSlice';
import {vi} from 'vitest';
import * as Yup from 'yup';
import AddUsersPage from './AddUserPage';
import {useGetAddUserLocationMetaData} from './addUser.util';
import {useCheckExistingEmail} from './hooks/useCheckExistingEmail';

// Mocks
vi.mock('./addUser.util', async original => ({
  getAddUserFormValidationSchema: vi.fn(() => {
    Yup.object({
      items: Yup.array()
        .of(
          Yup.object().shape({
            fullName: Yup.string().trim().required('Full Name is required'),
            email: Yup.string().trim().required('Email is required').matches(emailRegEx, 'Invalid email'),
            role: Yup.string().oneOf(['admin'], 'Invalid role').required('Role is required'),
          }),
        )
        .test('unique-email', 'Duplicate emails are not allowed', async list => {
          if (!list) {
            return true; // skip check if list is empty or invalid
          }
          const unique = new Set(list.map(item => item.email));
          return unique.size === list.length;
        }),
    });
  }),
  useGetAddUserLocationMetaData: vi.fn(),
  breadcrumbsData: vi.fn(() => ['listing', 'users']),
}));
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetRolesQuery: vi.fn(() => {
    return {
      data: [
        {
          id: 'admin',
          name: 'Admin',
        },
      ],
      isLoading: false,
      error: null,
    };
  }),
  useUpdateUserMutation: vi.fn(),
  useAddUserMutation: vi.fn(),
}));
vi.mock('./hooks/useCheckExistingEmail', () => ({
  useCheckExistingEmail: vi.fn(),
}));

vi.mock('notistack', () => ({
  enqueueSnackbar: vi.fn(),
}));

const mockNavigate = vi.fn();
const mockUpdateUser = vi.fn();
const mockAddUser = vi.fn();
const mockCheckEmailAddressExists = vi.fn();

vi.mock('react-router-dom', () => ({
  useNavigate: () => mockNavigate,
}));

describe('AddUsersPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useUpdateUserMutation as any).mockReturnValue([mockUpdateUser]);
    (useAddUserMutation as any).mockReturnValue([mockAddUser]);
    (useCheckExistingEmail as any).mockReturnValue({checkEmailAddressExists: mockCheckEmailAddressExists});
  });

  it('renders loader when roles are loading', () => {
    (useGetAddUserLocationMetaData as any).mockReturnValue({mode: 'add', user: null});
    (useGetRolesQuery as any).mockReturnValue({isLoading: true, data: null, error: null});
    render(
      <MemoryRouter>
        <AddUsersPage />
      </MemoryRouter>,
    );
    expect(screen.getByTestId('circularProgress')).toBeInTheDocument();
  });

  it('renders error when roles error', () => {
    (useGetAddUserLocationMetaData as any).mockReturnValue({mode: 'add', user: null});
    (useGetRolesQuery as any).mockReturnValue({isLoading: false, data: null, error: true});
    render(
      <MemoryRouter>
        <AddUsersPage />
      </MemoryRouter>,
    );
    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
  });

  it('redirects to home if edit mode and no user', async () => {
    (useGetAddUserLocationMetaData as any).mockReturnValue({mode: 'edit', user: null});
    (useGetRolesQuery as any).mockReturnValue({isLoading: false, data: [], error: null});
    const mockedNavigate = vi.fn();
    vi.spyOn(ReactRouter, 'useNavigate').mockReturnValue(mockedNavigate);
    renderWithTheme(
      <MemoryRouter>
        <AddUsersPage />
      </MemoryRouter>,
    );
    await waitFor(() => {
      expect(mockedNavigate).toHaveBeenCalled();
    });
  });

  it('submits form and calls updateUser successfully', async () => {
    const user = {
      email: '<EMAIL>',
      fullName: 'abc',
      roleId: 'admin',
    };
    (useGetAddUserLocationMetaData as any).mockReturnValue({mode: 'edit', user: user});
    mockUpdateUser.mockReturnValueOnce({unwrap: () => Promise.resolve()});

    renderWithTheme(
      <MemoryRouter>
        <AddUsersPage />
      </MemoryRouter>,
    );

    // Fill out Formik form fields (assuming labels in AddUserPageFormikForm)
    fireEvent.change(screen.getByPlaceholderText(/email/i), {target: {value: '<EMAIL>'}});
    fireEvent.change(screen.getByPlaceholderText(/full name/i), {target: {value: 'John Doe'}});
    // open the select dropdown
    const roleSelect = screen.getByTestId('role-select');
    await userEvent.click(roleSelect);

    // now select the option by text
    // Try both role and text, as MUI sometimes doesn't use role="option"
    // Open dropdown with click and keyboard
    await userEvent.click(roleSelect);
    await userEvent.type(roleSelect, '{arrowdown}');
    const adminOption = screen.queryByRole('option', {name: /admin/i}) || screen.getByText(/admin/i);
    expect(adminOption).toBeInTheDocument();
    await userEvent.click(adminOption);

    // Submit
    fireEvent.click(screen.getByTestId('submit-button'));

    await waitFor(() => {
      expect(mockUpdateUser).toHaveBeenCalledWith({
        fullName: 'John Doe',
        roleId: 'admin',
        userId: '',
      });
    });
  });
});
