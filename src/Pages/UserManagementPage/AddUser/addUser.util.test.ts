import {RouteNames} from 'Routes/routeNames';
import {describe, expect, it, vi} from 'vitest';
import {breadcrumbsData, getAddUserFormValidationSchema, useGetAddUserLocationMetaData} from './addUser.util';

// Mock for useLocation
vi.mock('react-router', () => ({
  useLocation: () => ({
    pathname: '/edit-user',
    state: {user: {id: '1', fullName: '<PERSON>', email: '<EMAIL>', role: 'admin'}},
  }),
}));

describe('getAddUserFormValidationSchema', () => {
  const roles = ['admin', 'user'];
  const validUser = {
    fullName: '<PERSON>',
    email: '<EMAIL>',
    role: 'admin',
  };

  it('should validate a correct user', async () => {
    const schema = getAddUserFormValidationSchema(roles);
    await expect(schema.validate({items: [validUser]})).resolves.toBeTruthy();
  });

  it('should fail if fullName is too short', async () => {
    const schema = getAddUserFormValidationSchema(roles);
    await expect(schema.validate({items: [{...validUser, fullName: 'John'}]})).rejects.toThrow(/at least 7 characters/);
  });

  it('should fail if email is invalid', async () => {
    const schema = getAddUserFormValidationSchema(roles);
    await expect(schema.validate({items: [{...validUser, email: 'invalid'}]})).rejects.toThrow(
      /is not in a valid format/,
    );
  });

  it('should fail if role is not in roles', async () => {
    const schema = getAddUserFormValidationSchema(roles);
    await expect(schema.validate({items: [{...validUser, role: 'guest'}]})).rejects.toThrow(/Invalid role/);
  });

  it('should call checkEmail for uniqueness', async () => {
    const checkEmail = vi.fn().mockResolvedValue(false);
    const schema = getAddUserFormValidationSchema(roles, checkEmail);
    await expect(schema.validate({items: [validUser]})).rejects.toThrow(/already exists/);
    expect(checkEmail).toHaveBeenCalledWith(validUser.email, 'items[0].email');
  });
});

describe('breadcrumbsData', () => {
  it('should return Add User breadcrumb for add mode', () => {
    const result = breadcrumbsData('add');
    expect(result[1].label).toBe('Add User');
    expect(result[1].url).toBe(RouteNames.ADD_USER);
  });

  it('should return Edit User breadcrumb for edit mode', () => {
    const result = breadcrumbsData('edit');
    expect(result[1].label).toBe('Edit User');
    expect(result[1].url).toBe(RouteNames.EDIT_USER);
  });
});

describe('useGetAddUserLocationMetaData', () => {
  it('should return edit mode and user from location', () => {
    const meta = useGetAddUserLocationMetaData();
    expect(meta.mode).toBe('edit');
    expect(meta.user).toEqual({id: '1', fullName: 'John Doe', email: '<EMAIL>', role: 'admin'});
  });
});
