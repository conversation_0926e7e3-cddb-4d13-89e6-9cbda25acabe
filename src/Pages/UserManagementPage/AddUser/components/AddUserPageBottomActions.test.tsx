import {fireEvent, render, screen} from '@testing-library/react';
import {Form, Formik, useFormikContext} from 'formik';
import {AnyObject} from 'Helpers/utils';
import {MemoryRouter} from 'react-router';
import {beforeEach, describe, expect, it, Mock, vi} from 'vitest';
import AddUserPageBottomAction, {AddUserPageBottomActionProps} from './AddUserPageBottomActions';

// Mock BlueButton and BorderButton
vi.mock('Components/BlueButton/BlueButton', () => ({
  default: (props: any) => <button {...props}>{props.children}</button>,
}));
vi.mock('Components/BorderButton/BorderButton', () => ({
  default: (props: any) => <button {...props}>{props.children}</button>,
}));
const handleSubmit = vi.fn();
vi.mock('formik', async original => {
  const org = await original();
  return {
    ...(org as AnyObject),
    useFormikContext: vi.fn(() => ({
      values: {items: [{fullName: '', email: '', role: ''}]},
      handleSubmit: handleSubmit,
      isSubmitting: false,
      isValid: true,
      dirty: true,
      isValidating: false,
    })),
  };
});

// Mock useNavigate
const mockNavigate = vi.fn();
vi.mock('react-router', async original => ({
  ...(await original()),
  useNavigate: () => mockNavigate,
}));

const renderWithFormik = (
  props: Partial<AddUserPageBottomActionProps>,
  initialItem = {fullName: '', email: '', role: ''},
) => {
  return render(
    <MemoryRouter>
      <Formik initialValues={{items: [initialItem]}} onSubmit={vi.fn()}>
        <Form>
          <AddUserPageBottomAction mode={props.mode || 'add'} {...props} />
        </Form>
      </Formik>
    </MemoryRouter>,
  );
};

describe('AddUserPageBottomAction', () => {
  beforeEach(() => {
    mockNavigate.mockClear();
  });

  it('renders Cancel and Add User(s) buttons in add mode', () => {
    renderWithFormik({mode: 'add'});
    expect(screen.getByText('Cancel')).toBeInTheDocument();
    expect(screen.getByText('Add User(s)')).toBeInTheDocument();
  });

  it('renders Update button in edit mode', () => {
    renderWithFormik({mode: 'edit'});
    expect(screen.getByText('Update')).toBeInTheDocument();
  });

  it('calls navigate(-1) when Cancel is clicked', () => {
    renderWithFormik({mode: 'add'});
    fireEvent.click(screen.getByText('Cancel'));
    expect(mockNavigate).toHaveBeenCalledWith(-1);
  });

  it('calls handleSubmit when submit button is clicked', () => {
    renderWithFormik(
      {mode: 'add'},
      {
        fullName: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
      },
    );
    const submitBtn = screen.getByTestId('submit-button');
    fireEvent.click(submitBtn);
    expect(submitBtn).not.toBeDisabled();
    expect(handleSubmit).toHaveBeenCalled();
  });

  it('disables submit button when canSubmit is false', () => {
    (useFormikContext as Mock).mockReturnValue({
      isSubmitting: true,
      isValid: false,
      dirty: true,
      isValidating: true,
    });
    renderWithFormik({mode: 'add'});
    expect(screen.getByTestId('submit-button')).toBeDisabled();
  });

  it('shows loading indicator when isSubmitting is true', async () => {
    (useFormikContext as Mock).mockReturnValue({
      isSubmitting: true,
      isValid: true,
      dirty: false,
      isValidating: false,
    });
    renderWithFormik({mode: 'edit'});
    const button = screen.getByTestId('submit-button');
    expect(button).toHaveTextContent(/updating/i);
  });

  it('shows loading indicator for add mode', () => {
    (useFormikContext as Mock).mockReturnValue({
      isSubmitting: true,
      isValid: true,
      dirty: true,
    });
    renderWithFormik({mode: 'add'});
    const button = screen.getByTestId('submit-button');
    expect(button).toHaveTextContent(/Adding/i);
  });
});
