import {Box, BoxProps} from '@mui/material';
import BlueButton from 'Components/BlueButton/BlueButton';
import BorderButton from 'Components/BorderButton/BorderButton';
import {useFormikContext} from 'formik';
import {useNavigate} from 'react-router';
import {AddUserPageMode} from '../addUser.util';

/**
 * User form data
 * Represents the structure of the user form fields
 */
export interface UserForm {
  fullName: string;
  email: string;
  role: string;
}

/**
 * Props for the bottom action buttons on the Add User page
 */
export interface AddUserPageBottomActionProps extends BoxProps {
  mode: AddUserPageMode;
}

/**
 * Bottom action buttons for the Add User page
 * @param param - Props for the bottom action buttons
 * @returns JSX.Element
 */
const AddUserPageBottomAction: React.FC<AddUserPageBottomActionProps> = ({mode, ...rest}) => {
  const {handleSubmit, isSubmitting, isValid, dirty, isValidating} = useFormikContext<{items: UserForm[]}>();
  const canSubmit = !isSubmitting && isValid && dirty && !isValidating;
  const navigate = useNavigate();

  const getUpdateTitle = () => {
    if (isSubmitting) {
      return 'Updating...';
    }
    return 'Update';
  };

  const getAddUserTitle = () => {
    if (isSubmitting) {
      return 'Adding...';
    }
    return 'Add User(s)';
  };

  return (
    <Box {...rest} display="flex" justifyContent="flex-end" gap={2}>
      <BorderButton
        data-testid="close-button"
        onClick={() => {
          navigate(-1);
        }}
      >
        Cancel
      </BorderButton>
      <BlueButton
        data-testid="submit-button"
        loadingPosition="end"
        loading={isSubmitting}
        disabled={!canSubmit}
        onClick={() => handleSubmit()}
      >
        {mode == 'edit' ? getUpdateTitle() : getAddUserTitle()}
      </BlueButton>
    </Box>
  );
};

export default AddUserPageBottomAction;
