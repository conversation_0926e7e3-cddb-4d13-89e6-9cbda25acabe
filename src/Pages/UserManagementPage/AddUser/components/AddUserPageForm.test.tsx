// src/Pages/UserManagementPage/AddUser/components/AddUserPageForm.test.tsx
import {fireEvent, screen} from '@testing-library/react';
import {Formik} from 'formik';
import {renderWithTheme} from 'TestHelper/TestHelper';
import AddUserPageFormikForm from './AddUserPageForm';

describe('AddUserPageFormikForm', () => {
  const initialValues = {items: [{fullName: '', email: '', role: ''}]};

  it('renders form fields for a user', () => {
    renderWithTheme(
      <Formik initialValues={initialValues} onSubmit={() => {}}>
        <AddUserPageFormikForm mode="add" />
      </Formik>,
    );
    expect(screen.getByPlaceholderText('Enter full name')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter email')).toBeInTheDocument();
    expect(screen.getByText('Select role')).toBeInTheDocument();
  });

  it('adds another user when "+ Add another user" is clicked', () => {
    renderWithTheme(
      <Formik initialValues={initialValues} onSubmit={() => {}}>
        <AddUserPageFormikForm mode="add" />
      </Formik>,
    );
    fireEvent.click(screen.getByText('+ Add another user'));
    expect(screen.getAllByPlaceholderText('Enter full name').length).toBe(2);
  });
});
