import {act, renderHook} from '@testing-library/react';
import {vi} from 'vitest';
import {useCheckExistingEmail} from './useCheckExistingEmail';

// Mock useLazyGetTenantUserQuery
const mockGetTenantUser = vi.fn();

vi.mock('redux/auth/authApiSlice', () => ({
  useLazyGetUserListsQuery: () => [mockGetTenantUser],
}));
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useLazyGetUserByEmailQuery: () => [mockGetTenantUser],
}));

describe('useCheckExistingEmail', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should return true if email does not exist', async () => {
    mockGetTenantUser.mockReturnValue({
      unwrap: vi.fn().mockResolvedValue([]),
    });

    const {result} = renderHook(() => useCheckExistingEmail());
    let exists: boolean | undefined;
    await act(async () => {
      exists = await result.current.checkEmailAddressExists('<EMAIL>', 'test-path');
    });
    expect(exists).toBe(true);
    expect(mockGetTenantUser).toHaveBeenCalledWith('<EMAIL>');
  });

  it('should return false if email exists', async () => {
    mockGetTenantUser.mockReturnValue({
      unwrap: vi.fn().mockResolvedValue([{id: 1, email: '<EMAIL>'}]),
    });

    const {result} = renderHook(() => useCheckExistingEmail());
    let exists: boolean | undefined;
    await act(async () => {
      exists = await result.current.checkEmailAddressExists('<EMAIL>', 'test-path');
    });
    expect(exists).toBe(false);
  });

  it('should cache results for the same email', async () => {
    mockGetTenantUser.mockReturnValue({
      unwrap: vi.fn().mockResolvedValue([]),
    });

    const {result} = renderHook(() => useCheckExistingEmail());
    let first: boolean | undefined;
    let second: boolean | undefined;
    await act(async () => {
      first = await result.current.checkEmailAddressExists('<EMAIL>', 'path1');
      second = await result.current.checkEmailAddressExists('<EMAIL>', 'path2');
    });
    expect(first).toBe(true);
    expect(second).toBe(true);
    expect(mockGetTenantUser).toHaveBeenCalledTimes(1);
  });

  it('should return false if getTenantUser throws error', async () => {
    mockGetTenantUser.mockReturnValue({
      unwrap: vi.fn().mockRejectedValue(new Error('API error')),
    });

    const {result} = renderHook(() => useCheckExistingEmail());
    let exists: boolean | undefined;
    await act(async () => {
      exists = await result.current.checkEmailAddressExists('<EMAIL>', 'error-path');
    });
    expect(exists).toBe(false);
  });

  it('should debounce calls for the same path', async () => {
    mockGetTenantUser.mockReturnValue({
      unwrap: vi.fn().mockResolvedValue([]),
    });

    const {result} = renderHook(() => useCheckExistingEmail());
    let resolved: boolean | undefined;
    await act(async () => {
      result.current.checkEmailAddressExists('<EMAIL>', 'debounce-path');
      const promise2 = result.current.checkEmailAddressExists('<EMAIL>', 'debounce-path');
      resolved = await promise2;
    });
    expect(resolved).toBe(true);
    expect(mockGetTenantUser).toHaveBeenCalledTimes(1);
  });
});
