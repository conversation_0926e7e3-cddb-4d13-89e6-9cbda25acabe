import {useCallback, useRef} from 'react';
import {useLazyGetUserByEmailQuery} from 'redux/app/tenantManagementApiSlice';

const DebouncedTimeout = 500;

/**
 * Custom hook to check if an email address already exists
 * @returns {Object} - An object containing the checkEmailAddressExists function
 */
export const useCheckExistingEmail = () => {
  const timeoutRef = useRef<Record<string, NodeJS.Timeout | undefined>>({});
  const lastResolve = useRef<Record<string, (value: boolean) => void>>({});
  const cacheRef = useRef<Map<string, boolean>>(new Map()); // cache for results
  const [getUserLists] = useLazyGetUserByEmailQuery();

  const checkEmailAddressExists = useCallback(
    async (email: string, path: string) => {
      const cacheKey = `${email}`;

      // Return from cache if available
      if (cacheRef.current.has(cacheKey)) {
        return cacheRef.current.get(cacheKey)!;
      }

      return new Promise<boolean>(resolve => {
        if (timeoutRef.current[path]) clearTimeout(timeoutRef.current[path]);
        if (lastResolve.current[path]) lastResolve.current[path](true);

        lastResolve.current[path] = resolve;

        timeoutRef.current[path] = setTimeout(async () => {
          try {
            const users = await getUserLists(email).unwrap();

            const result = (users ?? []).length === 0;
            cacheRef.current.set(cacheKey, result); // store in cache
            resolve(result);
          } catch {
            cacheRef.current.set(cacheKey, false); // cache negative as well
            resolve(false);
          }
        }, DebouncedTimeout);
      });
    },
    [getUserLists],
  );

  return {checkEmailAddressExists};
};
