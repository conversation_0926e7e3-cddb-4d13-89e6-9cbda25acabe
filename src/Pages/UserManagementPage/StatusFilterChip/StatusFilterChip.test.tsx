import {fireEvent, screen} from '@testing-library/react';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {vi} from 'vitest';
import {useGetAllUserViewStatusesQuery} from '../../../redux/auth/authApiSlice';
import FilterStatusChips from './StatusFilterChip';

// Mock dependencies
vi.mock('../../../redux/auth/authApiSlice', () => ({
  useGetAllUserViewStatusesQuery: vi.fn(),
}));
vi.mock('Components/FilterChip/FilterChip', () => ({
  __esModule: true,
  default: ({label, selected, onClick, onDelete}: any) => (
    <div data-testid={`chip-${label}`} onClick={onClick}>
      {label}
      {selected && <span data-testid={`selected-${label}`}>selected</span>}
      <button data-testid={`delete-${label}`} onClick={onDelete}>
        delete
      </button>
    </div>
  ),
}));
vi.mock('Components/UserStatusChip/UserStatusChip', () => ({
  getUserStatusLabel: (key: number) => `Status${key}`,
}));
vi.mock('../../../Components/FilterChip/FilterSectionView', () => ({
  __esModule: true,
  default: ({children}: any) => <div>{children}</div>,
}));
const mockUseGetAllUserViewStatusesQuery = vi.mocked(useGetAllUserViewStatusesQuery);
describe('FilterStatusChips', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('shows loader when loading', () => {
    mockUseGetAllUserViewStatusesQuery.mockReturnValue({
      isLoading: true,
      data: null,
      error: null,
      refetch: vi.fn(),
    });
    renderWithTheme(<FilterStatusChips />);
    expect(screen.getByRole('progressbar')).toBeInTheDocument();
  });

  it('shows error message on error', () => {
    mockUseGetAllUserViewStatusesQuery.mockReturnValue({
      isLoading: false,
      data: null,
      error: true,
      refetch: vi.fn(),
    });
    renderWithTheme(<FilterStatusChips />);
    expect(screen.getByText(/Something went wrong/i)).toBeInTheDocument();
  });

  it('renders chips and handles select/delete', () => {
    const statuses = {'1': 'Active', '2': 'Inactive'};
    mockUseGetAllUserViewStatusesQuery.mockReturnValue({
      isLoading: false,
      data: statuses,
      error: null,
      refetch: vi.fn(),
    });

    const handleSelect = vi.fn();
    renderWithTheme(<FilterStatusChips onSelect={handleSelect} />);

    // Chips rendered
    expect(screen.getByTestId('chip-Status1')).toBeInTheDocument();
    expect(screen.getByTestId('chip-Status2')).toBeInTheDocument();

    // Select chip
    fireEvent.click(screen.getByTestId('chip-Status1'));
    expect(handleSelect).toHaveBeenCalled();

    // Delete chip
    fireEvent.click(screen.getByTestId('delete-Status1'));
    expect(handleSelect).toHaveBeenCalled();
  });
});
