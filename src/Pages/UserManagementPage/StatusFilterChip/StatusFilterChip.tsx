import {Box, CircularProgress} from '@mui/material';
import FilterChip from 'Components/FilterChip/FilterChip';
import {getUserStatusLabel} from 'Components/UserStatusChip/UserStatusChip';
import {forwardRef, useEffect, useImperativeHandle, useState} from 'react';
import {useGetAllUserViewStatusesQuery} from 'redux/auth/authApiSlice';
import FilterSectionView from '../../../Components/FilterChip/FilterSectionView';
import {IClearFilters} from '../../../Components/FilterUtil/IClearFilters';

export interface FilterStatusChipsProps {
  value?: Set<string>;
  onSelect?: (selected: Set<string>) => void;
}

export type FilterStatusChipsRef = IClearFilters;

/**
 * FilterStatusChips component that displays a set of status chips for filtering tenants.
 * It allows users to select multiple statuses and provides a way to clear the selection.
 * @param {FilterStatusChipsProps} props - The properties for the component.
 * @returns {JSX.Element} The rendered FilterStatusChips component.
 */

const FilterStatusChips = forwardRef<FilterStatusChipsRef, FilterStatusChipsProps>(({onSelect, value}, ref) => {
  const [selectedStatus, setSelectedStatus] = useState<Set<string>>(value ? new Set(value) : new Set());

  const {data: allStatuses, isLoading, error} = useGetAllUserViewStatusesQuery();

  useEffect(() => {
    if (onSelect) {
      onSelect(new Set(selectedStatus));
    }
  }, [selectedStatus, onSelect]);

  useImperativeHandle(ref, () => ({
    clearSelection: () => {
      setSelectedStatus(new Set());
    },
  }));

  const buildContent = () => {
    if (isLoading) {
      return <CircularProgress />;
    }
    if (error) {
      return <div>Something went wrong</div>;
    }
    return (
      <Box sx={{display: 'flex', flexDirection: 'row', gap: 0.75, flexWrap: 'wrap', textTransform: 'capitalize'}}>
        {Object.entries(allStatuses ?? {}).map(([key]) => (
          <FilterChip
            key={key}
            onClick={() => {
              if (!selectedStatus.has(key)) {
                selectedStatus.add(key);
              }
              setSelectedStatus(new Set(selectedStatus));
            }}
            label={getUserStatusLabel(Number(key))}
            selected={selectedStatus.has(key)}
            onDelete={() => {
              selectedStatus.delete(key);
              setSelectedStatus(new Set(selectedStatus));
            }}
          />
        ))}
      </Box>
    );
  };

  return <FilterSectionView title="Status">{buildContent()}</FilterSectionView>;
});
FilterStatusChips.displayName = 'FilterStatusChips';

export default FilterStatusChips;
