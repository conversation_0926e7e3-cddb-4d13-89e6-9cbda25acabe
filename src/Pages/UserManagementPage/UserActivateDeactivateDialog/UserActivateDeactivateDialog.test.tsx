import {ThemeProvider, createTheme} from '@mui/material/styles';
import {fireEvent, render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import UserActivateDeactivateDialog from './UserActivateDeactivateDialog';

const baseProps = {
  open: true,
  onClose: vi.fn(),
  onConfirm: vi.fn(),
  actionType: 'Activate' as const,
  userName: 'Test User',
  email: '<EMAIL>',
  roleName: 'Admin',
  isLoading: false,
};

const renderWithTheme = (ui: React.ReactElement) => {
  const theme = createTheme({
    palette: {
      alert: {
        success: {bg: '#d0f5e8'},
        error: {bg: '#f5d0d0'},
      },
      white: {200: '#fff'},
      body: {dark: '#222', 100: '#eee', 900: '#111', 500: '#888'},
      secondary: {50: '#f0f0f0', 700: '#333', main: '#1976d2'},
      other: {white: '#fff'},
    },
  } as any);
  return render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);
};

describe('UserActivateDeactivateDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders activate dialog with correct content', () => {
    renderWithTheme(<UserActivateDeactivateDialog {...baseProps} />);
    expect(screen.getByText('Activate User')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to activate this user?')).toBeInTheDocument();
    expect(screen.getByText('Test User')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Activate/i})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Cancel/i})).toBeInTheDocument();
  });

  it('renders deactivate dialog with correct content', () => {
    renderWithTheme(<UserActivateDeactivateDialog {...baseProps} actionType="Deactivate" />);
    expect(screen.getByText('Deactivate User')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to deactivate this user?')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Deactivate/i})).toBeInTheDocument();
  });

  it('calls onClose when Cancel is clicked', () => {
    renderWithTheme(<UserActivateDeactivateDialog {...baseProps} />);
    fireEvent.click(screen.getByRole('button', {name: /Cancel/i}));
    expect(baseProps.onClose).toHaveBeenCalled();
  });

  it('calls onConfirm when Activate/Deactivate is clicked', () => {
    renderWithTheme(<UserActivateDeactivateDialog {...baseProps} />);
    fireEvent.click(screen.getByRole('button', {name: /Activate/i}));
    expect(baseProps.onConfirm).toHaveBeenCalled();
  });

  it('disables buttons when isLoading is true', () => {
    renderWithTheme(<UserActivateDeactivateDialog {...baseProps} isLoading />);
    expect(screen.getByRole('button', {name: /Activate/i})).toBeDisabled();
    expect(screen.getByRole('button', {name: /Cancel/i})).toBeDisabled();
  });
});
