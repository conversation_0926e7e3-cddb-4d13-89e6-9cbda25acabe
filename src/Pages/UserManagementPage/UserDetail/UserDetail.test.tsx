import {fireEvent, screen, waitFor} from '@testing-library/react';
import PermissionKey from 'Constants/enums/permissions';
import {MemoryRouter, useNavigate} from 'react-router-dom';
import {RouteNames} from 'Routes/routeNames';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {beforeEach, describe, expect, it, Mock, vi} from 'vitest';
import * as userDetailPageState from './hooks/useUserDetailPageState';
import UserDetails from './UserDetail';
import * as userDetailUtil from './userDetail.util';

// Mock dependencies

vi.mock('./userDetail.util', async original => ({
  ...(await original()),
  useGetUserDetailLocationMetaData: vi.fn(),
}));
vi.mock('./hooks/useUserDetailPageState', () => ({
  default: vi.fn(),
}));

// Mock useUserManagementDialogs globally
const handleActionClick = vi.fn();
vi.mock('../hooks/useUserManagementDialogs', () => ({
  useUserManagementDialogs: () => ({
    handleActionClick,
    handleDialogClose: vi.fn(),
    handleDialogConfirm: vi.fn(),
    handleResendDialogClose: vi.fn(),
    handleResendDialogConfirm: vi.fn(),
    dialogOpen: false,
    dialogAction: null,
    dialogUser: null,
    resendDialogOpen: false,
    resendDialogUser: null,
    isPatching: false,
    isResending: false,
  }),
}));
vi.mock('react-router-dom', async original => ({
  ...(await original()),
  useNavigate: vi.fn(),
}));

const mockUser = {
  userTenantId: '1',
  roleId: '2',
  roleName: 'Admin',
  email: '<EMAIL>',
  createdOn: '2024-06-01',
  modifiedOn: '2024-06-02',
  firstName: 'John',
  lastName: 'Doe',
  status: 1, // Will be overwritten in tests
};
const mockCreatedByUser = {
  firstName: 'Jane',
  lastName: 'Smith',
};

beforeEach(() => {
  handleActionClick.mockReset();
  // Always mock useGetUserDetailLocationMetaData to return the correct user for each test
  (userDetailUtil.useGetUserDetailLocationMetaData as any).mockImplementation(() => ({
    user: mockUser,
  }));
  (userDetailPageState.default as any).mockImplementation(() => ({
    user: mockUser,
    createdByUser: mockCreatedByUser,
    isUserLoading: false,
    userError: null,
    role: {
      permissions: [
        PermissionKey.ViewPlan,
        PermissionKey.CreatePlan,
        PermissionKey.CreateTenant,
        PermissionKey.ViewTenant,
        PermissionKey.ViewRoles,
      ],
    },
  }));
});

describe('UserDetails', () => {
  const renderComponent = () => {
    renderWithTheme(
      <MemoryRouter>
        <UserDetails />
      </MemoryRouter>,
    );
  };

  it('renders user information', () => {
    renderComponent();
    expect(screen.getByText('User information')).toBeInTheDocument();
    expect(screen.getByText('Admin')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByText('Created on')).toBeInTheDocument();
    expect(screen.getByText('Modified on')).toBeInTheDocument();
  });

  it('renders breadcrumbs', () => {
    renderComponent();
    expect(screen.getByText('User Management')).toBeInTheDocument();
    expect(screen.getAllByText('John Doe').length).greaterThan(0);
  });

  it('renders created by user', () => {
    renderComponent();
    expect(screen.getByText('Created by')).toBeInTheDocument();
    expect(screen.getByText('Jane Smith')).toBeInTheDocument();
  });

  it('renders permissions section', () => {
    renderComponent();
    expect(screen.getByText('User information')).toBeInTheDocument();
    expect(screen.getByText('Tenant')).toBeInTheDocument();
    expect(screen.getByText('Plans')).toBeInTheDocument();
  });

  it('shows loader when loading', () => {
    (userDetailPageState.default as any).mockReturnValue({
      user: null,
      isUserLoading: true,
      userError: null,
      role: null,
    });
    renderComponent();
    expect(screen.getByTestId('circularProgress')).toBeInTheDocument();
  });

  it('calls navigate on edit', async () => {
    const navigate = vi.fn();
    (useNavigate as Mock).mockReturnValue(navigate);
    renderComponent();
    fireEvent.click(screen.getByTestId('edit-user-button'));
    await waitFor(() => expect(navigate).toHaveBeenCalledWith(RouteNames.EDIT_USER, {state: {user: mockUser}}));
  });

  it('calls navigate on close', async () => {
    const navigate = vi.fn();
    (useNavigate as Mock).mockReturnValue(navigate);
    renderComponent();
    fireEvent.click(screen.getByText('Close'));
    await waitFor(() => expect(navigate).toHaveBeenCalledWith(-1));
  });

  // --- New tests for menu open/close and handleDeactivateUser ---

  it('opens and closes the menu', async () => {
    renderComponent();
    // Open menu
    fireEvent.click(screen.getAllByRole('button').find(btn => btn.querySelector('svg'))!);
    // Wait for the menu to appear
    await waitFor(() => expect(screen.getByTestId('user-action-menu')).toBeInTheDocument());
    // Close menu
    // fireEvent.keyDown(screen.getByTestId('user-action-menu'), {key: 'Escape', code: 'Escape'});
    fireEvent.mouseDown(screen.getByTestId('user-action-menu'));
    await waitFor(() => {
      expect(screen.getByTestId('user-action-menu')).not.toBeVisible();
    });
  });

  it('calls handleActionClick when menu item is clicked (deactivate)', async () => {
    renderWithTheme(
      <MemoryRouter>
        <UserDetails />
      </MemoryRouter>,
    );
    // Open menu
    fireEvent.click(screen.getAllByRole('button').find(btn => btn.querySelector('svg'))!);
    await waitFor(() => expect(screen.getByTestId('user-action-menu')).toBeInTheDocument());
    // Click menu item
    fireEvent.click(screen.getByTestId('user-action-menuitem'));
    expect(handleActionClick).toHaveBeenCalled();
  });

  it('calls handleActionClick when menu item is clicked (send-link)', async () => {
    // Set user status to a value that triggers 'send-link'
    const unknownStatusUser = {...mockUser, status: 99}; // Assuming 99 triggers send-link
    (userDetailUtil.useGetUserDetailLocationMetaData as any).mockImplementation(() => ({
      user: unknownStatusUser,
    }));
    (userDetailPageState.default as any).mockImplementation(() => ({
      user: unknownStatusUser,
      createdByUser: mockCreatedByUser,
      isUserLoading: false,
      userError: null,
      role: {permissions: [PermissionKey.ViewPlan, PermissionKey.CreatePlan, PermissionKey.CreateTenant]},
    }));

    renderWithTheme(
      <MemoryRouter>
        <UserDetails />
      </MemoryRouter>,
    );
    // Open menu
    fireEvent.click(screen.getAllByRole('button').find(btn => btn.querySelector('svg'))!);
    await waitFor(() => expect(screen.getByTestId('user-action-menu')).toBeInTheDocument());
    // Click menu item
    fireEvent.click(screen.getByTestId('user-action-menuitem'));
    expect(handleActionClick).toHaveBeenCalledWith('send-link', expect.objectContaining({status: 99}));
  });

  it('calls handleActionClick when menu item is clicked (activate)', async () => {
    // Set user status to INACTIVE to trigger 'activate'
    const inactiveUser = {...mockUser, status: 2}; // Assuming 2 = INACTIVE
    (userDetailUtil.useGetUserDetailLocationMetaData as any).mockImplementation(() => ({
      user: inactiveUser,
    }));
    (userDetailPageState.default as any).mockImplementation(() => ({
      user: inactiveUser,
      createdByUser: mockCreatedByUser,
      isUserLoading: false,
      userError: null,
      role: {permissions: [PermissionKey.ViewPlan, PermissionKey.CreatePlan, PermissionKey.CreateTenant]},
    }));

    renderWithTheme(
      <MemoryRouter>
        <UserDetails />
      </MemoryRouter>,
    );
    // Open menu
    fireEvent.click(screen.getAllByRole('button').find(btn => btn.querySelector('svg'))!);
    await waitFor(() => expect(screen.getByTestId('user-action-menu')).toBeInTheDocument());
    // Click menu item
    fireEvent.click(screen.getByTestId('user-action-menuitem'));
    expect(handleActionClick).toHaveBeenCalledWith('activate', expect.objectContaining({status: 2}));
  });
});
