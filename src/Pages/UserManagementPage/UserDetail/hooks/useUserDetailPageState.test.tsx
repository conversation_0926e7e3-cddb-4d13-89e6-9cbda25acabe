// @vitest-environment jsdom
import {renderHook, waitFor} from '@testing-library/react';
import {useLazyGetUserByIdQuery} from 'redux/app/tenantManagementApiSlice';
import {TestWrapper} from 'TestHelper/TestHelper';
import {describe, expect, it, Mock, vi} from 'vitest';
import useUserDetailPageState from './useUserDetailPageState';

// Helper to mock RTK Query lazy function with unwrap
function createLazyFn(returnValue: unknown) {
  const fn = vi.fn(() => {
    const promise = Promise.resolve({data: returnValue}) as any;
    promise.unwrap = () => Promise.resolve(returnValue);
    return promise;
  });
  return fn;
}
vi.mock('redux/app/tenantManagementApiSlice', () => ({
  useGetUserByIdQuery: vi.fn(() => ({
    data: [{id: '2', name: 'Creator'}],
    isLoading: false,
    error: null,
  })),
  useLazyGetRoleByIdQuery: vi.fn(() => [createLazyFn([{id: 'r1', name: 'Admin'}]), {isLoading: false, error: null}]),
  useLazyGetUserByIdQuery: vi.fn(() => [
    createLazyFn([{id: '1', createdBy: '2', name: 'Test User'}]),
    {isLoading: false, error: null},
  ]),
}));

describe('useUserDetailPageState', () => {
  it('returns user and role data', async () => {
    const {result} = renderHook(() => useUserDetailPageState('1', 'r1'), {wrapper: TestWrapper});
    await waitFor(() => {
      expect(result.current.user).toEqual({id: '1', createdBy: '2', name: 'Test User'});
      expect(result.current.role).toEqual({id: 'r1', name: 'Admin'});
      expect(result.current.createdByUser).toEqual({id: '2', name: 'Creator'});
      expect(result.current.isUserLoading).toBe(false);
      expect(result.current.loadingRole).toBe(false);
      expect(result.current.isCreatedByLoading).toBe(false);
      expect(result.current.userError).toBeNull();
      expect(result.current.roleError).toBeNull();
      expect(result.current.createdByError).toBeNull();
    });
  });

  it('sets user to null if users array is empty', async () => {
    // Override the mock for this test
    (useLazyGetUserByIdQuery as Mock).mockImplementation(() => [createLazyFn([]), {isLoading: false, error: null}]);
    const {result} = renderHook(() => useUserDetailPageState('empty', 'r1'), {wrapper: TestWrapper});
    await waitFor(() => {
      expect(result.current.user).toBeNull();
    });
  });
});
