import {usePermissions} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import Permission<PERSON>ey from 'Constants/enums/permissions';
import {useEffect, useState} from 'react';
import {
  useGetUserByIdQuery,
  useLazyGetRoleByIdQuery,
  useLazyGetUserByIdQuery,
} from 'redux/app/tenantManagementApiSlice';
import {IRole, UserViewType} from 'redux/app/types';

/**
 * Custom hook to manage the state and data fetching logic for the User Detail Page.
 *
 * @param userId - The ID of the user whose details are to be fetched.
 * @param roleId - The ID of the role associated with the user.
 * @returns An object containing:
 * - `role`: The role details of the user, or null if not found.
 * - `user`: The user details, or null if not found.
 * - `createdByUser`: The details of the user who created the current user, or null if not found.
 * - `isUserLoading`: Boolean indicating if the user data is currently loading.
 * - `userError`: Error object if fetching the user data failed.
 * - `loadingRole`: <PERSON><PERSON>an indicating if the role data is currently loading.
 * - `roleError`: Error object if fetching the role data failed.
 * - `isCreatedByLoading`: <PERSON>olean indicating if the created-by user data is currently loading.
 * - `createdByError`: Error object if fetching the created-by user data failed.
 *
 * @remarks
 * This hook uses Redux Toolkit Query hooks to fetch user and role data asynchronously.
 * It also handles loading and error states for each data fetch.
 */
const useUserDetailPageState = (userId: string, roleId: string) => {
  const {hasPermission} = usePermissions();
  const [user, setUser] = useState<UserViewType | null>(null);

  const [getUser, {isLoading: isUserLoading, error: userError}] = useLazyGetUserByIdQuery();
  const {
    data: createdByUsers,
    isLoading: isCreatedByLoading,
    error: createdByError,
  } = useGetUserByIdQuery(user?.createdBy ?? '', {skip: !user, refetchOnMountOrArgChange: true});
  const [getUserRole, {isLoading: loadingRole, error: roleError}] = useLazyGetRoleByIdQuery();
  const [role, setRole] = useState<IRole | null>(null);

  const createdByUser = createdByUsers && createdByUsers.length > 0 ? createdByUsers[0] : null;

  useEffect(() => {
    if (userId && userId.length > 0) {
      getUser(userId)
        .unwrap()
        .then(users => {
          if (users.length === 0) {
            setUser(null);
            return;
          }
          setUser(users[0] || null);
        });
    }
  }, [userId, getUser]);

  useEffect(() => {
    if (roleId && roleId.length > 0 && hasPermission(PermissionKey.ViewRoles)) {
      getUserRole({roleID: roleId})
        .unwrap()
        .then(detail => {
          if (detail.length === 0) {
            setRole(null);
            return;
          }
          setRole(detail[0]);
        });
    }
  }, [roleId, getUserRole]);

  return {
    role,
    user,
    createdByUser,
    isUserLoading,
    userError,
    loadingRole,
    roleError,
    isCreatedByLoading,
    createdByError,
  };
};

export default useUserDetailPageState;
