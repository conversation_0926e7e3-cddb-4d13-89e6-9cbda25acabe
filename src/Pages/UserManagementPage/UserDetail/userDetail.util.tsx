import {useLocation} from 'react-router';
import {UserViewType} from 'redux/app/types';

interface UserDetailLocationMetadata {
  user: UserViewType;
}
/**
 * Get the location metadata for the User Detail page
 * @returns Location metadata
 */
export const useGetUserDetailLocationMetaData = (): UserDetailLocationMetadata => {
  const location = useLocation();
  return {
    user: location.state?.user as UserViewType,
  };
};
