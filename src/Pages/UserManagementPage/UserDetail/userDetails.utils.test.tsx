import {renderHook} from '@testing-library/react';
import {AnyObject} from 'Helpers/utils';
import * as ReactRouter from 'react-router';
import {beforeEach, describe, expect, it, vi} from 'vitest';
import {useGetUserDetailLocationMetaData} from './userDetail.util';

describe('useGetUserDetailLocationMetaData', () => {
  const mockUser = {
    id: '1',
    firstName: 'Test',
    lastName: 'User',
    email: '<EMAIL>',
    status: 1,
    roleName: 'Admin',
    createdOn: '',
    modifiedOn: '',
  } as AnyObject;

  beforeEach(() => {
    vi.restoreAllMocks();
  });

  it('returns user from location state', () => {
    vi.spyOn(ReactRouter, 'useLocation').mockReturnValue({
      state: {user: mockUser},
    } as any);

    const {result} = renderHook(() => useGetUserDetailLocationMetaData());
    expect(result.current.user).toEqual(mockUser);
  });

  it('returns undefined user if not in location state', () => {
    vi.spyOn(ReactRouter, 'useLocation').mockReturnValue({
      state: {},
    } as any);

    const {result} = renderHook(() => useGetUserDetailLocationMetaData());
    expect(result.current.user).toBeUndefined();
  });

  it('returns undefined user if location state is undefined', () => {
    vi.spyOn(ReactRouter, 'useLocation').mockReturnValue({
      state: undefined,
    } as any);

    const {result} = renderHook(() => useGetUserDetailLocationMetaData());
    expect(result.current.user).toBeUndefined();
  });
});
