import {fireEvent, render, screen} from '@testing-library/react';
import {vi} from 'vitest';
import UserFilters from './UserFilters';

// Mock RoleFilter and FilterStatusChips
vi.mock('Pages/RolesResponsibilitiesPage/RoleFilter', () => ({
  __esModule: true,
  default: ({filterView, ...props}: any) =>
    props.open ? (
      <div data-testid="role-filter">
        {filterView && filterView(props.clearRef, props.onFilterChange, props.initialFilter)}
        <span data-testid="role-filter-props">{JSON.stringify(props)}</span>
      </div>
    ) : null,
}));
vi.mock('./StatusFilterChip/StatusFilterChip', () => ({
  __esModule: true,
  default: vi.fn(({ref, onSelect, value}: any) => (
    <div data-testid="status-filter-chip">
      <span data-testid="status-chip-value">{JSON.stringify(Array.from(value || []))}</span>
      <button data-testid="status-chip-select" onClick={() => onSelect && onSelect(new Set(['active']))}>
        Select
      </button>
      <button
        data-testid="status-chip-clear"
        onClick={() => ref?.current?.clearSelection && ref.current.clearSelection()}
      >
        Clear
      </button>
    </div>
  )),
}));

describe('UserFilters', () => {
  it('renders RoleFilter and passes props', () => {
    render(<UserFilters open={true} onClose={() => {}} onFilterChange={() => {}} />);
    expect(screen.getByTestId('role-filter')).toBeInTheDocument();
    expect(screen.getByTestId('role-filter-props').textContent).toContain('"open":true');
  });

  it('renders StatusFilterChip inside RoleFilter filterView', () => {
    render(<UserFilters open={true} onClose={() => {}} onFilterChange={() => {}} />);
    expect(screen.getByTestId('status-filter-chip')).toBeInTheDocument();
  });

  it('handles selection via onFilterChange callback', () => {
    const onFilterChange = vi.fn();
    render(<UserFilters open={true} onClose={() => {}} onFilterChange={onFilterChange} />);
    fireEvent.click(screen.getByTestId('status-chip-select'));
    expect(onFilterChange).toHaveBeenCalledWith(new Set(['active']));
  });

  it('does not render filters when open is false', () => {
    render(<UserFilters open={false} onClose={() => {}} onFilterChange={() => {}} />);
    expect(screen.queryByTestId('role-filter')).not.toBeInTheDocument();
    expect(screen.queryByTestId('status-filter-chip')).not.toBeInTheDocument();
  });

  it('handles clear button without ref gracefully', () => {
    render(<UserFilters open={true} onClose={() => {}} onFilterChange={() => {}} />);
    fireEvent.click(screen.getByTestId('status-chip-clear'));
    // No error should occur
  });

  it('calls onClose when close is triggered', () => {
    const onClose = vi.fn();
    render(<UserFilters open={true} onClose={onClose} onFilterChange={() => {}} />);
    // Simulate closing, e.g., by calling onClose directly
    onClose();
    expect(onClose).toHaveBeenCalled();
  });

  it('handles missing props gracefully', () => {
    render(<UserFilters open={true} onClose={() => {}} onFilterChange={() => {}} />);
    expect(screen.getByTestId('status-filter-chip')).toBeInTheDocument();
  });
});
