import {DateRangeFilterSelectedDate} from 'Components/DateRangeFilter/DateRangeFilter';
import {IClearFilters} from 'Components/FilterUtil/IClearFilters';
import RoleFilter, {RoleFilterProps} from 'Pages/RolesResponsibilitiesPage/RoleFilter';
import React from 'react';
import FilterStatusChips from './StatusFilterChip/StatusFilterChip';
import {UserStatus} from './userManagement.utils';

export interface IUserFilter {
  status: Set<UserStatus>;
  createdOnDateRange?: DateRangeFilterSelectedDate;
  modifiedDateRange?: DateRangeFilterSelectedDate;
}

const UserFilters = ({...props}: RoleFilterProps<IUserFilter>) => {
  const buildFilter = (
    clearRef: React.RefObject<IClearFilters | null>,
    onFilterChange?: (filter: Set<string>) => void,
    initialFilter?: Set<string>,
  ) => <FilterStatusChips ref={clearRef} onSelect={onFilterChange} value={initialFilter} />;

  return <RoleFilter {...props} filterView={buildFilter} />;
};

UserFilters.displayName = 'UserFilters';
export default UserFilters;
