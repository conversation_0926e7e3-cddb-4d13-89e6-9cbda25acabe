import AddIcon from '@mui/icons-material/Add';
import {Box, Tooltip, Typography} from '@mui/material';
import imgEmptyUser from 'Assets/empty-users.png';
import BlueButton from 'Components/BlueButton/BlueButton';
import {useNavigate} from 'react-router';
import {RouteNames} from 'Routes/routeNames';
import {toolTipStyles} from 'styles/pages/TenantPage.styles';
const buttonHeight = '2.375rem';
/**
 * UserListEmpty component
 * Displays a message and a button when there are no users in the list.
 * @returns {JSX.Element}
 */
const UserListEmpty = () => {
  const navigate = useNavigate();
  return (
    <Box
      display="flex"
      flexDirection="column"
      alignItems="center"
      justifyContent="center"
      sx={{
        width: '100%',
        height: '80vh',
        position: 'relative',
        textAlign: 'center',
        gap: 0,
      }}
    >
      <Box component="img" src={imgEmptyUser} alt="No users found" sx={{width: '10.68rem', height: '10.68rem'}} />
      <Typography
        sx={{
          fontSize: '1.37rem',
          fontWeight: 700,
          color: 'body.dark',
          mt: 2,
        }}
      >
        No users yet!
      </Typography>
      <Typography
        sx={{
          fontSize: '1rem',
          fontWeight: 400,
          color: 'body.500',
          mt: 1.75,
        }}
      >
        Add users and assign them roles to manage
        <br /> access and permissions.
      </Typography>
      <Tooltip title="Add User" slotProps={toolTipStyles}>
        <BlueButton
          sx={{height: buttonHeight, fontSize: '1rem', fontWeight: 600, px: 3.5, py: 1.75, mt: 3}}
          onClick={() => {
            navigate(RouteNames.ADD_USER);
          }}
        >
          <Box sx={{flexDirection: 'row', display: 'flex', alignItems: 'center', gap: 1}}>
            <AddIcon sx={{height: '1rem', width: '1rem'}} />
            Add User
          </Box>
        </BlueButton>
      </Tooltip>
    </Box>
  );
};

export default UserListEmpty;
