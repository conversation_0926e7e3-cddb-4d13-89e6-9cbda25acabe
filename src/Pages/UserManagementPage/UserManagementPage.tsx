import AddIcon from '@mui/icons-material/Add';
import {Box, Tooltip, Typography} from '@mui/material';
import {ColumnDef} from '@tanstack/react-table';
import SearchIcon from 'Assets/search-icon.svg';
import BlueButton from 'Components/BlueButton/BlueButton';
import CenterLoaderContainer from 'Components/CenterLoaderContainer';
import FilterWithCountButton from 'Components/FilterWithCountButton';
import PermissionWrapper from 'Components/PermissionWrapper';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import PermissionKey from 'Constants/enums/permissions';
import {isNil} from 'lodash';
import React, {useState} from 'react';
import {useNavigate} from 'react-router';
import {UserViewType} from 'redux/app/types';
import {RouteNames} from 'Routes/routeNames';
import {
  bodyCellProps,
  coloumnCellProps,
  tableContainerProps,
  tableHeadProps,
  toolTipStyles,
} from 'styles/pages/TenantPage.styles';
import {DebouncedInput, Table} from '../../Components/Table';
import {useUserManagementApi} from './hooks/useUserManagementApi';
import {HandleActionType, useUserManagementDialogs} from './hooks/useUserManagementDialogs';
import UserActivateDeactivateDialog from './UserActivateDeactivateDialog/UserActivateDeactivateDialog';
import UserFilters, {IUserFilter} from './UserFilters';
import UserListEmpty from './UserListEmpty';
import {userColumns, UserTableRow} from './userManagement.utils';
import UserResendActivationLinkDialog from './UserResendActivationLinkDialog/UserResendActivationLinkDialog';
export const headerBoxStyle = {
  pb: 1,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
};

export const leftHeaderStyle = {
  fontWeight: 700,
  color: 'body.dark',
};

/**
 * UserManagementPage component displays a paginated, sortable table of users.
 * Handles data fetching, error notifications, and empty state rendering.
 *
 * @component
 * @returns {JSX.Element} The rendered user management page.
 */
const UserManagementPage = () => {
  const navigate = useNavigate();
  const [userFilter, setUserFilter] = useState<IUserFilter>();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<string>('createdOn DESC');
  const [openFilter, setOpenFilter] = React.useState(false);
  const filterButtonRef = React.useRef<HTMLElement>(null);

  const buildAddButton = () => {
    return (
      <PermissionWrapper permission={PermissionKey.CreateTenantUser}>
        <Tooltip title="Add User" slotProps={toolTipStyles}>
          <BlueButton
            sx={{height: '2.6rem'}}
            onClick={() => {
              navigate(RouteNames.ADD_USER, {});
            }}
          >
            <AddIcon sx={{height: '1rem', width: '1rem'}} /> Add User
          </BlueButton>
        </Tooltip>
      </PermissionWrapper>
    );
  };

  const userFilterSize = React.useMemo(() => {
    let size = 0;
    size += userFilter?.status?.size ?? 0;
    size += !isNil(userFilter?.createdOnDateRange) ? 1 : 0;
    size += !isNil(userFilter?.modifiedDateRange) ? 1 : 0;
    return size;
  }, [userFilter]);

  const buildFilterButton = () => {
    return (
      <FilterWithCountButton
        ref={filterButtonRef}
        filterSize={userFilterSize}
        onClick={() => {
          setOpenFilter(prev => !prev);
        }}
      />
    );
  };

  const handleSortChange = (columnId: string, sort: boolean) => {
    const sortParam = `${columnId} ${sort ? 'DESC' : 'ASC'}`;
    setSortBy(sortParam);
  };

  const buildTopRightSection = () => {
    return (
      <Box sx={{display: 'flex', gap: 1, flexDirection: 'row', alignItems: 'center', ml: 'auto'}}>
        {/* Search Input */}
        <DebouncedInput
          placeholder="Search Users"
          data-testid="search-users"
          sx={{
            fontSize: '0.675rem',
            pl: 0,
          }}
          debounceTime={500}
          leftAdornment={<SVGImageFromPath path={SearchIcon} sx={{width: '1rem', height: '1rem', mr: 1}} />}
          inputSx={{
            fontSize: '1rem',
            fontWeight: 400,
            color: 'black.main',
          }}
          value={searchTerm}
          onChange={value => {
            setSearchTerm('' + value);
          }}
        />
        {/* // Filter Button */}
        {buildFilterButton()}
        {buildAddButton()}
      </Box>
    );
  };

  const buildTable = () => {
    return (
      <CenterLoaderContainer isLoading={isFetching}>
        <Table
          data={usersList as UserTableRow[]}
          columns={
            userColumns.map(col =>
              col.header === 'Actions'
                ? {
                    ...col,
                    meta: {
                      onAction: (action: HandleActionType, row: UserTableRow) => {
                        handleActionClick(action, row as UserViewType);
                      },
                    },
                  }
                : col,
            ) as ColumnDef<UserTableRow>[]
          }
          enableSorting={true}
          initialSortingState={[{id: 'createdOn', desc: true}]}
          tablePropsObject={{
            tableHeadProps: {sx: tableHeadProps},
            columnCellProps: {sx: coloumnCellProps},
            tableContainerProps: {sx: tableContainerProps},
            bodyCellProps: {sx: bodyCellProps},
            tableProps: {sx: {tableLayout: 'fixed', minWidth: '62.5rem'}},
          }}
          limit={limit}
          setLimit={setLimit}
          offset={offset}
          setOffset={setOffset}
          count={usersCount?.count ?? 0}
          manualPagination={true}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleRowsPerPageChange}
          data-testid="tenant-user-table"
          onSortChange={handleSortChange}
        />
      </CenterLoaderContainer>
    );
  };

  // API/data logic
  const {
    limit,
    setLimit,
    offset,
    setOffset,
    usersList,
    usersCount,
    isFetching,
    refetchUsers,
    handlePageChange,
    handleRowsPerPageChange,
  } = useUserManagementApi({
    order: sortBy,
    userTenantId: '',
    searchValue: searchTerm,
    createdOnDateRange: userFilter?.createdOnDateRange,
    modifiedDateRange: userFilter?.modifiedDateRange,
    status: userFilter?.status ? Array.from(userFilter.status).map(Number) : undefined,
  });

  // Dialog/action logic
  const {
    dialogOpen,
    dialogAction,
    dialogUser,
    handleDialogClose,
    handleDialogConfirm,
    resendDialogOpen,
    resendDialogUser,
    handleResendDialogClose,
    handleResendDialogConfirm,
    handleActionClick,
    isPatching,
    isResending,
  } = useUserManagementDialogs({
    refetchUsers: () => {
      refetchUsers();
    },
  });

  const canShowEmptyList = () => !isFetching && usersCount?.count === 0 && userFilterSize === 0 && !searchTerm;

  return (
    <Box>
      {/* Header section */}
      <Box sx={{...headerBoxStyle, minHeight: 44}}>
        <Typography variant="h6" sx={leftHeaderStyle}>
          User Management
        </Typography>
        {buildTopRightSection()}
      </Box>
      {/* Table or empty state */}
      <Box sx={{position: 'relative', minHeight: '12.5rem'}}>
        {canShowEmptyList() ? (
          <UserListEmpty />
        ) : (
          <>
            {buildTable()}
            <UserActivateDeactivateDialog
              open={dialogOpen}
              onClose={handleDialogClose}
              onConfirm={handleDialogConfirm}
              actionType={dialogAction ?? 'Activate'}
              userName={dialogUser?.firstName ?? ''}
              email={dialogUser?.email ?? ''}
              roleName={dialogUser?.roleName ?? ''}
              isLoading={isPatching}
            />
            {/* Resend Activation Link Confirmation Dialog */}
            <UserResendActivationLinkDialog
              open={resendDialogOpen}
              onClose={handleResendDialogClose}
              onConfirm={handleResendDialogConfirm}
              userName={resendDialogUser?.firstName ?? ''}
              email={resendDialogUser?.email ?? ''}
              isLoading={isResending}
            />
            <UserFilters
              open={openFilter}
              value={userFilter}
              onClose={() => setOpenFilter(false)}
              anchorEl={filterButtonRef.current}
              onFilterChange={setUserFilter}
            />
          </>
        )}
      </Box>
    </Box>
  );
};

export default UserManagementPage;
