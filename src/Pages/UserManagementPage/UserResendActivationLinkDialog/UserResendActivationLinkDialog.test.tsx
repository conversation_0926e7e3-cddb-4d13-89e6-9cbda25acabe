import {ThemeProvider, createTheme} from '@mui/material/styles';
import {fireEvent, render, screen} from '@testing-library/react';
import {describe, expect, it, vi} from 'vitest';
import UserResendActivationLinkDialog from './UserResendActivationLinkDialog';

const baseProps = {
  open: true,
  onClose: vi.fn(),
  onConfirm: vi.fn(),
  userName: 'Test User',
  email: '<EMAIL>',
  isLoading: false,
};

const renderWithTheme = (ui: React.ReactElement) => {
  const theme = createTheme({
    palette: {
      alert: {
        warning: {bg: '#fffbe6'},
        success: {bg: '#d0f5e8'},
      },
      white: {200: '#fff'},
      body: {dark: '#222', 100: '#eee', 900: '#111', 500: '#888'},
      secondary: {50: '#f0f0f0', 700: '#333', main: '#1976d2'},
      other: {white: '#fff'},
    },
  } as any);
  return render(<ThemeProvider theme={theme}>{ui}</ThemeProvider>);
};

describe('UserResendActivationLinkDialog', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders dialog with correct content', () => {
    renderWithTheme(<UserResendActivationLinkDialog {...baseProps} />);
    expect(screen.getByText('Resend activation link')).toBeInTheDocument();
    expect(screen.getByText('Are you sure you want to resend the activation link to Test User?')).toBeInTheDocument();
    expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Resend link/i})).toBeInTheDocument();
    expect(screen.getByRole('button', {name: /Cancel/i})).toBeInTheDocument();
    expect(screen.getByTestId('dialog-resend-button')).toBeInTheDocument();
  });

  it('calls onClose when Cancel is clicked', () => {
    renderWithTheme(<UserResendActivationLinkDialog {...baseProps} />);
    fireEvent.click(screen.getByRole('button', {name: /Cancel/i}));
    expect(baseProps.onClose).toHaveBeenCalled();
  });

  it('calls onConfirm when Resend link is clicked', () => {
    renderWithTheme(<UserResendActivationLinkDialog {...baseProps} />);
    fireEvent.click(screen.getByRole('button', {name: /Resend link/i}));
    expect(baseProps.onConfirm).toHaveBeenCalled();
  });

  it('disables buttons when isLoading is true', () => {
    renderWithTheme(<UserResendActivationLinkDialog {...baseProps} isLoading />);
    expect(screen.getByRole('button', {name: /Resend link/i})).toBeDisabled();
    expect(screen.getByRole('button', {name: /Cancel/i})).toBeDisabled();
  });
});
