import {Box, Button, CircularProgress, styled, Typography} from '@mui/material';
import LinkIcon from 'Assets/LinkIcon';
import {DefaultDialog} from 'Components/DefaultDialog/DefaultDialog';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import {Integers} from 'Helpers/integers';

/**
 * Props for the user resend activation link confirmation dialog.
 */
interface UserResendActivationLinkDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  userName: string;
  email?: string;
  isLoading: boolean;
}

const IconWrapper = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
}));

export const KeyIconContainer = styled(Box)(({theme}) => {
  return {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    width: '5rem',
    height: '5rem',
    borderRadius: '50%',
    border: `1px solid ${theme.palette.white[Integers.TwoHundred]}`,
    padding: '1rem',
    backgroundColor: theme.palette.alert.warning.bg,
    fill: 'transparent',
    color: theme.palette.alert.success.bg,
  };
});

/**
 * User resend activation link confirmation dialog.
 * @param param0 - Props for the dialog.
 * @returns The rendered dialog component.
 */
export default function UserResendActivationLinkDialog({
  open,
  onClose,
  onConfirm,
  userName,
  email,
  isLoading,
}: Readonly<UserResendActivationLinkDialogProps>) {
  return (
    <DefaultDialog
      title={'Resend activation link'}
      maxWidth={400}
      open={open}
      onClose={isLoading ? undefined : onClose}
    >
      <Box sx={{display: 'flex', flexDirection: 'column', alignItems: 'center'}}>
        <IconWrapper>
          <KeyIconContainer>
            <LinkIcon sx={{fill: 'transparent'}} />
          </KeyIconContainer>
        </IconWrapper>
        <Typography sx={{mt: 2, fontSize: '1.125rem', fontWeight: 700, color: 'body.dark', textAlign: 'center'}}>
          Are you sure you want to resend the activation link to {userName}?
        </Typography>

        <EllipsisText
          text={email ?? ''}
          sx={{
            fontSize: '0.875rem',
            color: 'body.500',
            fontWeight: 500,
            textAlign: 'center',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 1,
            WebkitBoxOrient: 'vertical',
            wordBreak: 'break-word',
          }}
        />

        <Box
          sx={{
            mt: 4,
            display: 'flex',
            gap: 1,
            flexDirection: 'row',
            width: '100%',
            fontSize: '1rem',
            fontWeight: 600,
          }}
        >
          <Button onClick={onClose} variant="outlined" color="inherit" disabled={isLoading} sx={{flex: 1}}>
            Cancel
          </Button>
          <Button
            onClick={onConfirm}
            variant="contained"
            color={'primary'}
            disabled={isLoading}
            sx={{
              borderColor: 'body.100',
              borderRadius: '0.375rem',
              color: isLoading ? 'primary' : 'other.white',
              fontWeight: 'inherit',
              fontSize: 'inherit',
              backgroundColor: 'secondary.main',
              flex: 1,
            }}
            startIcon={isLoading ? <CircularProgress size={18} color="inherit" /> : null}
            data-testid={'dialog-resend-button'}
          >
            {'Resend link'}
          </Button>
        </Box>
      </Box>
    </DefaultDialog>
  );
}
