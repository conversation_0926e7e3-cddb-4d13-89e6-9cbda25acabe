import {fireEvent, screen, waitFor} from '@testing-library/react';
import {PermissionProvider} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import {useNavigate} from 'react-router';
import {RouteNames} from 'Routes/routeNames';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {describe, expect, it, Mock, vi} from 'vitest';
import {UserStatus, UserTableRow} from '../userManagement.utils';
import ActionButtons from './ActionButton';

// Mocks
const mockUseNavigate = vi.fn();
vi.mock('react-router', () => ({
  useNavigate: vi.fn(),
}));
vi.mock('Assets/ActivateIcon', () => ({
  default: (props: any) => <div data-testid="activate-icon" {...props} />,
}));
vi.mock('Assets/DeactivateIcon', () => ({
  default: (props: any) => <div data-testid="deactivate-icon" {...props} />,
}));
vi.mock('Assets/EditIcon', () => ({
  default: (props: any) => {
    const {sx, ...otherProps} = props;
    return <button {...otherProps} style={sx} />;
  },
}));
/* Removed EyeIcon mock since 'eye-icon' is not rendered in the DOM */
vi.mock('Assets/link.svg', () => ({
  default: 'link.svg',
}));
vi.mock('Components/SVGImageFromPath', () => ({
  default: (props: any) => <div data-testid="svg-image" {...props} />,
}));
vi.mock('styles/pages/TenantPage.styles', () => ({
  actionStyles: {},
  toolTipStyles: {},
}));
vi.mock('Components/PermissionWrapper', () => ({
  default: (props: any) => (
    <div data-testid="permission-wrapper" {...props}>
      {props.children}{' '}
    </div>
  ),
}));

const baseRow: UserTableRow = {
  id: '1',
  firstName: 'Test',
  email: '<EMAIL>',
  roleName: 'Admin',
  status: UserStatus.INACTIVE,
  createdOn: '',
  modifiedOn: '',
};

const TEST_USER_ROW: UserTableRow = {
  id: '2',
  firstName: 'Test2',
  email: '<EMAIL>',
  roleName: 'User',
  status: UserStatus.INACTIVE,
  createdOn: '',
  modifiedOn: '',
};

const allStatuses = Object.values(UserStatus).filter(v => typeof v === 'number') as UserStatus[];

describe('ActionButtons', () => {
  const renderComponent = (component: React.ReactNode) => {
    return renderWithTheme(<PermissionProvider>{component}</PermissionProvider>);
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (useNavigate as Mock).mockReturnValue(mockUseNavigate);
  });

  it('renders correct icons for all UserStatus values', () => {
    allStatuses.forEach(status => {
      renderComponent(<ActionButtons tableRow={{...baseRow, status}} />);
      expect(screen.getByTestId('action-button')).toBeInTheDocument();
      expect(screen.getByTestId('view-button')).toBeInTheDocument();
      expect(screen.getByTestId('edit-button')).toBeInTheDocument();

      if (status === UserStatus.INACTIVE) {
        expect(screen.getByTestId('activate-button')).toBeInTheDocument();
      } else if (status === UserStatus.ACTIVE) {
        expect(screen.getByTestId('deactivate-button')).toBeInTheDocument();
      } else if (status === UserStatus.PENDINGPROVISION) {
        expect(screen.getByTestId('link-button')).toBeInTheDocument();
      }
      document.body.innerHTML = '';
    });
  });

  it('should navigate to view detail page', async () => {
    renderComponent(<ActionButtons tableRow={baseRow} />);
    fireEvent.click(screen.getByTestId('view-button'));
    waitFor(() => {
      expect(mockUseNavigate).toHaveBeenCalledWith(RouteNames.VIEW_USER, {
        state: {
          user: baseRow,
        },
      });
    });
  });

  it('calls onAction("deactivate", tableRow) when deactivate button is clicked', () => {
    const onAction = vi.fn();
    const {getByTestId} = renderComponent(
      <ActionButtons tableRow={{...TEST_USER_ROW, status: UserStatus.ACTIVE}} onAction={onAction} />,
    );
    fireEvent.click(getByTestId('deactivate-button'));
    expect(onAction).toHaveBeenCalledWith('deactivate', {...TEST_USER_ROW, status: UserStatus.ACTIVE});
  });

  it('calls onAction("activate", tableRow) when activate button is clicked', () => {
    const onAction = vi.fn();
    const {getByTestId} = renderComponent(<ActionButtons tableRow={TEST_USER_ROW} onAction={onAction} />);
    fireEvent.click(getByTestId('activate-button'));
    expect(onAction).toHaveBeenCalledWith('activate', TEST_USER_ROW);
  });

  it('calls onAction("send-link", tableRow) when link button is clicked', () => {
    const onAction = vi.fn();
    const {getByTestId} = renderComponent(
      <ActionButtons tableRow={{...TEST_USER_ROW, status: UserStatus.PENDINGPROVISION}} onAction={onAction} />,
    );
    fireEvent.click(getByTestId('link-button'));
    expect(onAction).toHaveBeenCalledWith('send-link', {...TEST_USER_ROW, status: UserStatus.PENDINGPROVISION});
  });
});
