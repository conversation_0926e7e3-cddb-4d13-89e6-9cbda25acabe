import {Box, Stack, Tooltip} from '@mui/material';
import ActivateIcon from 'Assets/ActivateIcon';
import DeactivateIcon from 'Assets/DeactivateIcon';
import EditIcon from 'Assets/EditIcon';
import EyeIcon from 'Assets/EyeIcon';
import LinkImage from 'Assets/link.svg';
import PermissionWrapper from 'Components/PermissionWrapper';
import SVGImageFromPath from 'Components/SVGImageFromPath';
import PermissionKey from 'Constants/enums/permissions';
import {useNavigate} from 'react-router';
import {UserViewType} from 'redux/app/types';
import {RouteNames} from 'Routes/routeNames';
import {actionStyles, toolTipStyles} from 'styles/pages/TenantPage.styles';
import {HandleActionType} from '../hooks/useUserManagementDialogs';
import {UserStatus, UserTableRow} from '../userManagement.utils';

/**
 * Action buttons for user management table
 * @param param0 - The props for the component
 * @returns The action buttons for the user management table
 *
 */
const ActionButtons = ({
  tableRow,
  onAction,
}: {
  tableRow: UserTableRow;
  onAction?: (action: HandleActionType, row: UserTableRow) => void;
}) => {
  const navigate = useNavigate();
  const userStatus = tableRow.status;
  const user = tableRow as UserViewType;
  const navigateToEditUser = () => {
    navigate(RouteNames.EDIT_USER, {
      state: {
        user: user,
      },
    });
  };

  const viewUserDetails = () => {
    navigate(RouteNames.VIEW_USER, {
      state: {
        user: user,
      },
    });
  };

  const isUserInactive = userStatus === UserStatus.INACTIVE;

  const buildEditButton = () => {
    return (
      <PermissionWrapper permission={PermissionKey.UpdateTenantUser}>
        <Tooltip
          title={isUserInactive ? 'Edit disabled for inactive users' : 'Edit user'}
          placement="top"
          arrow
          slotProps={toolTipStyles}
        >
          <EditIcon
            sx={{
              ...actionStyles,
              color: isUserInactive ? 'body.200' : 'body.500',
              cursor: isUserInactive ? 'not-allowed' : 'pointer',
            }}
            data-testid="edit-button"
            onClick={isUserInactive ? undefined : () => navigateToEditUser()}
          />
        </Tooltip>
      </PermissionWrapper>
    );
  };

  return (
    <Stack display="flex" flexDirection={'row'} data-testid="action-button">
      <Tooltip title="View details" placement="top" arrow slotProps={toolTipStyles}>
        <EyeIcon sx={actionStyles} data-testid="view-button" onClick={() => viewUserDetails()} />
      </Tooltip>
      {buildEditButton()}
      <PermissionWrapper permission={PermissionKey.UpdateTenantUser}>
        {isUserInactive && (
          <Tooltip title="Activate user" placement="top" arrow slotProps={toolTipStyles}>
            <Box
              component="span"
              sx={{cursor: onAction ? 'pointer' : 'default'}}
              data-testid="activate-button"
              onClick={() => onAction?.('activate', tableRow)}
            >
              <ActivateIcon sx={actionStyles} />
            </Box>
          </Tooltip>
        )}
        {userStatus === UserStatus.ACTIVE && (
          <Tooltip title="Deactivate user" placement="top" arrow slotProps={toolTipStyles}>
            <Box
              component="span"
              sx={{cursor: onAction ? 'pointer' : 'default'}}
              data-testid="deactivate-button"
              onClick={() => onAction?.('deactivate', tableRow)}
            >
              <DeactivateIcon sx={actionStyles} />
            </Box>
          </Tooltip>
        )}
        {userStatus !== UserStatus.ACTIVE && userStatus !== UserStatus.INACTIVE && (
          <Tooltip title="Send email link" placement="top" arrow slotProps={toolTipStyles}>
            <Box
              sx={{cursor: onAction ? 'pointer' : 'default'}}
              onClick={() => onAction?.('send-link', tableRow)}
              data-testid="link-button"
            >
              <SVGImageFromPath path={LinkImage} sx={actionStyles} />
            </Box>
          </Tooltip>
        )}
      </PermissionWrapper>
    </Stack>
  );
};

export default ActionButtons;
