import {act, renderHook} from '@testing-library/react';
import {SnackbarProvider} from 'notistack';
import {useUserManagementApi} from './useUserManagementApi';

// Mock dependencies
const enqueueSnackbarMock = vi.fn();
vi.mock('notistack', async () => {
  const actual = await vi.importActual<typeof import('notistack')>('notistack');
  return {
    ...actual,
    useSnackbar: () => ({enqueueSnackbar: enqueueSnackbarMock}),
    SnackbarProvider: actual.SnackbarProvider,
  };
});
const refetchMock = vi.fn();

let usersCountMock: any = {data: {count: 5}, error: undefined};
vi.mock('redux/auth/authApiSlice', () => ({
  useGetUserListsQuery: vi.fn((params, opts) => ({
    data: params.searchValue ? [{id: 1}] : [],
    error: params.status && params.status[0] === 999 ? {message: 'error'} : undefined,
    isLoading: params.status && params.status[0] === 888,
    refetch: refetchMock,
  })),
  useGetUserQuery: vi.fn(() => ({data: {userTenantId: 'tenant1'}})),
  useGetUsersCountQuery: vi.fn(() => usersCountMock),
}));
vi.mock('../userManagement.utils', () => ({
  DEFAULT_LIMIT: 10,
}));

const defaultParams = {
  userTenantId: 'tenant1',
  limit: 10,
  offset: 0,
  order: 'asc',
  searchValue: '',
  createdOnDateRange: undefined,
  modifiedDateRange: undefined,
  status: [],
};

describe('useUserManagementApi', () => {
  const wrapper = ({children}: any) => <SnackbarProvider>{children}</SnackbarProvider>;

  beforeEach(() => {
    enqueueSnackbarMock.mockClear();
    refetchMock.mockClear();
    usersCountMock = {data: {count: 5}, error: undefined};
  });

  it('should return default values and functions', () => {
    const {result} = renderHook(() => useUserManagementApi(defaultParams), {wrapper});
    expect(result.current.limit).toBe(10);
    expect(typeof result.current.setLimit).toBe('function');
    expect(result.current.offset).toBe(0);
    expect(typeof result.current.setOffset).toBe('function');
    expect(Array.isArray(result.current.usersList)).toBe(true);
    expect(result.current.usersCount).toEqual({count: 5});
    expect(result.current.isLoading).toBe(false);
    expect(typeof result.current.refetchUsers).toBe('function');
    expect(typeof result.current.handlePageChange).toBe('function');
    expect(typeof result.current.handleRowsPerPageChange).toBe('function');
  });

  it('should update offset on handlePageChange', () => {
    const {result} = renderHook(() => useUserManagementApi(defaultParams), {wrapper});
    act(() => {
      result.current.handlePageChange(2);
    });
    expect(result.current.offset).toBe(10);
  });

  it('should update limit and reset offset on handleRowsPerPageChange', () => {
    const {result} = renderHook(() => useUserManagementApi(defaultParams), {wrapper});
    act(() => {
      result.current.handleRowsPerPageChange(20);
    });
    expect(result.current.limit).toBe(20);
    expect(result.current.offset).toBe(0);
  });

  it('should call enqueueSnackbar on usersError', () => {
    const params = {...defaultParams, status: [999]};
    renderHook(() => useUserManagementApi(params), {wrapper});
    expect(enqueueSnackbarMock).toHaveBeenCalledWith('Failed to fetch user data', {variant: 'error'});
  });

  it('should call enqueueSnackbar on countError', () => {
    usersCountMock = {data: undefined, error: {message: 'error'}};
    const params = {...defaultParams};
    renderHook(() => useUserManagementApi(params), {wrapper});
    expect(enqueueSnackbarMock).toHaveBeenCalledWith('Failed to fetch user count', {variant: 'error'});
  });

  it('should handle loading state', () => {
    const params = {...defaultParams, status: [888]};
    const {result} = renderHook(() => useUserManagementApi(params), {wrapper});
    expect(result.current.isLoading).toBe(true);
  });

  it('should handle custom filter params', () => {
    const params = {...defaultParams, searchValue: 'test', status: [1], order: 'desc'};
    const {result} = renderHook(() => useUserManagementApi(params), {wrapper});
    expect(result.current.usersList).toEqual([{id: 1}]);
  });

  it('should call refetchUsers', () => {
    const {result} = renderHook(() => useUserManagementApi(defaultParams), {wrapper});
    act(() => {
      result.current.refetchUsers();
    });
    expect(refetchMock).toHaveBeenCalled();
  });

  it('should handle missing userTenantId', () => {
    usersCountMock = {data: undefined, error: undefined};
    const params = {...defaultParams, userTenantId: ''};
    const {result} = renderHook(() => useUserManagementApi(params), {wrapper});
    expect(result.current.usersCount).toBeUndefined();
  });
});
