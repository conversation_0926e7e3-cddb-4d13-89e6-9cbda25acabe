import {useSnackbar} from 'notistack';
import {useEffect, useMemo, useState} from 'react';
import {IUserFilterModel} from 'redux/app/types';
import {useGetUserListsQuery, useGetUserQuery, useGetUsersCountQuery} from '../../../redux/auth/authApiSlice';
import {DEFAULT_LIMIT} from '../userManagement.utils';

export function useUserManagementApi({
  searchValue,
  createdOnDateRange,
  modifiedDateRange,
  status,
  order,
}: IUserFilterModel) {
  const [limit, setLimit] = useState<number>(DEFAULT_LIMIT);
  const [offset, setOffset] = useState<number>(0);

  const {enqueueSnackbar} = useSnackbar();
  const {data: userData} = useGetUserQuery();

  const params: IUserFilterModel = {
    userTenantId: userData?.userTenantId ?? '',
    limit,
    offset,
    order,
    searchValue: searchValue,
    createdOnDateRange: createdOnDateRange,
    modifiedDateRange: modifiedDateRange,
    status: status,
  };

  const {data: usersCount, error: countError} = useGetUsersCountQuery(params, {
    refetchOnMountOrArgChange: true,
  });

  useMemo(() => {
    setOffset(0);
  }, [
    searchValue,
    JSON.stringify(createdOnDateRange), // just to make sure it's a deep comparison
    JSON.stringify(modifiedDateRange),
    JSON.stringify(status),
    order,
  ]);

  const {
    data: usersList = [],
    error: usersError,
    isLoading,
    isFetching,
    refetch: refetchUsers,
  } = useGetUserListsQuery(params, {
    refetchOnMountOrArgChange: true,
  });

  useEffect(() => {
    setOffset(0);
  }, [limit]);

  useEffect(() => {
    if (usersError && !isLoading) {
      enqueueSnackbar('Failed to fetch user data', {variant: 'error'});
    }
  }, [usersError, isLoading, enqueueSnackbar]);

  useEffect(() => {
    if (countError && !isLoading) {
      enqueueSnackbar('Failed to fetch user count', {variant: 'error'});
    }
  }, [countError, isLoading, enqueueSnackbar]);

  const handlePageChange = (page: number) => {
    const newOffset = (page - 1) * limit;
    setOffset(newOffset);
  };

  const handleRowsPerPageChange = (newLimit: number) => {
    setLimit(newLimit);
    setOffset(0);
  };

  return {
    limit,
    setLimit,
    offset,
    setOffset,
    usersList,
    usersCount,
    isLoading,
    isFetching,
    refetchUsers,
    handlePageChange,
    handleRowsPerPageChange,
  };
}
