import {act, renderHook} from '@testing-library/react';
import {SnackbarProvider} from 'notistack';
import React from 'react';
import {UserViewType} from 'redux/app/types';
import {useUserManagementDialogs} from './useUserManagementDialogs';

// Mocks
vi.mock('notistack', async () => {
  const actual = await vi.importActual<typeof import('notistack')>('notistack');
  return {
    ...actual,
    useSnackbar: () => ({enqueueSnackbar: vi.fn()}),
    SnackbarProvider: actual.SnackbarProvider,
  };
});

const mockUpdateUserStatus = vi.fn();
const mockResendInvitation = vi.fn();

vi.mock('../../../redux/auth/authApiSlice', () => ({
  useUpdateUserStatusMutation: () => [mockUpdateUserStatus, {isLoading: false}],
  useResendInvitationMutation: () => [mockResendInvitation, {isLoading: false}],
}));

const refetchUsers = vi.fn();

const user = {
  id: '1',
  firstName: 'Test',
  email: '<EMAIL>',
  roleName: 'Admin',
  status: 1,
  createdOn: '2023-01-01T00:00:00Z',
  modifiedOn: '2023-01-02T00:00:00Z',
};

describe('useUserManagementDialogs', () => {
  function wrapper({children}: {children: React.ReactNode}) {
    return <SnackbarProvider>{children}</SnackbarProvider>;
  }

  beforeEach(() => {
    mockUpdateUserStatus.mockClear();
    mockResendInvitation.mockClear();
    refetchUsers.mockClear();
  });

  it('should open and close activate dialog', () => {
    const {result} = renderHook(() => useUserManagementDialogs({refetchUsers}), {wrapper});

    act(() => {
      result.current.handleActionClick('activate', user as UserViewType);
    });
    expect(result.current.dialogOpen).toBe(true);
    expect(result.current.dialogAction).toBe('Activate');
    expect(result.current.dialogUser).toEqual(user);

    act(() => {
      result.current.handleDialogClose();
    });
    expect(result.current.dialogOpen).toBe(false);
    expect(result.current.dialogUser).toBeNull();
    expect(result.current.dialogAction).toBeNull();
  });

  it('should open and close resend dialog', () => {
    const {result} = renderHook(() => useUserManagementDialogs({refetchUsers}), {wrapper});

    act(() => {
      result.current.handleActionClick('send-link', user as UserViewType);
    });
    expect(result.current.resendDialogOpen).toBe(true);
    expect(result.current.resendDialogUser).toEqual(user);

    act(() => {
      result.current.handleResendDialogClose();
    });
    expect(result.current.resendDialogOpen).toBe(false);
    expect(result.current.resendDialogUser).toBeNull();
  });

  it('should call updateUserStatus and refetchUsers on confirm', async () => {
    mockUpdateUserStatus.mockReturnValueOnce({unwrap: async () => {}});
    const {result} = renderHook(() => useUserManagementDialogs({refetchUsers}), {wrapper});

    act(() => {
      result.current.handleActionClick('activate', user as UserViewType);
    });

    await act(async () => {
      await result.current.handleDialogConfirm();
    });

    expect(mockUpdateUserStatus).toHaveBeenCalled();
  });

  it('should call resendInvitation on resend confirm', async () => {
    mockResendInvitation.mockReturnValueOnce({unwrap: () => Promise.resolve()});
    const {result} = renderHook(() => useUserManagementDialogs({refetchUsers}), {wrapper});

    act(() => {
      result.current.handleActionClick('send-link', user as UserViewType);
    });

    await act(async () => {
      await result.current.handleResendDialogConfirm();
    });

    expect(mockResendInvitation).toHaveBeenCalled();
  });
});
