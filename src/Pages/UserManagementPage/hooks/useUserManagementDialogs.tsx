import {useSnackbar} from 'notistack';
import {useCallback, useState} from 'react';
import {UserViewType} from 'redux/app/types';
import {useResendInvitationMutation, useUpdateUserStatusMutation} from '../../../redux/auth/authApiSlice';

type DialogAction = 'Activate' | 'Deactivate' | null;
export type HandleActionType = 'activate' | 'deactivate' | 'send-link';

interface UseUserManagementDialogsProps {
  refetchUsers: () => void;
}

export function useUserManagementDialogs({refetchUsers}: UseUserManagementDialogsProps) {
  const [dialogOpen, setDialogOpen] = useState<boolean>(false);
  const [dialogAction, setDialogAction] = useState<DialogAction>(null);
  const [dialogUser, setDialogUser] = useState<UserViewType | null>(null);
  const [pendingAction, setPendingAction] = useState<(() => Promise<void>) | null>(null);

  const [resendDialogOpen, setResendDialogOpen] = useState<boolean>(false);
  const [resendDialogUser, setResendDialogUser] = useState<UserViewType | null>(null);

  const [updateUserStatus, {isLoading: isPatching}] = useUpdateUserStatusMutation();
  const [resendInvitation, {isLoading: isResending}] = useResendInvitationMutation();

  const {enqueueSnackbar} = useSnackbar();

  const handleActionClick = useCallback(
    (action: HandleActionType, user: UserViewType) => {
      if (action === 'activate' || action === 'deactivate') {
        setDialogAction(action === 'activate' ? 'Activate' : 'Deactivate');
        setDialogUser(user);
        setDialogOpen(true);
        setPendingAction(() => async () => {
          await updateUserStatus({id: user.id ?? '', status: action === 'activate' ? 1 : 2});
          if (action === 'activate') {
            enqueueSnackbar(`User activation is pending. Please complete onboarding to proceed!`, {
              variant: 'success',
              subMessage: user.firstName,
            });
          } else {
            enqueueSnackbar(`User deactivated successfully!`, {variant: 'success', subMessage: user.firstName});
          }
          refetchUsers();
        });
      }
      if (action === 'send-link') {
        setResendDialogUser(user);
        setResendDialogOpen(true);
      }
    },
    [updateUserStatus, enqueueSnackbar, refetchUsers],
  );

  const handleDialogClose = () => {
    setDialogOpen(false);
    setDialogUser(null);
    setDialogAction(null);
    setPendingAction(null);
  };

  const handleDialogConfirm = async () => {
    if (pendingAction) {
      await pendingAction();
    }
    setDialogOpen(false);
  };

  const handleResendDialogClose = () => {
    if (isResending) return;
    setResendDialogOpen(false);
    setResendDialogUser(null);
  };

  const handleResendDialogConfirm = async () => {
    const user = resendDialogUser;
    if (user) {
      try {
        await resendInvitation({id: user.id ?? ''}).unwrap();
        enqueueSnackbar('Activation link sent successfully!', {variant: 'success', subMessage: user.firstName});
      } catch {
        //supress sonar
      }
    }
    setResendDialogOpen(false);
    setResendDialogUser(null);
  };

  return {
    dialogOpen,
    dialogAction,
    dialogUser,
    handleDialogClose,
    handleDialogConfirm,
    resendDialogOpen,
    resendDialogUser,
    handleResendDialogClose,
    handleResendDialogConfirm,
    handleActionClick,
    isPatching,
    isResending,
  };
}
