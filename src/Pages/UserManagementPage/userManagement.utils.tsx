import type {CellContext} from '@tanstack/react-table';
import EllipsisText from 'Components/EllipsisText/EllipsisText';
import StatusChip from 'Components/StatusChip/StatusChip';
import {StatusChipState} from 'Components/StatusChip/statusChip.util';
import React from 'react';
import ActionButtons from './components/ActionButton';
import {HandleActionType} from './hooks/useUserManagementDialogs';
export {default as ActionButtons} from './components/ActionButton';

/**
 * Table row type for user management
 */
export interface UserTableRow {
  id: string;
  firstName: string;
  email: string;
  roleName: string;
  status: UserStatus;
  createdOn: string;
  modifiedOn: string;
}

/**
 * User status enumeration for users
 */
export enum UserStatus {
  PENDINGPROVISION,
  ACTIVE,
  INACTIVE,
}

/**
 * Default number of rows per page in user management table.
 */
export const DEFAULT_LIMIT = 5;

/**
 * Default offset for pagination in user management table.
 */
export const DEFAULT_OFFSET = 0;

/**
 * Returns the label for a given user status.
 * @param status - The user status or its numeric value.
 * @returns Status label string.
 */
export const getUserStatusLabel = (status: UserStatus | number): string => {
  const statusLabelMap: Record<UserStatus | number, string> = {
    [UserStatus.ACTIVE]: 'Active',
    [UserStatus.INACTIVE]: 'Inactive',
    [UserStatus.PENDINGPROVISION]: 'Pending activation',
  };
  return statusLabelMap[status] ?? 'Pending activation';
};

/**
 * Maps frontend column names to backend field names for sorting.
 */
const columnNameMap: Record<string, string> = {
  name: 'firstName',
  email: 'email',
  role: 'roleName',
  status: 'status',
  createdDate: 'createdOn',
  modifiedDate: 'modifiedOn',
};

const userStatusMapToStatusChip: Record<UserStatus, StatusChipState> = {
  [UserStatus.ACTIVE]: StatusChipState.ACTIVE,
  [UserStatus.PENDINGPROVISION]: StatusChipState.PENDINGPROVISION,
  [UserStatus.INACTIVE]: StatusChipState.INACTIVE,
};

/**
 * Returns the backend field name for a given frontend column name.
 * @param columnName - The frontend column name.
 * @returns Backend field name.
 */
export const getBackendColumnName = (columnName: string): string => columnNameMap[columnName] || columnName;

/**
 * Columns definition for the user management table.
 * Each column configures header, accessor key, cell renderer, and width.
 */
export type UserActionsColumnMeta = {
  onAction?: (action: HandleActionType, row: UserTableRow) => void;
};

export const userColumns: Array<{
  accessorKey?: keyof UserTableRow;
  header: string;
  cell?: (context: CellContext<UserTableRow, UserActionsColumnMeta>) => React.ReactNode;
  meta?: UserActionsColumnMeta;
}> = [
  {
    accessorKey: 'firstName',
    header: 'Name',
    // Renders user's name with tooltip and ellipsis for overflow
    cell: ({row}) => <EllipsisText text={row.original.firstName || '-'} />,
  },
  {
    accessorKey: 'email',
    header: 'Email Address',
    // Renders user's email with tooltip and ellipsis for overflow
    cell: ({row}) => <EllipsisText text={row.original.email || '-'} />,
  },
  {
    accessorKey: 'roleName',
    header: 'Role',
    // Renders user's role with tooltip
    cell: ({row}) => <EllipsisText text={row.original.roleName || '-'} />,
  },
  {
    accessorKey: 'status',
    header: 'Status',
    // Renders user's status with colored StatusChip
    cell: ({row}) => {
      const status = row.original.status;
      return <StatusChip label={getUserStatusLabel(status)} status={userStatusMapToStatusChip[status]} />;
    },
  },
  {
    accessorKey: 'createdOn',
    header: 'Created On',
    // Renders formatted creation date with tooltip
    cell: ({row}) => {
      const date = new Date(row.original.createdOn);
      const parts = date
        .toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
        .split(' ');
      const formatted = `${parts[0]} ${parts[1].replace('.', '')}, ${parts[2]}`;
      return <EllipsisText text={formatted} />;
    },
  },
  {
    accessorKey: 'modifiedOn',
    header: 'Modified On',
    // Renders formatted modification date with tooltip
    cell: ({row}) => {
      const date = new Date(row.original.modifiedOn);
      const parts = date
        .toLocaleDateString('en-GB', {
          day: '2-digit',
          month: 'short',
          year: 'numeric',
        })
        .split(' ');
      const formatted = `${parts[0]} ${parts[1].replace('.', '')}, ${parts[2]}`;
      return <EllipsisText text={formatted} />;
    },
  },
  {
    header: 'Actions',
    // Renders action buttons for each row
    cell: (ctx: CellContext<UserTableRow, UserActionsColumnMeta>) => (
      <ActionButtons
        tableRow={ctx.row.original}
        onAction={(action: HandleActionType) =>
          (ctx.column.columnDef.meta as UserActionsColumnMeta)?.onAction
            ? (ctx.column.columnDef.meta as UserActionsColumnMeta).onAction!(action, ctx.row.original)
            : undefined
        }
      />
    ),
  },
];
