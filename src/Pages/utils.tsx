import {Box} from '@mui/material';
import React from 'react';
import {GetCity, GetState} from 'react-country-state-city';

type FilterButtonProps = {
  filterButtonRef: React.RefObject<HTMLButtonElement | null>;
  buttonHeight: string;
  filterSize: number;
  hasFilters: boolean;
  setOpenFilter: (open: boolean) => void;
  FilterIcon: string;
  BorderButton: React.ElementType;
  SVGImageFromPath: React.ElementType;
};

/**
 * Renders a filter button with an optional counter badge indicating the number of active filters.
 *
 * @param filterButtonRef - A ref object to attach to the filter button for DOM access.
 * @param buttonHeight - The height of the filter button.
 * @param filterSize - The number of active filters to display in the counter badge.
 * @param hasFilters - Boolean indicating whether any filters are currently applied.
 * @param setOpenFilter - Function to open the filter dialog or panel.
 * @param FilterIcon - The icon to display inside the filter button.
 * @param BorderButton - The button component to use for rendering the filter button.
 * @param SVGImageFromPath - A component to render the SVG icon from a given path.
 * @returns A React element representing the filter button with an optional counter badge.
 */
export function renderFilterButton({
  filterButtonRef,
  buttonHeight,
  filterSize,
  hasFilters,
  setOpenFilter,
  FilterIcon,
  BorderButton,
  SVGImageFromPath,
}: FilterButtonProps) {
  return (
    <Box sx={{position: 'relative', display: 'inline-block'}}>
      <BorderButton
        ref={filterButtonRef}
        sx={{
          height: buttonHeight,
          minWidth: buttonHeight,
          p: 0,
          borderColor: filterSize > 0 ? 'secondary.main' : 'body.100',
        }}
        onClick={() => setOpenFilter(true)}
      >
        <SVGImageFromPath path={FilterIcon} sx={{width: '0.8125rem', color: 'body.800'}} />
      </BorderButton>
      {/* Counter */}
      {hasFilters && (
        <Box
          sx={{
            position: 'absolute',
            top: '-15%',
            right: '-15%',
            backgroundColor: 'primary.main',
            color: 'white.main',
            borderRadius: '50%',
            minWidth: 18,
            height: 18,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: '0.75rem',
            fontWeight: 700,
            zIndex: 1,
            boxShadow: 1,
            px: 0.5,
          }}
        >
          {filterSize}
        </Box>
      )}
    </Box>
  );
}

/**
 * Asynchronously retrieves the ID of a US state whose name includes the provided `stateName`.
 *
 * @param stateName - The name (or partial name) of the state to search for.
 * @returns A promise that resolves to the state's ID as a string, or an empty string if not found or on error.
 */
export const getStateOptions = async (stateName: string): Promise<string> => {
  const USA_ID = 233;
  try {
    const states = await GetState(USA_ID);

    const filteredStates = states.filter((state: {name: string; id?: number}) =>
      state.name.toLowerCase().includes(stateName.toLowerCase()),
    );
    if (!filteredStates.length) return '';
    return filteredStates[0].id?.toString() ?? '';
  } catch {
    return '';
  }
};

/**
 * Retrieves the city ID as a string for a given state and city name within the USA.
 *
 * @param stateId - The ID of the state as a string.
 * @param cityName - The name of the city to search for.
 * @returns A promise that resolves to the city ID as a string if found, or an empty string if not found or on error.
 */
export const getCityOptions = async (stateId: string, cityName: string): Promise<string> => {
  const USA_ID = 233;
  try {
    const cities = await GetCity(USA_ID, Number(stateId));
    const match = (cities || []).find(city => city.name.toLowerCase() === cityName.toLowerCase());

    return match?.id?.toString() ?? '';
  } catch {
    return '';
  }
};
