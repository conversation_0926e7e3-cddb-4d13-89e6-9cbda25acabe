// @vitest-environment jsdom
import * as mui from '@mui/material';
import {render, screen} from '@testing-library/react';
import DeviceWidthProvider from './DeviceWidthProvider';

vi.mock('@mui/material', async () => {
  const actual = await vi.importActual<any>('@mui/material');
  return {
    ...actual,
    useTheme: () => ({
      breakpoints: {
        up: (bp: string) => `@media (min-width:${bp})`,
      },
    }),
  };
});

describe('DeviceWidthProvider', () => {
  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders children when isGreaterThanBreakPoint is true', () => {
    vi.spyOn(mui, 'useMediaQuery').mockImplementation(() => true);
    render(
      <DeviceWidthProvider breakpoint="md">
        <div>Visible</div>
      </DeviceWidthProvider>,
    );
    expect(screen.getByText('Visible')).toBeInTheDocument();
  });

  it('renders nothing when isGreaterThanBreakPoint is false', () => {
    vi.spyOn(mui, 'useMediaQuery').mockImplementation(() => false);
    render(
      <DeviceWidthProvider breakpoint="md">
        <div>Hidden</div>
      </DeviceWidthProvider>,
    );
    expect(screen.queryByText('Hidden')).toBeNull();
  });
});
