// @vitest-environment jsdom
import {render, screen} from '@testing-library/react';
import ErrorBoundary from './ErrorBoundary';

describe('ErrorBoundary', () => {
  const origError = console.error;
  beforeEach(() => {
    // Suppress error output for cleaner test logs
    console.error = vi.fn();
  });
  afterEach(() => {
    console.error = origError;
  });

  it('renders children when no error', () => {
    render(
      <ErrorBoundary>
        <div>Safe Child</div>
      </ErrorBoundary>,
    );
    expect(screen.getByText('Safe Child')).toBeInTheDocument();
  });

  it('renders fallback UI when child throws', () => {
    // Component that throws on render
    const ProblemChild = () => {
      throw new Error('Boom');
    };
    render(
      <ErrorBoundary>
        <ProblemChild />
      </ErrorBoundary>,
    );
    expect(screen.getByRole('heading', {level: 1})).toHaveTextContent('Something went wrong');
  });
});
