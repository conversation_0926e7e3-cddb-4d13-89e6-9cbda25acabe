import Slide from '@mui/material/Slide';
import CustomSnackbarView from 'Components/CustomSnackbarView/CustomSnackbarView';
import {SnackbarProvider} from 'notistack';
import {ReactNode} from 'react';

/**
 * NotificationProvider component
 * @param param0 - Props for the NotificationProvider
 * @returns JSX.Element
 */
const NotificationProvider = ({children}: {children: ReactNode}) => {
  return (
    <SnackbarProvider
      TransitionComponent={Slide}
      anchorOrigin={{
        vertical: 'top',
        horizontal: 'right',
      }}
      autoHideDuration={5000}
      maxSnack={5}
      Components={{
        default: CustomSnackbarView, // Custom view for default notifications
        success: CustomSnackbarView, // Custom view for success notifications
        error: CustomSnackbarView, // Custom view for error notifications
        info: CustomSnackbarView, // Custom view for info notifications
        warning: CustomSnackbarView, // Custom view for warning notifications
      }}
    >
      {children}
    </SnackbarProvider>
  );
};

export default NotificationProvider;
