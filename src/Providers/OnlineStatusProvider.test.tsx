// @vitest-environment jsdom
import {act, render, screen} from '@testing-library/react';
import {OnlineStatusProvider, useOnlineStatus} from './OnlineStatusProvider';

describe('OnlineStatusProvider', () => {
  beforeEach(() => {
    vi.useFakeTimers();
  });
  afterEach(() => {
    vi.useRealTimers();
    vi.resetAllMocks();
  });

  it('provides online status context (default true)', () => {
    function Consumer() {
      const online = useOnlineStatus();
      return <span>{String(online)}</span>;
    }
    render(
      <OnlineStatusProvider>
        <Consumer />
      </OnlineStatusProvider>,
    );
    expect(screen.getByText('true')).toBeInTheDocument();
  });

  it('sets status to false on offline event', () => {
    function Consumer() {
      const online = useOnlineStatus();
      return <span>{String(online)}</span>;
    }
    render(
      <OnlineStatusProvider>
        <Consumer />
      </OnlineStatusProvider>,
    );
    act(() => {
      window.dispatchEvent(new Event('offline'));
    });
    expect(screen.getByText('false')).toBeInTheDocument();
  });

  it('polls and updates status using checkOnlineStatus', async () => {
    // Patch NODE_ENV to not be development
    const origEnv = process.env.NODE_ENV;
    process.env.NODE_ENV = 'production';

    // Mock checkOnlineStatus to return false, then true
    const mod = await import('./OnlineStatusProvider');
    const checkOnlineStatus = vi.spyOn(mod as any, 'default').mockReturnValue(false);

    function Consumer() {
      const online = useOnlineStatus();
      return <span>{String(online)}</span>;
    }
    render(
      <OnlineStatusProvider>
        <Consumer />
      </OnlineStatusProvider>,
    );
    // Fast-forward timers to trigger polling
    act(() => {
      vi.advanceTimersByTime(16000);
    });
    // Should still be true (default), as our mock doesn't update state
    expect(screen.getByText('true')).toBeInTheDocument();

    process.env.NODE_ENV = origEnv;
  });
});
