import CssBaseline from '@mui/material/CssBaseline';
import {createTheme, ThemeProvider as MuiThemeProvider, Theme} from '@mui/material/styles';
import {createContext, ReactNode, useEffect, useMemo, useState} from 'react';
import {commonConfig, paletteConfig} from './default';

export const ThemeContext = createContext({
  toggleColorMode: () => {},
});

let defaultTheme: Theme | undefined;

export const getDefaultTheme = () => defaultTheme;

const ThemeProvider = ({children}: {children: ReactNode}) => {
  const [mode, setMode] = useState<'light' | 'dark'>('light');

  const colorMode = useMemo(
    () => ({
      toggleColorMode: () => {
        setMode(prevMode => (prevMode === 'light' ? 'dark' : 'light'));
      },
    }),
    [],
  );

  const theme = useMemo(
    () =>
      createTheme({
        palette: {
          mode,
          ...(mode === 'light' ? paletteConfig.light : paletteConfig.dark),
        },
        ...commonConfig,
        components: {
          ...commonConfig.components,
          MuiButton: {
            styleOverrides: {
              sizeLarge: {
                minHeight: '3.125rem', // 50px converted to rem
                padding: 0,
              },
            },
          },
        },
      }),
    [mode],
  );

  useEffect(() => {
    defaultTheme = theme;
  }, [theme]);

  return (
    <ThemeContext.Provider value={colorMode}>
      <MuiThemeProvider theme={theme}>
        <CssBaseline />
        {children}
      </MuiThemeProvider>
    </ThemeContext.Provider>
  );
};

export default ThemeProvider;
