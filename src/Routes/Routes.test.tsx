import {render, screen} from '@testing-library/react';
import {authorizationFunctions} from 'Helpers/authorizationFunctions';
import NotificationProvider from 'Providers/NotificationProvider';
import PropTypes from 'prop-types';
import {Provider} from 'react-redux';
import {MemoryRouter, Outlet, RouteObject} from 'react-router-dom';
import {vi} from 'vitest';
import {store} from '../redux/store';
import Routes from './Routes';
import {getRouteConfig} from './layoutRouteConfig';

// --- Robust theme mock for all palette keys used in Login and Home pages ---
import {ThemeProvider, createTheme} from '@mui/material/styles';

const robustTheme = createTheme({
  palette: {
    body: {
      dark: '#222',
      100: '#f5f5f5',
      200: '#eeeeee',
      400: '#bdbdbd',
      500: '#9e9e9e',
      800: '#424242',
    },
    white: {
      main: '#fff',
      100: '#fff',
      200: '#f8f8f8',
    },
    primary: {main: '#1976d2'},
    secondary: {main: '#dc004e'},
    alert: {
      success: {
        main: '#4caf50',
        bg: '#e0ffe0',
        border: '#388e3c',
        onBg: '#e0ffe0',
      },
      error: {
        main: '#f44336',
        bg: '#ffebee',
        border: '#d32f2f',
        onBg: '#ffebee',
      },
      warning: {
        main: '#ff9800',
        bg: '#fff3e0',
        border: '#f57c00',
        onBg: '#fff3e0',
      },
      info: {
        main: '#2196f3',
        bg: '#e3f2fd',
        border: '#1976d2',
        onBg: '#e3f2fd',
      },
    },
  },
});
vi.mock('@mui/material/styles', async importOriginal => {
  const actual = await importOriginal();
  return Object.assign({}, actual, {
    useTheme: () => robustTheme,
  });
});

const TestApp: React.FC<{initialEntries: string[]}> = ({initialEntries}) => {
  return (
    <Provider store={store}>
      <MemoryRouter initialEntries={initialEntries}>
        <ThemeProvider theme={robustTheme}>
          <NotificationProvider>
            <Routes routesConfig={getRouteConfig()} />
          </NotificationProvider>
        </ThemeProvider>
      </MemoryRouter>
    </Provider>
  );
};

TestApp.propTypes = {
  initialEntries: PropTypes.arrayOf(PropTypes.string.isRequired).isRequired,
};

describe('Default AppRoutes', () => {
  beforeEach(() => {
    // Mock the authentication function to return false by default
    authorizationFunctions.isAuthenticated = vi.fn().mockReturnValue(false);
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it('renders routes properly', async () => {
    authorizationFunctions.isAuthenticated = vi.fn().mockReturnValue(false);
    render(<TestApp initialEntries={['/login']} />);
    const loginPage = await screen.findByTestId('LoginPage');
    expect(loginPage).toBeInTheDocument();
  });

  it('redirects unauthenticated user to login page when he tries to access a protected route', async () => {
    authorizationFunctions.isAuthenticated = vi.fn().mockReturnValue(false);
    expect(authorizationFunctions.isAuthenticated()).toBe(false);
    render(<TestApp initialEntries={['/home']} />);
    const loginPage = await screen.findByTestId('LoginPage');
    expect(loginPage).toBeInTheDocument();
  });

  // commenting out this test case due to timeout issue on AWS console
  // it('authenticated users can access protected route', async () => {
  //   authorizationFunctions.isAuthenticated = vi.fn().mockReturnValue(true);
  //   expect(authorizationFunctions.isAuthenticated()).toBe(true);
  //   render(<TestApp initialEntries={['/home']} />);
  //   await waitFor(
  //     async () => {
  //       const homePage = await screen.findByTestId('HomePage');
  //       expect(homePage).toBeInTheDocument();
  //     },
  //     {timeout: 5000},
  //   );
  // });
});

describe('Route Component', () => {
  it('should support nested routing', () => {
    const nestedRoutes: RouteObject[] = [
      {
        path: '/home',
        children: [
          {
            index: true,
            element: (
              <div>
                Home Component <Outlet />
              </div>
            ),
          },
          {
            path: 'test',
            element: <div>Test Component</div>,
          },
        ],
      },
    ];
    const ExampleApp = () => <Routes routesConfig={nestedRoutes} />;

    render(
      <MemoryRouter initialEntries={['/home/<USER>']}>
        <ExampleApp />
      </MemoryRouter>,
    );
    expect(screen.getByText(/test component/i)).toBeInTheDocument();
  });
});
