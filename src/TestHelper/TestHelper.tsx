import {createTheme} from '@mui/material';
import {LocalizationProvider} from '@mui/x-date-pickers';
import {AdapterDateFns} from '@mui/x-date-pickers/AdapterDateFns';
import {configureStore} from '@reduxjs/toolkit';
import {render} from '@testing-library/react';
import {PermissionProvider} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import {AnyObject} from 'Helpers/utils';
import {paletteConfig} from 'Providers/theme/default';
import ThemeProvider from 'Providers/theme/ThemeProvider';
import {Provider} from 'react-redux';
import {MemoryRouter} from 'react-router-dom';
import {apiSlice} from 'redux/apiSlice';
import {vi} from 'vitest';

const createTestStore = () => {
  return configureStore({
    reducer: {
      [apiSlice.reducerPath]: apiSlice.reducer,
    },
    middleware: getDefaultMiddleware => getDefaultMiddleware().concat(apiSlice.middleware),
  });
};

export const TestWrapper = ({children}: {children: React.ReactNode}) => {
  return (
    <Provider store={createTestStore()}>
      <PermissionProvider>
        <ThemeProvider>
          <LocalizationProvider dateAdapter={AdapterDateFns}>{children}</LocalizationProvider>
        </ThemeProvider>
      </PermissionProvider>
    </Provider>
  );
};

export const memoryRenderWithTheme = (ui: React.ReactElement) =>
  renderWithTheme(
    <MemoryRouter>
      <TestWrapper>{ui}</TestWrapper>
    </MemoryRouter>,
  );

export const renderWithTheme = (ui: React.ReactElement) => render(<TestWrapper>{ui}</TestWrapper>);

export const testingTheme = createTheme({
  palette: {
    ...paletteConfig.light,
  },
});
export const testingPalette = testingTheme.palette;

export function useThemeColor(path: string) {
  return path.split('.').reduce((obj: AnyObject, key) => obj?.[key], testingPalette);
}

export const setupCommonMocks = () => {
  vi.mock('Components/PermissionRedirectWrapper/PermissionRedirectWrapper', async original => ({
    ...(await original()),
    default: (props: AnyObject) => <div data-testid="permission-redirect-wrapper">{props.children}</div>,
  }));

  vi.mock('Components/PermissionWrapper/PermissionWrapper', async original => ({
    ...(await original()),
    default: (props: AnyObject) => <div data-testid="permission-wrapper">{props.children}</div>,
  }));

  vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', async original => ({
    ...(await original()),
    usePermissions: () => ({
      hasPermission: vi.fn().mockReturnValue(true),
    }),
  }));

  vi.mock('react-router', async importOriginal => ({
    ...(await importOriginal()),
    useNavigate: () => vi.fn(),
  }));

  vi.mock('Components/PermissionWrapper/PermissionWrapper', async importOriginal => ({
    ...(await importOriginal()),
    default: (props: AnyObject) => <div data-testid="permission-wrapper">{props.children}</div>,
  }));

  vi.mock('Components/PermissionRedirectWrapper/PermissionProvider', async importOriginal => ({
    ...(await importOriginal()),
    default: (props: AnyObject) => <div data-testid="permission-provider">{props.children}</div>,
  }));
};
