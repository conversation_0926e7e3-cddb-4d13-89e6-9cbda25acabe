import {render} from '@testing-library/react';
import {PermissionProvider} from 'Components/PermissionRedirectWrapper/PermissionProvider';
import {Provider} from 'react-redux';
import {renderWithTheme} from 'TestHelper/TestHelper';
import {createMockStore} from './mockStore';
/**
 * Renders a react component with mock redux store.
 * @param ui - component passed to render
 * @param preloadedState - Initial state for mock store.
 */

export const renderWithStore = (
  component: React.ReactElement,
  {preloadedState = {}, store = createMockStore(preloadedState)} = {},
) => {
  return {
    ...render(<Provider store={store}>{component}</Provider>),
    store,
  };
};

export const renderWithThemedStore = (
  component: React.ReactElement,
  {preloadedState = {}, store = createMockStore(preloadedState)} = {},
) => {
  return {
    ...renderWithTheme(
      <PermissionProvider>
        <Provider store={store}>{component}</Provider>
      </PermissionProvider>,
    ),
    store,
  };
};
