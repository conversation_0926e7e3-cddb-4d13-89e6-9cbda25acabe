// src/config/theme.ts
import {createTheme, ThemeOptions} from '@mui/material/styles';

const blackMain = 'black.main';
const whiteMain = 'white.main';

const themeOptions: ThemeOptions = {
  palette: {
    mode: 'light',
    primary: {
      main: blackMain,
    },
    background: {
      default: 'white.100',
      paper: whiteMain,
    },
    text: {
      primary: blackMain,
      disabled: 'body.100',
    },
  },
  typography: {
    fontFamily: "'Lato', 'Inter', 'Roboto', sans-serif",
    h1: {
      fontSize: '2.25rem',
      fontWeight: 700,
    },
    h6: {
      fontSize: '1.25rem',
      fontWeight: 500,
    },
    body1: {
      fontSize: '1rem',
      fontWeight: 400,
    },
    button: {
      textTransform: 'none',
      fontSize: '0.875rem',
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          padding: '0.75rem 1.5rem',
          borderRadius: 8,
        },
        containedPrimary: {
          color: whiteMain,
          backgroundColor: blackMain,
        },
      },
    },
  },
};

const theme = createTheme(themeOptions);

export default theme;
