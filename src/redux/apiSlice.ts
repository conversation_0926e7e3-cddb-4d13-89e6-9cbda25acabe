import type {BaseQueryFn, FetchBaseQueryError} from '@reduxjs/toolkit/query';
import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import {ApiSliceIdentifier} from 'Constants/enums';
import {getErrorMessage} from 'Helpers/utils';
import {enqueueSnackbar} from 'notistack';
import {AuthData, setCredentials, unsetCredentials} from './auth/authSlice';
import {getBaseUrl} from './redux.helper';
import type {RootState} from './store';

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
const RESULT_ERROR_STATUS = 401;

let refreshingToken: Promise<AuthData | null> | null = null;

const baseQueryWithReauth: BaseQueryFn<
  {url: string; method?: string; body?: unknown; apiSliceIdentifier?: ApiSliceIdentifier},
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const state = api.getState() as RootState;
  const baseUrl = getBaseUrl(state, args.apiSliceIdentifier);

  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders(headers, {getState}) {
      const token = (getState() as RootState).auth.accessToken;
      if (token) {
        headers.set('Authorization', `Bearer ${token}`);
      }
      return headers;
    },
  });

  let result = await baseQuery(args, api, extraOptions);

  if (result.error?.status === RESULT_ERROR_STATUS) {
    // refresh handling
    if (!refreshingToken) {
      refreshingToken = (async () => {
        const refreshResult = await baseQuery(
          {
            url: getBaseUrl(state) + '/auth/token-refresh',
            method: 'POST',
            body: {refreshToken: (api.getState() as RootState).auth.refreshToken},
          },
          api,
          extraOptions,
        );

        if (refreshResult.data) {
          const newAuth = refreshResult.data as AuthData;
          api.dispatch(setCredentials(newAuth));
          return newAuth;
        } else {
          // refresh failed → force logout
          api.dispatch(unsetCredentials());
          // reset RTK Query cache + cancel inflight
          api.dispatch(apiSlice.util.resetApiState());
          enqueueSnackbar('Session expired. Please log in again.', {variant: 'error'});
          return null;
        }
      })();

      refreshingToken.finally(() => {
        refreshingToken = null;
      });
    }

    const newAuth = await refreshingToken;

    if (newAuth) {
      // retry the original request with the new token
      result = await baseQuery(args, api, extraOptions);
    } else {
      // no new auth, return original 401 error
      return {error: {status: 401, data: 'Session expired'}} as {
        error: FetchBaseQueryError;
      };
    }
  } else if (result.error) {
    const errorMessage = getErrorMessage(result.error);
    enqueueSnackbar(`${errorMessage}`, {variant: 'error'});
  } else {
    // handle other errors
  }

  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
});
