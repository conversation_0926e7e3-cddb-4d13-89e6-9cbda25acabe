import {ApiSliceIdentifier} from 'Constants/enums';
import {apiSlice} from 'redux/apiSlice';
import {LeadApiDTO, LeadApiForCountDTO, LeadCreateType, LeadFilterDTO, LeadType} from './types';

const apiSliceIdentifier = ApiSliceIdentifier.TENANT_FACADE;

const composeLeadFilter = (filterQuery: LeadApiDTO): LeadFilterDTO => {
  if (Object.keys(filterQuery).length === 0) return {};

  const filter: LeadFilterDTO = {
    ...pickBasicFilters(filterQuery),
  };

  const where = {
    ...filterQuery.where,
    ...buildStatusFilter(filterQuery),
    ...buildDateRangeFilter(filterQuery),
    ...buildSearchFilter(filterQuery),
  };

  if (Object.keys(where).length > 0) {
    filter.where = where;
  }

  return {
    ...filter,
    include: [{relation: 'address'}],
  };
};

function pickBasicFilters(query: LeadApiDTO): Partial<LeadFilterDTO> {
  const {include, limit, offset, order} = query;
  return {include, limit, offset, order};
}

function buildStatusFilter(query: LeadApiDTO) {
  return query.status?.length ? {status: {inq: query.status}} : {};
}

function buildDateRangeFilter(query: LeadApiDTO) {
  if (!query.dateRange) return {};
  const {startDate, endDate} = query.dateRange;
  return {createdOn: {between: [startDate.toISOString(), endDate.toISOString()]}};
}

function buildSearchFilter(query: LeadApiDTO) {
  const search = query.searchValue?.trim();
  return search ? {companyName: {ilike: `%${search}%`}} : {};
}

export const tenantApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    getLeads: builder.query<LeadType[], LeadApiDTO>({
      query: (filterQuery: LeadApiDTO) => {
        const filter = composeLeadFilter(filterQuery);
        return {
          url: '/leads',
          method: 'GET',
          params: {
            filter: JSON.stringify(filter),
          },
          apiSliceIdentifier,
        };
      },
    }),
    getLeadsCount: builder.query({
      query: (filterQuery: LeadApiForCountDTO = {}) => {
        const filter = composeLeadFilter(filterQuery);
        return {
          url: '/leads/count',
          method: 'GET',
          params: filter?.where && {
            where: JSON.stringify({
              ...filter.where,
            }),
          },
          apiSliceIdentifier,
        };
      },
    }),
    createLead: builder.mutation({
      query: (leadData: LeadCreateType) => ({
        url: '/leads',
        method: 'POST',
        body: leadData,
        apiSliceIdentifier,
      }),
    }),
    getAllLeadStatuses: builder.query({
      query: () => ({
        url: '/leads/all-status',
        method: 'GET',
        apiSliceIdentifier,
      }),
    }),
    getLeadById: builder.query<LeadType, string>({
      query: (tenantId: string) => ({
        url: `/leads/${tenantId}`,
        method: 'GET',
        params: {
          filter: JSON.stringify({include: [{relation: 'address'}]}),
        },
        apiSliceIdentifier,
      }),
    }),
    updateLeadById: builder.mutation({
      query: ({leadId, status}: {leadId: string; status: number}) => ({
        url: `/leads/${leadId}`,
        method: 'PATCH',
        apiSliceIdentifier,
        body: {status},
      }),
    }),
  }),
});

export const {
  useGetLeadsQuery,
  useGetLeadsCountQuery,
  useLazyGetLeadsCountQuery,
  useLazyGetLeadsQuery,
  useCreateLeadMutation,
  useGetAllLeadStatusesQuery,
  useGetLeadByIdQuery,
  useLazyGetLeadByIdQuery,
  useUpdateLeadByIdMutation,
} = tenantApiSlice;
