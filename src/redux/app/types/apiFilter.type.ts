import {SelectedDateRange} from 'Components/DateRangeFilter/types';

/**
 * Interface for API filter parameters.
 * A base filter for pages has date range and search value.
 * Pagination is also included.
 */
export interface IApiFilter {
  status?: number[];
  dateRange?: SelectedDateRange;
  searchValue?: string;
  order?: string;
  limit?: number;
  offset?: number;
  where?: object;
  include?: {relation: string}[];
}
