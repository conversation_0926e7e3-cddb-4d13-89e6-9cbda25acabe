import {SelectedDateRange} from 'Components/DateRangeFilter/types';

export interface ITenantBillingFilterDTO {
  limit?: number;

  offset?: number;
  order?: string;
  where?: {
    status?: {inq: string[]};
    name?: {ilike: string};
  };
  include?: {relation: string}[];
}

export interface IBillingCustomer {
  deleted: boolean;
  deletedOn?: string;
  deletedBy?: string;
  createdOn: string;
  modifiedOn?: string;
  createdBy?: string;
  modifiedBy: string;
  id: string;
  name: string;
  tenantId: string;
  customerId: string;
  paymentSourceId?: string;
}

export interface ITenantBillingInvoice {
  deleted: boolean;
  deletedOn?: string;
  deletedBy?: string;
  createdOn: string;
  modifiedOn?: string;
  createdBy: string;
  modifiedBy?: string;
  id: string;
  invoiceId: string;
  invoiceStatus: string;
  billingCustomerId: string;
  amount?: number;
  tax?: number;
  discount?: number;
  dueDate: string;
  customerId: string;
  tenantId: string;
  tenantName: string;
  billingCustomer?: IBillingCustomer;
}

export interface ITenantBillingsFilter {
  status?: string[];
  dateRange?: SelectedDateRange;
  searchValue?: string;
  order?: string;
  limit?: number;
  offset?: number;
  include?: {relation: string}[];
  billingCustomerId?: string;
  tenantId?: string;
}

export type TenantBillingsApiDTO = ITenantBillingsFilter;
export type TenantBillingsApiForCountDTO = Omit<ITenantBillingsFilter, 'limit' | 'offset'>;
