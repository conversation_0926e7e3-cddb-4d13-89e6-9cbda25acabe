import {IApiFilter} from './apiFilter.type';
import {TenantAddress} from './tenant.type';

export enum LeadStatus {
  'PENDING',
  'CONVERTED',
  'INVALID',
}

export interface LeadFilterDTO {
  limit?: number;
  offset?: number;
  order?: string;
  where?: {
    status?: {
      inq: number[];
    };
    createdOn?: {
      between?: string[];
    };
    name?: {
      ilike: string;
    };
  };
  include?: {relation: string}[];
}

export type LeadCreateType = {
  firstName: string;
  lastName: string;
  companyName: string;
  email: string;
  designation?: string;
  phoneNumber?: string;
  countryCode?: string;
  city: string;
  state: string;
};

export type LeadType = {
  id: string;
  firstName: string;
  lastName: string;
  companyName: string;
  email: string;
  designation?: string;
  phoneNumber?: string;
  countryCode?: string;
  status: LeadStatus;
  address?: TenantAddress;
  addressId: string;
  isValidated: boolean;
  deleted: boolean;
  deletedOn: string;
  deletedBy: string;
  createdOn: string;
  modifiedOn: string;
  createdBy: string;
  modifiedBy: string;
};

export type LeadApiDTO = IApiFilter;
export type LeadApiForCountDTO = Omit<IApiFilter, 'limit' | 'offset'>;
