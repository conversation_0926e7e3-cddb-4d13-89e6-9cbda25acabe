import {DateRangeFilterSelectedDate} from 'Components/DateRangeFilter/DateRangeFilter';
import {IApiFilter} from './apiFilter.type';

export type IRoleFilterModel = Pick<IApiFilter, 'limit' | 'order' | 'offset' | 'searchValue'> & {
  status?: number[];
  createdOnDateRange?: DateRangeFilterSelectedDate;
  modifiedDateRange?: DateRangeFilterSelectedDate;
};

export interface IRoleFilterDTO {
  limit?: number;
  offset?: number;
  order?: string;
  where?: {
    status?: {
      inq: number[];
    };
    createdOn?: {
      between?: string[];
    };
    modifiedOn?: {
      between?: string[];
    };
    name?: {
      ilike: string;
    };
  };
}

export const buildRoleFilterDto = (dto: IRoleFilterModel): IRoleFilterDTO => {
  return {
    limit: dto.limit,
    offset: dto.offset,
    order: dto.order,
    where: {
      ...(dto.status && dto.status?.length > 0 && {status: {inq: dto.status}}),
      ...(dto.createdOnDateRange && {
        createdOn: {
          between: [dto.createdOnDateRange?.startDate.toISOString(), dto.createdOnDateRange?.endDate.toISOString()],
        },
      }),
      ...(dto.modifiedDateRange && {
        modifiedOn: {
          between: [dto.modifiedDateRange?.startDate?.toISOString(), dto.modifiedDateRange?.endDate?.toISOString()],
        },
      }),
      ...(dto.searchValue &&
        dto.searchValue.length > 0 && {
          role_name: {
            ilike: `%${dto.searchValue}%`,
          },
        }),
    },
  };
};
