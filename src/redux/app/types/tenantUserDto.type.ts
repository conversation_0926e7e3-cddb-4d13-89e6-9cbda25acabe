import {AnyObject} from 'Helpers/utils';
import {IRoleFilterDTO, IRoleFilterModel} from './rolesDto.types';

/**
 * Data transfer object for updating a tenant user.
 */
export type UpdateTenantUserDto = {
  userId: string;
  fullName: string;
  roleId: string;
};

/**
 * Data transfer object for creating a tenant user.
 */

export type CreateTenantUserDto = Omit<UpdateTenantUserDto, 'userId'> & {
  email: string;
};

export type IUserFilterModel = IRoleFilterModel & {
  userTenantId: string;
};

export type IUserFilterDto = Omit<IRoleFilterDTO, 'where'> & {
  where: AnyObject;
};

export const buildUserViewFilterDto = (dto: IUserFilterModel): IUserFilterDto => {
  return {
    limit: dto.limit,
    offset: dto.offset,
    order: dto.order,
    where: {
      and: [
        ...[dto.status && dto.status?.length > 0 && {status: {inq: dto.status}}],
        ...[
          dto.createdOnDateRange && {
            createdOn: {
              between: [dto.createdOnDateRange?.startDate.toISOString(), dto.createdOnDateRange?.endDate.toISOString()],
            },
          },
        ],
        ...[
          dto.modifiedDateRange && {
            modifiedOn: {
              between: [dto.modifiedDateRange?.startDate?.toISOString(), dto.modifiedDateRange?.endDate?.toISOString()],
            },
          },
        ],
        ...[
          dto.searchValue &&
            dto.searchValue.length > 0 && {
              or: [
                {firstName: {ilike: `%${dto.searchValue}%`}},
                {lastName: {ilike: `%${dto.searchValue}%`}},
                {email: {ilike: `%${dto.searchValue}%`}},
              ],
            },
        ],
        ...[dto.userTenantId && {userTenantId: {neq: dto.userTenantId}}],
      ].filter(obj => obj),
    },
  };
};
