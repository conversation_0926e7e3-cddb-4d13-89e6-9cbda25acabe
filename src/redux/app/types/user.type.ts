export type UserViewType = {
  id?: string;
  firstName: string;
  lastName?: string;
  middleName?: string;
  username: string;
  email: string;
  designation?: string;
  phone?: string;
  authClientIds: string;
  lastLogin?: string;
  photoUrl?: string;
  defaultTenantId: string;
  tenantId: string;
  roleId: string;
  tenantName: string;
  tenantKey?: string;
  roleName?: string;
  userTenantId: string;
  gender?: string;
  dob?: string;
  status: number;
  deleted: boolean;
  deletedOn?: string;
  deletedBy?: string;
  createdOn?: string;
  modifiedOn?: string;
  createdBy?: string;
  modifiedBy?: string;
};
