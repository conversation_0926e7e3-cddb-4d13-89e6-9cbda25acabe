// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';
import {vi} from 'vitest';
vi.mock('redux/auth/authSlice', async original => ({
  ...(await original()),
  selectCurrentAuthState: vi.fn().mockReturnValue({
    accessToken: 'test.ewogICAgInBlcm1pc3Npb25zIjogWwogICAgICAgICIqIgogICAgXQp9.test',
    refreshToken: 'mockRefreshToken',
    expires: Date.now() + 1000 * 60 * 60,
  }),
}));
