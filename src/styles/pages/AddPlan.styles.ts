import {SxProps, Theme} from '@mui/material';
import {Integers} from 'Helpers/integers';

export const gridContainerStyle: SxProps<Theme> = {
  height: '100%',
  boxSizing: 'border-box',
  border: theme => `0.0625rem solid ${theme.palette.body[Integers.TwoHundred]}`,
  borderRadius: '0.5rem',
};

export const boxContainerStyle: SxProps<Theme> = {
  border: theme => `0.0625rem solid ${theme.palette.body[Integers.TwoHundred]}`,
  borderRadius: '0.5rem',
  padding: 3,
  backgroundColor: theme => theme.palette.white.main,
  width: '100%',
};

export const formHeaderFirstStyles: SxProps<Theme> = {
  fontWeight: 600,
  fontSize: '1rem',
  color: theme => theme.palette.body.dark,
};

export const formHeaderStyles = {
  ...formHeaderFirstStyles,
  mt: '1.25rem',
};

export const inputAdornment: SxProps<Theme> = {
  borderRight: theme => `0.0625rem solid ${theme.palette.body[Integers.OneHundred]}`,
  backgroundColor: theme => theme.palette.white[Integers.ThreeHundred],
  maxHeight: 'unset',
  height: '2.625rem',
  padding: ' 0 1rem',
  color: theme => theme.palette.body[Integers.EightHundred],
};

export const inputBoxForAdornmentStyles: SxProps<Theme> = {
  fontSize: '1rem',
  fontWeight: 500,
  lineHeight: '1.375rem',
  borderColor: theme => theme.palette.body[Integers.OneHundred],
  borderRadius: '0.25rem',
  height: '2.625rem',
  padding: '0',
};

export const buttonGridStyles = {
  display: 'flex',
  justifyContent: 'flex-end',
  gap: 2,
  paddingRight: '1.25rem',
  paddingLeft: '1.25rem',
  paddingBottom: '1.25rem',
};

export const cancelButtonStyles: SxProps<Theme> = {
  width: '6.25rem',
  height: '3.125rem',
  borderColor: 'body.200',
  color: theme => theme.palette.body.dark,
  fontSize: '0.25rem 1rem 1rem 1rem',
};

export const submitButtonStyles: SxProps<Theme> = {
  width: '9rem',
  height: '3.125rem',
  backgroundColor: theme => theme.palette.secondary.main,
  color: theme => theme.palette.white.main,
  '&:hover': {
    backgroundColor: theme => theme.palette.secondary.main,
  },
  '&.Mui-disabled': {
    backgroundColor: theme => theme.palette.secondary[Integers.OneHundred],
    color: theme => theme.palette.white.main, // optional: set disabled text color
  },
  fontSize: '1rem',
};

export const toggleHeaderStyles: SxProps<Theme> = {
  fontSize: '1rem',
  fontWeight: 700,
  color: theme => theme.palette.body.dark,
  lineHeight: '1.25rem',
};

export const toggleTextStyles: SxProps<Theme> = {
  color: theme => theme.palette.body.dark[Integers.ThreeHundred],
  fontWeight: 400,
  fontSize: '0.875rem',
  lineHeight: '1.125rem',
  mt: 0.25,
};
