import {Breakpoint, SxProps, Theme} from '@mui/material';

/**
 * Common styles for the header box.
 */
export const commonHeaderBoxStyle: SxProps = {
  pb: 1,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
};

/**
 * Common styles for the left header.
 */
export const leftHeaderStyle: SxProps<Theme> = {
  fontWeight: 700,
  color: 'body.dark',
};

/**
 * Common styles for the action buttons.
 */

export const actionStyles: SxProps<Theme> = {
  color: 'body.500',
  fontSize: '1.25rem',
  fill: theme => theme.palette.white.main,
  cursor: 'pointer',
};

/*
 * Grid size for the user detail view.
 */
export const gridSize: {[key in Breakpoint]?: number | null} = {xs: 12, sm: 4, md: 3};

/**
 * Styles for the title label in the user detail view.
 */
export const detailPageTitleLabelSx: SxProps = {
  fontWeight: 600,
  fontSize: '0.75rem',
  color: 'body.500',
};

/**
 * Styles for the subtitle label in the user detail view.
 */
export const detailPageSubtitleLabelSx: SxProps = {
  fontWeight: 600,
  fontSize: '0.875rem',
  color: 'body.800',
  textTransform: 'capitalize',
  overflow: 'hidden',
  textOverflow: 'ellipsis',
  maxWidth: '100%',
};
