import {Box, styled, SxProps, Theme, Typography} from '@mui/material';
import Button from 'Components/Button';
import {Integers} from 'Helpers/integers';

export const LogoContainer = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
}));

export const IconWrapper = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
  marginTop: '2rem',
  marginBottom: '2rem',
}));

export const KeyIconContainer = styled(Box)(({theme}) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  width: '5rem',
  height: '5rem',
  borderRadius: '50%',
  border: `0.0625 solid ${theme.palette.white[Integers.TwoHundred]}`,
  padding: '1rem',
  backgroundColor: theme.palette.white[Integers.TwoHundred],
}));

const spacing = 0.75;

export const WelcomeTitle = styled(Typography)(({theme}) => ({
  fontFamily: theme.typography.fontFamily,
  fontWeight: 700,
  color: theme.palette.body.dark,
  marginBottom: theme.spacing(spacing),
  fontSize: '1.125rem', // 18px
  lineHeight: 1.1875,
  marginTop: '2rem',

  [theme.breakpoints.up('sm')]: {
    marginBottom: theme.spacing(spacing),
    fontSize: '1.25rem', // 20px
  },

  [theme.breakpoints.up('md')]: {
    fontSize: '1.75rem',
  },

  [theme.breakpoints.up('lg')]: {
    fontSize: '1.875rem',
  },
}));

export const StyledButton = styled(Button)(({theme}) => ({
  width: '100%',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  paddingLeft: '1.75rem',
  paddingRight: '1.75rem',
  fontSize: '1rem',
  lineHeight: 1.375,
  fontWeight: 600,
  textTransform: 'none',
  borderRadius: '0.5rem', // 8px
  backgroundColor: theme.palette.secondary.main,
  color: theme.palette.white.main,
  boxShadow: 'none',
  fontFamily: theme.typography.fontFamily,
  '&:hover': {
    backgroundColor: theme.palette.secondary.main,
    boxShadow: 'none',
  },
  '&:active': {
    backgroundColor: theme.palette.secondary.main,
  },

  [theme.breakpoints.up('sm')]: {
    paddingTop: '1rem',
    paddingBottom: '1rem',
    paddingLeft: '1.75rem',
    paddingRight: '1.75rem',
    fontSize: '0.9375rem', // 15px
  },
}));

export const signInWrapper: SxProps<Theme> = {
  display: 'flex',
  gap: 1,
  flexDirection: 'row',
  justifyContent: 'center',
  marginTop: '2rem',
  alignItems: 'center',
  color: 'body.400',
  fontWeight: 600,
  fontSize: '0.875rem',
};

export const signedInTextStyles: SxProps<Theme> = {
  color: 'body.400',
  fontWeight: 600,
  fontSize: '0.875rem',
};

export const signInTextStyles: SxProps<Theme> = {
  color: theme => theme.palette.secondary.main,
};

export const emailTextStyles: SxProps<Theme> = {
  fontWeight: 400,
  fontSize: ' 1.125rem',
  color: theme => theme.palette.secondary.main,
  textDecoration: 'underline',
};
