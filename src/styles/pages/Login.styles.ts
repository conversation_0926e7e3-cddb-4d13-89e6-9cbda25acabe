import {Box, Card, Link, styled, Typography} from '@mui/material';
import {SxProps} from '@mui/system';
import {MagicNumbers} from 'constant';
import {Integers} from 'Helpers/integers';
import DistekLogo from '../../Assets/DistekLogo';
import EmailIcon from '../../Assets/EmailIcon';
import LockIconAlt from '../../Assets/LockIcon';
import Button from '../../Components/Button';
import FormInput from '../../Components/Forms/FormInput';
import FormPasswordInput from '../../Components/Forms/FormPasswordInput';

// Main container for the login page
export const LoginContainer = styled(Box)(({theme}) => ({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  background: 'linear-gradient(111deg, #F0F0FB 3.63%, #FBF1F6 66.98%, #FFE9EE 93.92%)',
  padding: theme.spacing(2),
}));

export const inputStyles = {
  '.MuiOutlinedInput-input': {
    padding: '0.875rem 0',
    fontSize: '1rem', // 16px
    fontWeight: 400,
    lineHeight: 1.25,
  },
};

export const inputErrorStyles = {
  color: '#A91417 !important',
  fontSize: '0.75rem',
  fontWeight: MagicNumbers.SIX_HUNDRED,
  marginTop: '0.25rem',
  marginLeft: 0,
};

// Login card with responsive design
export const LoginCard = styled(Card)(({theme}) => ({
  width: '100%',
  maxWidth: '32.375rem',
  minWidth: '20rem',
  padding: '1rem', // Temporarily disable responsive padding
  borderRadius: '1.35rem',
  backgroundColor: theme.palette.white.main,
  overflow: 'visible',

  [theme.breakpoints.up('sm')]: {
    maxWidth: '31.25rem', // 500px
    minWidth: '28.125rem', // 450px
    padding: theme.spacing(4),
  },

  [theme.breakpoints.up('md')]: {
    maxWidth: '34.375rem', // 550px
    padding: theme.spacing(MagicNumbers.FIVE),
  },
}));

// Header section container
export const HeaderContainer = styled(Box)(({theme}) => ({
  textAlign: 'center',
  marginBottom: theme.spacing(4),

  [theme.breakpoints.up('sm')]: {
    marginBottom: theme.spacing(MagicNumbers.FIVE),
  },
}));

// Logo container
export const LogoContainer = styled(Box)(() => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
}));

// Styled logo with responsive sizing
export const StyledDistekLogo = styled(DistekLogo)(({theme}) => ({
  width: '1rem', // 16px - very small for login
  maxWidth: '100%',
  maxHeight: '1rem', // 16px max height
  height: 'auto',
  marginBottom: theme.spacing(2),

  [theme.breakpoints.up('sm')]: {
    width: '1.25rem', // 20px
    maxHeight: '1.25rem', // 20px max height
    marginBottom: theme.spacing(MagicNumbers.TWO_POINT_FIVE),
  },

  [theme.breakpoints.up('md')]: {
    width: '1.5rem', // 24px
    maxHeight: '1.5rem', // 24px max height
    marginBottom: theme.spacing(MagicNumbers.THREE),
  },
}));

// Welcome title with responsive typography
export const WelcomeTitle = styled(Typography)(({theme}) => ({
  fontFamily: theme.typography.fontFamily,
  fontWeight: 700,
  color: theme.palette.body.dark,
  marginBottom: theme.spacing(0.5),
  fontSize: '2rem', // 28px
  lineHeight: 1.1875,
  marginTop: '2rem',

  [theme.breakpoints.up('sm')]: {
    marginBottom: theme.spacing(MagicNumbers.ZERO_POINT_SEVEN_FIVE),
    fontSize: '2rem', // 32px
  },

  [theme.breakpoints.up('md')]: {
    fontSize: '2.25rem', // 36px
  },
}));

// Subtitle with responsive sizing
export const SubTitle = styled(Typography)(({theme}) => ({
  fontFamily: theme.typography.fontFamily,
  color: theme.palette.body[Integers.SixHundred],
  fontSize: '1.25rem',
  lineHeight: 1.45,
  fontWeight: 400,
  marginBottom: theme.spacing(1),

  [theme.breakpoints.up('sm')]: {
    fontSize: '1.25rem',
    marginBottom: theme.spacing(MagicNumbers.ONE_POINT_FIVE),
  },

  [theme.breakpoints.up('md')]: {
    fontSize: '1.25rem',
  },
}));

// Form field container
export const FormFieldContainer = styled(Box)(({theme}) => ({
  marginBottom: theme.spacing(2),

  [theme.breakpoints.up('sm')]: {
    marginBottom: theme.spacing(2),
  },
}));

// Field label styling
export const FieldLabel = styled(Typography)(({theme}) => ({
  fontFamily: theme.typography.fontFamily,
  color: theme.palette.body[Integers.FiveHundred],
  fontWeight: MagicNumbers.SIX_HUNDRED,
  marginBottom: 5,
  marginLeft: 2,
  fontSize: '0.875rem', // 14px

  [theme.breakpoints.up('sm')]: {
    marginBottom: 5,
    fontSize: '0.9375rem', // 15px
  },
}));

// Icon divider for input adornments
export const IconDivider = styled(Box)(({theme}) => ({
  width: '0.095rem',
  height: '0.9rem',
  backgroundColor: theme.palette.body[Integers.OneHundred],
  margin: '0 0.75rem',
}));

// Styled form input with consistent theming
export const StyledFormInput = styled(FormInput)(({theme}) => ({
  borderRadius: '0.25rem',
  '& .MuiOutlinedInput-root': {
    backgroundColor: theme.palette.white.main,
    height: '2.625rem', // 42px

    '& .MuiInputAdornment-root.MuiInputAdornment-positionStart': {
      margin: '0 !important',
    },

    [theme.breakpoints.up('sm')]: {
      height: '2.8125rem', // 45px
    },

    '& fieldset': {
      borderColor: theme.palette.body[Integers.OneHundred],
    },
    '&:hover fieldset': {
      borderColor: theme.palette.action.hover,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.body[Integers.OneHundred],
      borderWidth: '0.0625rem',
    },
    '& input': {
      fontFamily: theme.typography.fontFamily,
      fontSize: '0.875rem',

      [theme.breakpoints.up('sm')]: {
        fontSize: '0.9375rem',
      },
    },
    '& input::placeholder': {
      color: theme.palette.text.disabled,
      opacity: 1,
      fontFamily: theme.typography.fontFamily,
    },
  },
}));

// Styled password input with consistent theming
export const StyledFormPasswordInput = styled(FormPasswordInput)(({theme}) => ({
  borderRadius: '0.25rem',
  '& .MuiOutlinedInput-root': {
    backgroundColor: theme.palette.white.main,
    height: '2.625rem',

    '& .MuiInputAdornment-root.MuiInputAdornment-positionStart': {
      margin: '0 !important',
    },

    [theme.breakpoints.up('sm')]: {
      height: '2.8125rem',
    },

    '& fieldset': {
      borderColor: theme.palette.body[Integers.OneHundred],
    },
    '&:hover fieldset': {
      borderColor: theme.palette.action.hover,
    },
    '&.Mui-focused fieldset': {
      borderColor: theme.palette.body[Integers.OneHundred],
      borderWidth: '0.0625rem',
    },
    '& input': {
      fontFamily: theme.typography.fontFamily,
      fontSize: '0.875rem',

      [theme.breakpoints.up('sm')]: {
        fontSize: '0.9375rem',
      },
    },
    '& input::placeholder': {
      color: theme.palette.body[Integers.ThreeHundred],
      opacity: 1,
      fontFamily: theme.typography.fontFamily,
    },
    '& .MuiIconButton-root': {
      color: theme.palette.text.disabled,
      padding: '0.375rem', // 6px
      fontSize: '1.125rem', // 18px
      '&:hover': {
        backgroundColor: theme.palette.action.hover,
        color: theme.palette.body[Integers.FiveHundred],
      },
    },
    '& .MuiSvgIcon-root': {
      fontSize: '1.125rem', // 18px
    },
  },
}));

// Styled email icon
export const StyledEmailIcon = styled(EmailIcon)(({theme}) => ({
  color: theme.palette.body[Integers.FiveHundred],
  fontSize: '1.125rem', // 18px
  marginLeft: '0.5rem', // 8px

  [theme.breakpoints.up('sm')]: {
    fontSize: '1.25rem', // 20px
  },
}));

// Styled lock icon
export const StyledLockIcon = styled(LockIconAlt)(({theme}) => ({
  color: theme.palette.body[Integers.FiveHundred],
  fontSize: '1.125rem', // 18px
  marginLeft: '0.5rem', // 8px

  [theme.breakpoints.up('sm')]: {
    fontSize: '1.25rem', // 20px
  },
}));

// Forgot password container
export const ForgotPasswordContainer = styled(Box)(({theme}) => ({
  textAlign: 'right',
  marginBottom: theme.spacing(MagicNumbers.TWO_POINT_FIVE),

  [theme.breakpoints.up('sm')]: {
    marginBottom: theme.spacing(MagicNumbers.THREE),
  },
}));

// Forgot password link
export const ForgotPasswordLink = styled(Link)(({theme}) => ({
  fontFamily: theme.typography.fontFamily,
  color: theme.palette.body[Integers.SevenHundred],
  textDecoration: 'underline',
  fontSize: '0.875rem', // 14px
  fontWeight: 400,
  '&:hover': {
    cursor: 'pointer',
    // textDecoration: 'underline',
    // color: theme.palette.primary.main,
  },

  [theme.breakpoints.up('sm')]: {
    fontSize: '0.9375rem', // 15px
  },
}));

// Styled submit button
export const StyledButton = styled(Button)(({theme}) => ({
  width: '100%',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  paddingLeft: '1.75rem',
  paddingRight: '1.75rem',
  fontSize: '1rem',
  lineHeight: 1.375,
  fontWeight: MagicNumbers.SIX_HUNDRED,
  textTransform: 'none',
  borderRadius: '0.5rem', // 8px
  backgroundColor: theme.palette.secondary.main,
  color: theme.palette.white.main,
  boxShadow: 'none',
  fontFamily: theme.typography.fontFamily,
  '&:hover': {
    backgroundColor: theme.palette.secondary.main,
    boxShadow: 'none',
  },
  '&:active': {
    backgroundColor: theme.palette.secondary.main,
  },

  [theme.breakpoints.up('sm')]: {
    paddingTop: '1rem',
    paddingBottom: '1rem',
    paddingLeft: '1.75rem',
    paddingRight: '1.75rem',
    fontSize: '0.9375rem', // 15px
  },
}));

const onBGErrorColor = 'alert.error.onBg';

export const errorStyles = (loginError: string | null): SxProps =>
  loginError
    ? {
        height: MagicNumbers.FORTY_SEVEN,
        visibility: 'visible',
        transition: 'all 0.5s ease',
        color: onBGErrorColor,
        marginBottom: '1.5rem',
        fontWeight: MagicNumbers.SIX_HUNDRED,
        fontSize: '0.875rem',
        alignItems: 'center',
        display: 'flex',
        border: '0.0625rem solid #E8ABAC',
        borderRadius: '0.25rem',
        background: '#FFE0E0',
        padding: '0.75rem 0.625rem',
      }
    : {
        height: 0,
        visibility: 'hidden',
        transition: 'all 0.5s ease',
        color: onBGErrorColor,
      };

export const errorIconStyles = {
  color: onBGErrorColor,
  fontSize: '1rem',
};
