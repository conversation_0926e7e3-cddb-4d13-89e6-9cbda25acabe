import {Box, styled, SxProps, Theme} from '@mui/material';
import {Integers} from 'Helpers/integers';

export const passwordValidationStyles: SxProps<Theme> = {
  background: theme => theme.palette.alert.error.bg,
  border: theme => `0.0625rem solid ${theme.palette.alert.error.border}`,
  borderRadius: '0.25rem',
  padding: '0.625rem 0.875rem',
  marginBottom: '1rem',
};

export const typographyStyles: SxProps<Theme> = {
  color: 'body.700',
  fontSize: '0.75rem',
  fontWeight: 600,
  lineHeight: '1rem',
};

export const listItemTypographyStyles: SxProps<Theme> = {
  color: theme => theme.palette.alert.error.onBg,
  fontSize: '0.9375rem',
  fontWeight: 500,
  lineHeight: '1rem',
};

export const listItemCheckedTypographyStyles: SxProps<Theme> = {
  ...listItemTypographyStyles,
  color: theme => theme.palette.alert.success.onBg,
};

export const listItemStyles = {
  paddingBottom: 0.5,
  paddingLeft: 0,
  '&:last-child': {
    paddingBottom: 0,
  },
};

interface BackgroundColorProp {
  backgroundColor?: string;
}

export const KeyIconContainer = styled(Box, {
  shouldForwardProp: prop => prop !== 'backgroundColor',
})<BackgroundColorProp>(({theme, backgroundColor}) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  width: '5rem',
  height: '5rem',
  borderRadius: '50%',
  border: `0.625rem solid ${theme.palette.white[Integers.TwoHundred]}`,
  padding: '1rem',
  backgroundColor: backgroundColor || theme.palette.white[Integers.TwoHundred],
}));

export const listItemContainerStyles = {mr: 1, minWidth: 'auto'};

export const iconStyles: SxProps<Theme> = {width: 20, height: 20, color: theme => theme.palette.white.main};
