import {SxProps, Theme} from '@mui/material';
import {Integers} from 'Helpers/integers';

const whiteMain = 'white.main';

export const headerBoxStyle: SxProps = {
  pb: 1,
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
};

export const leftHeaderStyle = {
  fontWeight: 700,
  color: 'body.dark',
};

export const buttonStyle = {
  backgroundColor: 'secondary.main',
  color: whiteMain,
  '&:hover': {backgroundColor: 'secondary.650'},
  textTransform: 'none',
  fontWeight: 700,
  fontSize: '0.8125rem',
  borderRadius: '0.375rem',
};

export const tableHeadProps = {
  backgroundColor: 'table.header',
  borderColor: 'body.100',
  color: 'body.900',
};

export const coloumnCellProps = {
  fontWeight: 700,
  fontSize: '0.8125rem',
  color: 'body.900',
};

export const tableContainerProps: SxProps<Theme> = {
  border: theme => '0.0625rem solid ' + theme.palette.body[Integers.OneHundred],
};

export const bodyCellProps: SxProps<Theme> = {
  color: 'body.800',
  fontWeight: 500,
  fontSize: '0.8125rem',
  borderBottom: theme => '0.125rem solid ' + theme.palette.body[Integers.Fifty],
};

export const actionStyles: SxProps<Theme> = {
  color: 'body.500',
  fontSize: '1.25rem',
  fill: theme => theme.palette.white.main,
  mr: 1.5,
  cursor: 'pointer',
};

export const toolTipStyles: {tooltip: {sx: SxProps<Theme>}; arrow: {sx: SxProps<Theme>}} = {
  tooltip: {
    sx: {
      backgroundColor: whiteMain,
      borderWidth: '0.0625rem',
      borderStyle: 'solid',
      borderColor: 'divider',
      color: 'body.500',
      fontSize: '0.875rem',
    },
  },
  arrow: {
    sx: {
      color: whiteMain,
      '&:before': {
        borderWidth: '0.0625rem',
        borderStyle: 'solid',
        borderColor: 'divider',
      },
    },
  },
};
